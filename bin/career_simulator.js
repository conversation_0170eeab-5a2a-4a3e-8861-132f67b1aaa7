/**
 * node bin/career_simulator.js card=4 round=10 ticket=1000
 */

const child_process = require('child_process');
const fs = require('fs');
const path = require('path');
const projectRoot = path.join(__dirname, '..');
const target = '/var/www/html/static/';

const args = {};
process.argv.forEach((kv) => {
  const [key, value] = kv.split('=');
  if (value) {
    args[key] = value;
  }
});

const argString = Object.keys(args).map(key => `${key}=${args[key]}`).join(' ');
const command = `npm run simulate-career -- ${argString}`;
console.log(command);
child_process.execSync(command, {stdio: 'inherit', cwd: projectRoot});

let fileName = null;
if (fs.existsSync(target)) {
  const timestamp = Date.now();
  fileName = `player_career_${timestamp}.xlsx`;
  fs.copyFileSync(path.join(projectRoot, 'player_career.xlsx'), path.join(target, fileName));
}

if (fileName) {
  const downloadLink = encodeURI(`http://10.10.10.50/static/${fileName}`);
  console.log(`\x1b[44m DOWNLOAD LINK \x1b[0m \n${downloadLink}`);
}

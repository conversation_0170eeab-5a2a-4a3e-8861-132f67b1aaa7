const _ = require('lodash');

const aws_region = process.env.AWS_REGION || 'localhost';
const isLambdaEnv = aws_region !== 'localhost';

const table_ttl_gsi_collection = 'list';
const table_ttl = 'foo-ttl';
// const table_ttl = 'playwpt-ttl';
const table_poem = 'foo-poem';
const dynamoDB_ProvisionedThroughput = {
  'ReadCapacityUnits': 5,
  'WriteCapacityUnits': 5,
};

class FooError extends Error {
  constructor(data, msg) {
    super();
    this.code = _.get(data, 'code', error_unknown.code);

    if (data) {
      if (data.msg) {
        this.message = data.msg;
      }

      //兼容dynamoDB的eror message
      if (data.message) {
        this.message = data.message;
      }
    }

    //优先使用自定义的msg
    msg = _.toString(msg);
    if (msg) {
      this.message = msg;
    }

    //没有任何匹配，使用默认
    this.message = this.message || error_unknown.msg;

  }
}

class WptError2 extends Error {
  constructor(data) {
    super();
    data = data || {};
    // 如果是错误本身，直接赋值
    if (data instanceof Error) {
      _.assign(this, data);
    } else {
      // 提高错误的层级，防止嵌套e的存在，并删除原始error对象后再赋值
      // 不需要判定data.e是否是Error的实例
      if (data.e) {
        _.assign(this, data.e);
        delete data.e;
      }
      _.assign(this, data);
    }
  }
}

class WptError extends Error {
  constructor(data, msg) {
    super();
    this.code = _.get(data, 'code', error_internal.code);

    if (data) {
      if (data.msg) {
        this.message = data.msg;
      }

      //兼容dynamoDB的eror message
      if (data.message) {
        this.message = data.message;
      }
    }

    //优先使用自定义的msg
    msg = _.toString(msg);
    if (msg) {
      this.message = msg;
    }

    //没有任何匹配，使用默认
    this.message = this.message || c.error_unknown.msg;

  }
}

const tableScript_ttl = {
  TableName: table_ttl,
  KeySchema: [
    {AttributeName: 'ttlId', KeyType: 'HASH'},
  ],
  AttributeDefinitions: [
    {AttributeName: 'ttlId', AttributeType: 'S'},
    {AttributeName: 'info', AttributeType: 'S'},
  ],
  GlobalSecondaryIndexes: [
    {
      IndexName: 'info',
      KeySchema: [
        {AttributeName: 'info', KeyType: 'HASH'},
      ],
      Projection: {
        'ProjectionType': 'ALL',
      },
      ProvisionedThroughput: dynamoDB_ProvisionedThroughput,
    },
  ],
  ProvisionedThroughput: dynamoDB_ProvisionedThroughput,
};

const tableScript_poem = {
  TableName: table_poem,
  KeySchema: [
    {AttributeName: 'poemId', KeyType: 'HASH'},
  ],
  AttributeDefinitions: [
    {AttributeName: 'poemId', AttributeType: 'S'},
    {AttributeName: 'hInfo', AttributeType: 'S'},
    {AttributeName: 'rInfo', AttributeType: 'S'},
  ],
  GlobalSecondaryIndexes: [
    {
      IndexName: 'poemIndex',
      KeySchema: [
        {AttributeName: 'hInfo', KeyType: 'HASH'},
        {AttributeName: 'rInfo', KeyType: 'range'},
      ],
      Projection: {
        'ProjectionType': 'KEYS_ONLY',
      },
      ProvisionedThroughput: dynamoDB_ProvisionedThroughput,
    },
  ],
  ProvisionedThroughput: dynamoDB_ProvisionedThroughput,
};

const no = 0;//通用否定字段，与bool的否定对应
const yes = 1;//通用肯定字段，与bool的肯定对应
const time_ms_second = 1000;//second毫秒数
const time_ms_day = 24 * 60 * 60 * 1000;//一天的毫秒数
const time_ms_hour = 60 * 60 * 1000;//hour毫秒数
const time_ms_minute = 60 * 1000;//minute毫秒数

const crawler_test_ttlId = 'V2SxU298';

const poemHub_panghuang = {
  ttlId: 'VyiXyV4J',
  url: 'http://www.my2852.com/xdmj/luxun/panghuang/index.htm',
};
const poemHub_yecao = {
  ttlId: 'FQ4BsEQl',
  url: 'http://www.my2852.com/xdmj/luxun/yecao/index.htm',
};
const poemHub_nahan = {
  ttlId: 'V2SxU298',
  url: 'http://www.my2852.com/xdmj/luxun/nahan/index.htm',
};
const poemHub_zhaohuaxishi = {
  ttlId: 'CNNqIXT0',
  url: 'http://www.my2852.com/xdmj/luxun/zhxs/index.htm',
};

//pre：
//鲁迅,余光中,朱自清，胡适，废名,梁实秋
//

const error_invalidRequest = {code: -98, msg: 'invalid request.'};
const error_internal = {code: -99, msg: 'internal error.'};//服务器内部致命错误
const error_unknown = {code: -100, msg: 'unknown sys error.'};
const error_awsSdkSupport = {code: -101, msg: 'aws-sdk do not support.'};
const error_dd_poemData = {code: -102, msg: 'dd poem data error.'};
const error_params = {code: -97, msg: 'error_params'};

const test_poemHub_list_ttlId = 'V2SxU298';
const test_poemHub_detail_ttlId = 'QtKL-pUO5k';
const format_year_to_second = 'YYYY-MM-DD HH:mm:ss';

const record_server_warn = 'server_warn';// 服务器警告日志

const error_server_busy = {code: 9010, msg: 'system busy,retry later.'};// 系统繁忙，稍后重试

const dd_tableNameSplitter = '-';// dynamoDB表通用分隔名字符号
const dd_limit = 100;// dynamoDB通用分页数
const dd_limit_batchWrite = 25;// BatchWriteItem的最大请求数
const dd_limit_tranWriteSize = 100;// TransactWriteItems的最大请求数
const dd_batchWrite = 'batchWrite';
const dd_get = 'get';
const dd_put = 'put';
const dd_update = 'update';
const dd_batchGet = 'batchGet';
const dd_transactWrite = 'transactWrite';
const dd_delete = 'delete';
const dd_query = 'query';
const dd_scan = 'scan';

function check(condition, errorInfo, logObj) {
  if (condition) {
    return;
  }
  errorInfo = errorInfo || error_internal;
  errorInfo.code = errorInfo.code || error_internal.code;
  errorInfo.msg = errorInfo.msg || error_internal.msg;
  if (logObj && logObj.player) {
    logObj.playerId = logObj.player.playerId;
    delete logObj.player;
  }
  _.assign(errorInfo, logObj);
  throw new WptError(errorInfo);
}

//牌型
const rank_absent = -2;//玩家不存在，以最低的rank处理
const rank_default = -1;//玩家只有holeCards
const rank_highCard = 0;
const rank_onePair = 1;
const rank_twoPair = 2;
const rank_threeOfAKind = 3;
const rank_straight = 4;
const rank_flush = 5;
const rank_fullHouse = 6;
const rank_fourOfAKind = 7;
const rank_straightFlush = 8;
const rank_royalFlush = 9;

function get_themeTask_options() {
  let resData = {};
  resData.enable_themeTask = true;//是否开启主题任务
  resData.funcVer = '-8';//功能版号，仅用于开发和重大异常更新的线上

  //主任务类型，Start
  resData.taskType_playHands = 1;//玩了多少局
  resData.taskType_winHands = 2;//赢了多少局
  resData.taskType_winSuit = 3;//从赢的局里面收集对应配置的花色，比如要求收集10个，赢时，成牌里面有2张️，任务进度+2
  resData.taskType_winHands_beforeShowdown = 4;//不比牌赢
  resData.taskType_allIn = 5;//allIn
  resData.taskType_winChips = 6;//赢取筹码
  resData.taskType_winCardsRank = 7;//以大于等于配置的牌型来赢取比赛，cardsRank
  //主任务类型，End

  //任务状态
  resData.task_status_on = 0;//进行中
  resData.task_status_failed = 1;//失败
  resData.task_status_over = 2;//成功结束

  //附加任务类型，Start
  resData.taskType_addType_empty = 0;//无附加任务类型
  resData.taskType_addType_blinds = 1;//身上总筹码*系数>=大盲
  resData.taskType_addType_handsLeft = 2;//在限定局数内完成
  resData.taskType_addType_timeLeft = 3;//在时限内(ms)
  //附加任务类型，End

  //客户端需要的牌型和配置牌型的映射
  resData.cardsRankObj = {
    'twoPairs': rank_twoPair,
    'threeOfAKind': rank_threeOfAKind,
    'straight': rank_straight,
    'flush': rank_flush,
    'fullHouse': rank_fullHouse,
  };

  //客户端需要的花色和配置的映射
  resData.suitObj = {
    'Diamonds': 0, 'Spades': 16, 'Hearts': 32, 'Clubs': 48,
  };

  resData.min_task_winChips = 10000;//生成赢取筹码任务的最低筹码阈值

  return resData;
}

const cfg_CFGPlots = 'CFGPlots';//地图
const cfg_CFGTasks = 'CFGTasks';//任务

//TAG 新增方法的TAG点，请勿删除改行，在此行以上进行添加
module.exports = {
  isLambdaEnv: isLambdaEnv,
  cfg_CFGTasks: cfg_CFGTasks,
  cfg_CFGPlots: cfg_CFGPlots,
  get_themeTask_options: get_themeTask_options,
  dd_tableNameSplitter: dd_tableNameSplitter,
  dd_scan: dd_scan,
  dd_query: dd_query,
  dd_delete: dd_delete,
  dd_transactWrite: dd_transactWrite,
  dd_batchGet: dd_batchGet,
  dd_update: dd_update,
  dd_put: dd_put,
  dd_get: dd_get,
  dd_batchWrite: dd_batchWrite,
  dd_limit: dd_limit,
  dd_limit_batchWrite: dd_limit_batchWrite,
  dd_limit_tranWriteSize: dd_limit_tranWriteSize,
  check: check,
  error_server_busy: error_server_busy,
  record_server_warn: record_server_warn,
  format_year_to_second: format_year_to_second,
  test_poemHub_detail_ttlId: test_poemHub_detail_ttlId,
  test_poemHub_list_ttlId: test_poemHub_list_ttlId,
  error_params: error_params,
  error_invalidRequest: error_invalidRequest,
  error_internal: error_internal,
  error_unknown: error_unknown,
  error_dd_poemData: error_dd_poemData,
  error_awsSdkSupport: error_awsSdkSupport,
  table_ttl_gsi_collection: table_ttl_gsi_collection,
  poemHub_panghuang: poemHub_panghuang,
  poemHub_nahan: poemHub_nahan,
  poemHub_yecao: poemHub_yecao,
  crawler_test_ttlId: crawler_test_ttlId,
  no: no,
  yes: yes,
  tableScript_poem: tableScript_poem,
  tableScript_ttl: tableScript_ttl,
  table_poem: table_poem,
  table_ttl: table_ttl,
  isLambdaEnv: isLambdaEnv,
  FooError: FooError,
  time_ms_second: time_ms_second,
  time_ms_day: time_ms_day,
  time_ms_hour: time_ms_hour,
  time_ms_minute: time_ms_minute,
  WptError: WptError,
  WptError2: WptError2,
};




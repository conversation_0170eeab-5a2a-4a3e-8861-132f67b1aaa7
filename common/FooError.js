const c = require('./FooConstants');

class WptError2 extends Error {
  constructor(data) {
    super();
    data = data || {};
    // 如果是错误本身，直接赋值
    if (data instanceof Error) {
      _.assign(this, data);
    } else {
      // 提高错误的层级，防止嵌套e的存在，并删除原始error对象后再赋值
      // 不需要判定data.e是否是Error的实例
      if (data.e) {
        _.assign(this, data.e);
        delete data.e;
      }
      _.assign(this, data);
    }
  }
}

class WptError extends Error {
  constructor(data, msg) {
    super();
    this.code = _.get(data, 'code', c.error_unknown.code);

    if (data) {
      if (data.msg) {
        this.message = data.msg;
      }

      //兼容dynamoDB的eror message
      if (data.message) {
        this.message = data.message;
      }
    }

    //优先使用自定义的msg
    msg = _.toString(msg);
    if (msg) {
      this.message = msg;
    }

    //没有任何匹配，使用默认
    this.message = this.message || c.error_unknown.msg;

  }
}

module.exports = {
  fooError: WptError,
  WptError: WptError,
  WptError2: WptError2,
};
const winston = require('winston');
const _ = require("lodash");
const c = require("./FooConstants");

//lambda环境默认日志等级为info，除非模块有特殊设置
const env_lambdaLogLevel = process.env.lambdaLogLevel;
const lambdaLogLevel = c.isLambdaEnv
    //lambda环境默认等级info
    ? (_.includes(["debug", "warn", "error", "info"], env_lambdaLogLevel) ? env_lambdaLogLevel : "info")
    //测试环境默认等级debug
    : env_lambdaLogLevel ? env_lambdaLogLevel : "debug";


const winstonInfoFormat = winston.format(info => {
    if (info.e && info.e instanceof Error) {

        if (info.game) {
            info.routeId = _.get(info.game, "routeId");
        }

        if (!c.isLambdaEnv) {
            console.error("error(no lambda env).\n", info.e);
        }
        info.error = _.pick(info.e, ["name", "time", "code", "time", "message", "msg"]);
        //stack的截取需要按实际日志进行控制
        info.error.stack = _.truncate(info.e.stack, {length: 230});
    }
    delete info.e;
    return info;
});

//初始化logger，根据lambda和测试环境，初始化不同的logger
const winstonLogger = c.isLambdaEnv ? winston.createLogger({
    level: lambdaLogLevel,
    format: winston.format.combine(winstonInfoFormat(), winston.format.json()),
    transports: [
        new winston.transports.Console()
    ],
    exitOnError: true,
}) : winston.createLogger({
    level: lambdaLogLevel,
    format: winston.format.combine(winstonInfoFormat(), winston.format.colorize(), winston.format.simple()),
    // format: winston.format.combine(winston.format.timestamp(), winstonInfoFormat(), winston.format.colorize(), winston.format.simple()),
    transports: [
        new winston.transports.Console()
    ],
    exitOnError: true,
});

const prettyLogger = c.isLambdaEnv ? winston.createLogger({
    level: lambdaLogLevel,
    format: winston.format.combine(winstonInfoFormat(), winston.format.json()),
    transports: [
        new winston.transports.Console()
    ],
    exitOnError: true,
}) : winston.createLogger({
    level: lambdaLogLevel,
    format: winston.format.combine(
        winstonInfoFormat(),
        winston.format.colorize(),
        winston.format.simple(),
        winston.format.prettyPrint()),
    transports: [
        new winston.transports.Console()
    ],
    exitOnError: true,
});

const simpleLogger = winston.createLogger({
    format: winston.format.printf(({level, message, label, timestamp}) => {
        return `${message}`;
    }),
    level: lambdaLogLevel,
    transports: [
        new winston.transports.Console()
    ]
});

module.exports = {
    logger: winstonLogger,
    prettyLogger: prettyLogger,
    simpleLogger: simpleLogger,
};
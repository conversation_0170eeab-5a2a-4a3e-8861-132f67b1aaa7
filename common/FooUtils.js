const _ = require('lodash');
const coreFs = require('fs');
const corePath = require('path');
const c = require('./FooConstants');
const logger = require('./FooLogger').logger;
const dayjs = require('dayjs');

let crypto;
let nanoid;
let default_shortid;
let axios;
let iconv;
let xlsx;

function now() {
  return _.now();
}

/**
 * 记录错误信息
 * @param {Object} [errorObj] 错误对象
 * @param {Object}[errorObj.e={}] node的异常对象exception对象，必须以e的属性进行传递
 * @param {Object}[errorObj.event={}] lambda或者express请求的event对象
 * @param {Object}[errorObj.context={}] lambda请求的context对象
 * @param {Object}[errorObj.param={}] Dynamodb的执行参数
 * @param {String}[errorObj.tag] 唯一标签，用于错误抛出点记录
 * @param {Number}[errorObj.code] 错误码，会覆写e里面的错误码
 * @param {String}[errorObj.record] 如果传了record，日志会被记录为指定的record，而不是server_error
 */
async function logError(errorObj = {}) {
  try {
    logger.error('logErrorCp1', {...errorObj});
    // await toFailJson2(errorObj);
  } catch (e) {
    logger.error('logErrorCp1', {...errorObj});
  }
}

function osHome() {
  return require('os').homedir();
}

/**
 * 生成仅包括alphanumeric的随机数，21位
 * @param size
 * @return {string}
 */
function uid(size = 21) {
  nanoid = nanoid || require('nanoid');
  default_shortid = default_shortid || nanoid.customAlphabet(
      '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', 21);
  if (size === 21) {
    return default_shortid();
  }
  /**
   * numbers:1234567890
   * hexadecimalLowercase:0123456789abcdef
   * hexadecimalUppercase:0123456789ABCDEF
   * lowercase:abcdefghijklmnopqrstuvwxyz
   * uppercase:ABCDEFGHIJKLMNOPQRSTUVWXYZ
   * alphanumeric:1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ
   * nolookalikes:346789ABCDEFGHJKLMNPQRTUVWXYabcdefghijkmnpqrtwxyz
   * nolookalikesSafe:6789BCDFGHJKLMNPQRTWbcdfghjkmnpqrtwz
   */
  const alphanumeric = '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const customAlphabet = nanoid.customAlphabet(alphanumeric, size);
  return customAlphabet();
}

function getCheerio(option) {
  option = option || {decodeEntities: false};
  return require('cheerio').load('', option);
}

async function readDirs(dir, recursive = false) {
  let dirs = [];
  if (recursive === true) {
    let doReadDirs = function(dir) {
      if (!isDir(dir)) {
        return;
      }
      dirs.push(dir);
      for (let dirName of coreFs.readdirSync(dir)) {
        doReadDirs(corePath.join(dir, dirName));
      }
    };
    for (let dirName of coreFs.readdirSync(dir)) {
      doReadDirs(corePath.join(dir, dirName));
    }
    return dirs;
  }
  // let dirs = [];
  for (let dirName of coreFs.readdirSync(dir)) {
    let tmpDir = corePath.join(dir, dirName);
    if (isDir(tmpDir)) {
      dirs.push(tmpDir);
    }
  }
  return dirs;
}

function getReqDataOnEvent(event) {
  if (!event) {
    throw new c.FooError(c.error_invalidRequest);
  }
  //测试环境的body已经是json，直接使用即可
  let body = event.body;
  //lambda环境因为启用了proxy代理，所以需要再次分析body
  if (c.isLambdaEnv) {
    body = parseJson(event.body);
  }
  if (!body) {
    throw new c.FooError(c.error_invalidRequest);
  }
  return body;
}

function rmdirSync(path) {
  if (!isDir(path)) {
    return;
  }
  coreFs.rmdirSync(path, {recursive: true});
  logger.debug('rmdirSyncEnd', {path});
}

function getHrefOrigin(url) {
  return new URL(url).origin;
}

/**
 * join url
 * @param url url母体
 * @param tokens A sequence of path segments，支持字符串、字符串数组
 * @returns {string}
 */
function joinUrl(url, tokens) {
  let iUrl = new URL(url);
  let origin = iUrl.origin;
  let remain = _.replace(_.clone(iUrl.href), origin, '');

  remain = corePath.dirname(remain);

  tokens = _.concat([], tokens);
  for (let token of tokens) {
    remain = corePath.join(remain, token);
  }
  let finalPath = origin + remain;
  // logger.info("", {remain, origin, finalPath});
  return finalPath;
}

function mkdirSync(path) {
  if (isDir(path)) {
    return;
  }
  coreFs.mkdirSync(path);
  logger.info('mkdirSyncEnd', {path});
}

//render engine: mustache
async function render2File(srcFile, desFile, tmplData) {
  let fileData = await readFile(srcFile);
  let data = renderWithMustache(fileData, tmplData);
  writeFileSync(desFile, data);
  logger.debug('render2File', {from: srcFile, to: desFile});
}

function renderWithMustache(content, view) {
  return require('mustache').render(content, view);
}

async function readFile(file, config) {
  iconv = iconv || require('iconv-lite');
  return new Promise((resolve, reject) => {
    coreFs.readFile(file, function(err, data) {
      if (err) {
        reject(err);
      }
      let encoding = 'utf-8';
      if (config && config.decoding) {
        encoding = config.decoding;
      }
      data = iconv.decode(data, encoding);
      resolve(data);
    });
  });
}

function toBase64(str) {
  return Buffer.from(str).toString('base64');
}

function fromBase64(str) {
  return Buffer.from(str, 'base64').toString();
}

function getDeleteModelParam(table, model, conUpdate = false) {
  let key = getDDKey(table, model);
  let deleteParam = {
    TableName: table,
    Key: key,
  };
  if (conUpdate) {
    let attrValues = {};
    attrValues[':conToken'] = _.get(model, 'conToken', generateShortId());
    deleteParam.ExpressionAttributeValues = attrValues;
    deleteParam.ConditionExpression = 'attribute_not_exists(conToken) or conToken=:conToken';
  }
  return deleteParam;
}

function push(array, ele) {
  array = array || [];
  if (!array.includes(ele)) {
    array.push(ele);
  }
}

function exeSysCmdSpawn(cmd) {
  logger.info('exe cmd start.', {cmd});
  let data = '';
  const exec_spawn = require('child_process').spawn;
  return new Promise((resolve, reject) => {
    // const spawn = require('child_process').spawn;
    // console.log(`executeWindowsCommand:${cmd}`);
    let iSpawn = exec_spawn(cmd, {shell: true});
    iSpawn.stdout.on('data', (data) => {
      logger.info('stdout', {data: _.toString(data)});
      // console.log(_.toString(data));
    });
    iSpawn.stderr.on('data', (data) => {
      resolve(`error:${data}`);
      reject(`${data}`);
    });
    iSpawn.on('close', (data) => {
      // console.log("close:",data);
      resolve(`close:${data}`);
    });
    iSpawn.on('exit', (data) => {
      // console.log("exit:",data);
      resolve(`exit:${data}`);
    });
  });
}

function exeSysCmdSpawnSync(cmd) {
  cmd = formatCmd(cmd);
  logger.silly('exeSysCmdSpawnSyncStart', {cmd});
  require('child_process').spawnSync(cmd, [], {shell: true});
  logger.silly('exeSysCmdSpawnSyncEnd', {cmd});
}

/**
 * 执行系统命令
 * @param cmd
 * @param option 配置
 * @param {boolean}[option.silent=false] 是否静默
 */
async function exeSysCmd(cmd, option = {}) {
  cmd = formatCmd(cmd);
  const SYS_EXEC = require('child_process').exec;
  if (option?.silent === true) {
    cmd += ' > /dev/null';
  }
  logger.info('exe cmd start.', {cmd});
  let data = '';
  const cmdOption = {maxBuffer: 1024 * 500};
  return new Promise((resolve, reject) => {
    SYS_EXEC(cmd, cmdOption, (error, stdout, stderr) => {
      if (error) {
        return reject(error);
      }
      if (stdout) {
        data += stdout.toString();
      }
      if (stderr) {
        data += stderr.toString();
      }
      return resolve(data);
    });
  });
}

function formatCmd(cmd) {
// 注意，反斜杠、斜杠等一定不能格式化，否则会影响例如更新alias的参数：\$LATEST
// cmd = _.replace(cmd, //g, "");
  cmd = _.replace(cmd, /\n/g, ' ');
  cmd = _.replace(cmd, /\s+/g, ' ');
  return cmd;
}

function formatSql(cmd) {
// 注意，反斜杠、斜杠等一定不能格式化，否则会影响例如更新alias的参数：\$LATEST
// cmd = _.replace(cmd, //g, "");
  cmd = _.replace(cmd, /\n/g, ' ');
  cmd = _.replace(cmd, /\s+/g, ' ');
  return cmd;
}

//标准化crawler的的诗词内容
function n11nPoemText(str) {
  str = _.trim(str);
  str = str.replace(/\r\n/g, '');
  str = str.replace(/\n/g, '');
  str = str.replace(/\s+/g, ' ');
  return str;
}

//获取url的原始数据，用于cheerio分析
async function getUrlData2(url) {
  axios = axios || require('axios');
  let encoding = getEncodingByUrl(url);
  //减少base64后的唯一的文件名长度
  // let iUrl = new URL(url);
  let origin = getHrefOrigin(url);
  let shortUrl = _.replace(_.clone(url), origin, '');

  if (shortUrl.endsWith('/')) {
    shortUrl = shortUrl.substring(0, shortUrl.length - 1);
  }

  let originDir = _.truncate(toBase64(origin), {
    'length': 100,
    'omission': '',
  });

  let fileKeyToken = _.truncate(toBase64(shortUrl), {
    'length': 100,
    'omission': '',
  });
  // fileKeyToken = uid();

  let crawlerHomeDir = `${osHome}/tmp/crawlers/${originDir}`;
  mkdirSync(crawlerHomeDir);
  check(isDir(crawlerHomeDir), c.error_internal, {
    msg: 'crawlerHomeDir not exits.',
    crawlerHomeDir,
  });

  let iFile = corePath.join(crawlerHomeDir, fileKeyToken + '.html');

  let oriData;
  if (isFile(iFile)) {
    oriData = await readFile(iFile, {decoding: encoding});
    // oriData = iconv.decode(oriData, encoding);
    logger.debug('getUrlDataFromCacheEnd', {iFile, url});
  } else {
    //参考：https://stackoverflow.com/questions/40211246/encoding-issue-with-axios
    //如果有request组件，参考这里: https://www.jianshu.com/p/a7c4e7b51d42
    let instance = axios.create();
    let response = await instance.get(url, {
      timeout: 30000,
      responseType: 'arraybuffer',
      responseEncoding: 'binary',
    });
    oriData = iconv.decode(response.data, encoding);
    let encodingData = iconv.encode(oriData, encoding);
    await writeFileSync(iFile, encodingData);
    logger.debug('getUrlDataOnRealTimeEnd', {iFile, url});
    //实时抓取设定间隔
    await sleep(c.time_ms_second);
  }
  return oriData;
}

function getEncodingByUrl(url) {
  if (!url) {
    return 'utf-8';
  }
  let origin = getHrefOrigin(url);
  if (origin.indexOf('www.my2852.com') >= 0) {
    return 'gb2312';
  }
  return 'utf-8';
}

//参考js：Crawler_baidubaike.js
//参考：https://stackoverflow.com/questions/40211246/encoding-issue-with-axios
//如果有request组件，参考这里: https://www.jianshu.com/p/a7c4e7b51d42
async function getUrlData(url, config) {
  axios = axios || require('axios');
  if (!config) {
    config = {
      // headers: {
      //     'Accept-Language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
      // },
      timeout: 30000,
    };
  }
  let result = await axios.get(url, config);
  return result.data;
}

function toFailJson(error, reqData) {

  let msg = _.get(error, 'msg');
  let code = _.get(error, 'code', c.error_internal.code);

  let reqDataAttrs = ['msgId', 'cvt', 'rTime'];
  let data = _.pick(reqData, reqDataAttrs);

  data.sTime = currentDateTime();

  if (!msg) {
    msg = _.get(error, 'message', c.error_internal.msg);
  }

  //覆盖非标准化的异常，隐藏服务器信息
  if (!_.isInteger(code)) {
    code = c.error_internal.code;
    msg = c.error_internal.msg;
  }

  let responseBody = JSON.stringify({
    code,
    msg,
    'data': data,
  });

  // responseBody = encryptUtils.encodeStr(responseBody);

  if (!c.isLambdaEnv) {
    return responseBody;
  }

  let lambdaRes = {
    statusCode: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
  };
  lambdaRes.body = responseBody;
  return lambdaRes;
}

function toSuccessJson(data, reqData) {
  data = data || {};
  let reqDataAttrs = ['msgId', 'cvt', 'rTime'];
  let reqSource = _.pick(reqData, reqDataAttrs);
  data = _.assign(data, reqSource);
  data.sTime = currentDateTime();

  let responseBody = JSON.stringify({
    'code': 0,
    'msg': '',
    data,
  });

  // responseBody = encryptUtils.encodeStr(responseBody);

  if (!c.isLambdaEnv) {
    return responseBody;
  }

  let lambdaRes = {
    statusCode: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    // body: JSON.stringify(responseBody)
  };
  lambdaRes.body = responseBody;
  return lambdaRes;
}

function toDateFromYear2Second() {
  return dayjs().format('YYYY-MM-DD HH:mm:ss');
}

function writeFileSync(file, data, options) {
  let myPath = corePath.dirname(file);
  mkdirSync(myPath);
  coreFs.writeFileSync(file, data, options);
}

async function readLines(file) {

  let rl = require('readline').createInterface({
    input: coreFs.createReadStream(file),
    crlfDelay: Infinity,
  });

  return new Promise((resolve, reject) => {
    let resArray = [];
    rl.on('line', (line) => {
      line = _.trim(line);
      if (isNotNull(line)) {
        resArray.push(line);
      }
    }).on('close', () => {
      resolve(resArray);
    });
  });
}

function readFileSync(file, toString = true) {
  if (toString === true) {
    return coreFs.readFileSync(file).toString();
  }
  return coreFs.readFileSync(file);
}

function toNumber(obj) {
  return _.toNumber(obj) || 0;
}

function file2JsonSync(file) {
  if (!isFile(file)) {
    return null;
  }
  return parseJson(coreFs.readFileSync(file));
}

function isFile(path) {
  return coreFs.existsSync(path) && coreFs.statSync(path).isFile();
}

function isDir(path) {
  return coreFs.existsSync(path) && coreFs.statSync(path).isDirectory();
}

function check(condition, data, logMessage, logData) {
  if (condition) {
    return;
  }
  if (logMessage) {
    logger.error(logMessage, logData);
  }
  if (data && _.isString(data)) {
    let myError = {
      code: c.error_internal.code,
      msg: data,
    };
    throw new c.FooError(myError);
  }
  data = data || c.error_internal;
  throw new c.FooError(data, logMessage);
}

/**
 * 整数相加
 * @param a
 * @param b
 * @return {number}
 */
function addInt(a, b) {
  let t = _.toInteger(a) + _.toInteger(b);
  if (t < Number.MIN_SAFE_INTEGER || t > Number.MAX_SAFE_INTEGER) {
    logger.error('addIntError', {a, b});
    return 0;
  }
  return t;
}

/**
 * 数字相加
 * @param a a
 * @param b b
 * @param fixxed 保留小数的精度，默认多一位，即3位进行计算
 * @return {number}
 */
function addNumber(a, b, fixxed = 2) {
  let t = _.add(a, b);
  if (!_.isFinite(t) || t < Number.MIN_SAFE_INTEGER || t >
      Number.MAX_SAFE_INTEGER) {
    logger.error('addNumberError', {a, b, fixxed});
    return 0;
  }
  return _.toNumber(_.round(t, fixxed));
}

async function timesSuccess(asyncFunc, time = 0, times = 3) {
  if (time++ >= times) {
    return;
  }
  try {
    console.log(`retry time as:${time}`);
    await asyncFunc();
  } catch (e) {
    console.error(`retry time as:${e}`);
    console.log(`retry in one second,time:${time}`);
    await sleep(1000);
    await timesSuccess(asyncFunc, time, times);
  }
}

function getTimeYear2Day() {
  return dayjs().format('YYYY-MM-DD');
}

function getCheckParamStr(obj, param) {
  let value = _.toString(_.get(obj, param));
  if (!value) {
    throw new c.FooError(c.error_params, `invalid param:${param}`);
  }
  return _.toString(value);
}

function getParamStr(obj, param) {
  let value = _.get(obj, param);
  return _.toString(value);
}

function getParamInt(obj, param) {
  let value = _.get(obj, param);
  if (!_.isInteger(value)) {
    return null;
  }
  return _.toInteger(value);
}

function getCheckParamInt(obj, param) {
  let value = _.get(obj, param);
  if (!_.isInteger(value)) {
    throw new c.FooError(c.error_params, `invalid param:${param}`);
  }
  return _.toInteger(value);
}

function safeGetInt(obj, attr) {
  if (_.isUndefined(obj) || _.isUndefined(obj[attr])) {
    return null;
  }
  if (_.isInteger(obj[attr])) {
    return obj[attr];
  }
  return null;
}

/**
 * 当前线程sleep
 * @param milliseconds
 * @return {Promise<undefined|Promise<any>>}
 */
async function sleep(milliseconds) {
  if (_.toInteger(milliseconds <= 0)) {
    return;
  }
  return new Promise(res => setTimeout(() => res(), milliseconds));
}

/**
 * 同uid方法
 * @return {string}
 */
function generateShortId() {
  nanoid = nanoid || require('nanoid');
  default_shortid = default_shortid || nanoid.customAlphabet(
      '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', 21);
  return default_shortid();
}

function silentParseJson(json) {
  try {
    return JSON.parse(json);
  } catch (e) {
    return null;
  }
}

function parseJson(json, silent = false) {
  if (!json) {
    return '';
  }
  if (silent) {
    try {
      return JSON.parse(json);
    } catch (e) {
      return '';
    }
  }
  try {
    return JSON.parse(json);
  } catch (e) {
    logger.error('parseJsonError', {e, json: json});
    return '';
  }
}

function currentDateTime() {
  return now();
}

function currentDate() {
  return dayjs().format(c.format_year_to_second);
}

function getHome() {
  return __dirname;
}

function encryptPsd(psd) {
  crypto = crypto || require('crypto');
  return crypto.createHmac('md5', psd).digest('hex');
}

function toJson(obj, replacer, space) {
  return JSON.stringify(obj, replacer, space);
}

function toJsonPretty(obj) {
  return toJson(obj, null, '\t');
}

/**
 * 从指定对象路径选取属性值，组合成一个新对象
 * @param obj 原始对象
 * @param paths 路径
 * @param deepClone 是否进行深拷贝
 * @return 指定路径组合成的新对象
 */
function pick(obj, paths = [], deepClone = false) {
  let obj$ = _.pick(_.pickBy(obj, (v, k) => {
    return isNotNull(v);
  }), paths);
  if (!deepClone) {
    return obj$;
  }
  return _.cloneDeep(obj$);
}

//array里面元素的值即是weight值
function getRandomIndexOnArray(array) {
  let maxValue = _.sum(array);
  let iSeed = _.random(0, (maxValue - 1));
  let iIndex = 0;
  let sampleSize = maxValue;
  _.each(array, pp => {
    sampleSize -= pp;
    if (iSeed >= sampleSize) {
      return false;
    }
    iIndex++;
  });
  return iIndex;
}

function isNotNull(token) {
  return !isNull(token);
}

function isNull(token) {
  return !(token || token === false || token === 0);
}

async function httpPost(url, postData, axiosRequestConfig = {}, option = {}) {
  option.data = option.data ?? true;
  let https = require('https');
  let axios = require('axios');
  const axios_instance = axios.create({
    httpsAgent: new https.Agent({
      rejectUnauthorized: false,
    }),
    timeout: 5000,
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
    },
  });
  let res = await axios_instance.post(url, postData, axiosRequestConfig);
  return option.data === true ? res.data : res;
}

/**
 * 发起httpGet
 * @param url 请求地址
 * @param {Object} [option] 选项
 * @param {Boolean} [option.data=true] 是否只获取返回数据本体，默认true
 * @param {Number} [option.timeout=5000] 超时时间，单位毫秒，默认5000ms
 */
async function httpGet(url, option = {}) {
  option.data = option.data ?? true;
  let timeout = option.timeout ?? 5000;

  let https = require('https');
  let axios = require('axios');
  const axios_instance = axios.create({
    httpsAgent: new https.Agent({
      rejectUnauthorized: false,
    }),
    timeout: timeout,
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
    },
  });
  let res = await axios_instance.get(url);
  return option.data === true ? res.data : res;
}

/**
 * 读取路径下所有文件
 * @param dir 目录
 */
async function readFiles(dir) {
  let files = await _readFiles(dir);
  return _.filter(files, file => {
    return file && corePath.basename(file) !== '.DS_Store';
  });
}

// 递归读取所有路径
async function _readFiles(dir) {
  return new Promise(function(resolve, reject) {
    walkDir(dir, function(err, results) {
      if (err) {
        reject(err);
      }
      resolve(results);
    });
  });
}

function walkDir(dir, done) {
  let results = [];
  coreFs.readdir(dir, function(err, list) {
    if (err) {
      return done(err);
    }
    let i = 0;
    (function next() {
      let file = list[i++];
      if (!file) {
        return done(null, results);
      }
      file = corePath.resolve(dir, file);
      coreFs.stat(file, function(err, stat) {
        if (stat && stat.isDirectory()) {
          walkDir(file, function(err, res) {
            results = results.concat(res);
            next();
          });
        } else {
          results.push(file);
          next();
        }
      });
    })();
  });
}

async function md5Dir(dir) {
  let files = await readFiles(dir);
  check(_.size(files) > 0, {msg: 'dir files empty.', dir});
  let allMd5Str = '';
  for (let file of files) {
    let fileMd5 = md5File(file);
    allMd5Str += fileMd5;
  }
  return md5(allMd5Str);
}

function md5File(file) {
  const md5_file = require('md5-file');
  return md5_file.sync(file);
}

function md5(obj, option = {}) {
  crypto = crypto || require('crypto');
  if (isNull(obj)) {
    return '';
  }
  let json = option?.json ?? true;
  if (json) {
    obj = JSON.stringify(obj);
  }
  return crypto.createHash('md5').update(obj).digest('hex');
}

function homedir2() {
  return osHome();
}

/**
 * 检查并抛出自定义异常
 * @param {Boolean|Object} condition 检查项
 * @param {Object} [errorInfo] 自定义错误，必须含有code和msg
 * @param {Object} [logObj] 错误日志对象
 * @param {String[]} [resAttrs] 保留发给客户端的字段
 */
function check2(condition, errorInfo, logObj, resAttrs = []) {
  if (condition) {
    return;
  }
  errorInfo = errorInfo || c.error_internal;
  errorInfo.code = errorInfo.code || c.error_internal.code;
  errorInfo.msg = errorInfo.msg || c.error_internal.msg;
  if (logObj && logObj.player) {
    logObj.playerId = logObj.player.playerId;
    delete logObj.player;
  }
  errorInfo.resAttrs = resAttrs;
  _.assign(errorInfo, logObj);
  // logger.error(data.msg, logObj);
  throw new c.FooError(errorInfo);
}

function check4(condition, logObj) {
  check2(condition, c.error_internal, logObj);
}

/**
 * 返回error对象的快捷方法
 * @param data 包含error的任意对象
 * @return {WptError}
 */
function newError(data) {
  return new c.FooError(data);
}

function clone2(obj) {
  return _.cloneDeep(obj);
}

function concat(...values) {
  return _.filter(_.concat([], values), o => {
    return isNotNull(o);
  });
}

function minifyFile(targetFile, oriFile) {
  let UglifyJS = require('uglify-js');
  writeFileSync(targetFile, UglifyJS.minify(readFileSync(oriFile)).code);
}

function readJson(file) {
  return parseJson(coreFs.readFileSync(file).toString());
}

async function sheet_to_aoa(excel, sheetIndex = 0, dataRow = 1, option) {
  const XLSX = require('xlsx');
  let workbook = XLSX.readFile(excel);
  let worksheet = workbook.Sheets[workbook.SheetNames[sheetIndex]];

  let option_raw = option?.raw ?? true;

  //配置参考：https://docs.sheetjs.com/#json
  let jsonRawData = XLSX.utils.sheet_to_json(worksheet,
      {
        //如果设置了header，第一行就会被处理为数据行，具体数据我们自己处理即可
        header: 1,
        //不需要关注空的行
        blankrows: false,
        //以原始字符串读取，防止类似473000的数字会被处理为473000.00006的小数问题
        raw: option_raw,
      },
  );
  return _.slice(jsonRawData, dataRow);
}

//读取所有sheet到一个对象数组
async function sheet_to_aoas(excel, option) {
  option = option || {};
  let workbook = XLSX.readFile(excel);
  let aoas = [];
  let option_raw = option.raw ?? true;
  let dataRow = option.dataRow ?? 0;

  for (let sheetName of workbook.SheetNames) {
    let worksheet = workbook.Sheets[sheetName];
    //配置参考：https://docs.sheetjs.com/#json
    //配置参考：https://docs.sheetjs.com/#json
    let jsonRawData = XLSX.utils.sheet_to_json(worksheet,
        {
          //如果设置了header，第一行就会被处理为数据行，具体数据我们自己处理即可
          header: 1,
          //不需要关注空的行
          blankrows: false,
          //以原始字符串读取，防止类似473000的数字会被处理为473000.00006的小数问题
          raw: option_raw,
        }
    );
    let aoa = _.slice(jsonRawData, dataRow);
    aoas.push({sheetName, aoa});
  }
  return aoas;
}

function includes(collection, value, fromIndex = 0) {
  return _.includes(collection, value, fromIndex);
}

function pushAll(array, pushArray) {
  if (!array || !pushArray || _.size(pushArray) <= 0) {
    return;
  }
  array = array || [];
  _.each(pushArray, a => {
    if (!array.includes(a)) {
      array.push(a);
    }
  });
}

function isNumber(token) {
  return isNotNull(token) && _.isNumber(token);
}

function getFileSize(file) {
  if (!isFile(file)) {
    return 0;
  }
  return coreFs.statSync(file).size;
}

function rewardsStr2Obj(rewards) {
  if (!rewards) {
    return;
  }
  let finalObj = {};
  _.each(_.split(rewards, ';'), reward => {
    let rewardObj = _.split(reward, ':');
    let type = rewardObj[0];//对于头像框、卡背、卡面，type就是具体奖励
    let value = 1;//对于头像框等无需配置数量的奖励
    if (rewardObj.length >= 2) {
      value = _.toInteger(rewardObj[1]);
    }
    _.set(finalObj, `${type}`, value);
  });
  return finalObj;
}

function parseJson2(token, option) {
  option = option || {};
  let silent = option.silent ?? true;
  let format = option.format ?? true;

  if (_.isObject(token)) {
    return token || {};
  } else if (_.isString(token)) {
    if (format) {
      token = _.replace(token, /[\n\r]/g, ' ');
    }
    return parseJson(token, silent) || {};
  } else {
    return {};
  }
}


function assert2(condition, obj) {
  if (condition) {
    return;
  }
  let errorInfo = _.isString(obj) ? {msg: obj} : obj;
  errorInfo = errorInfo || {};

  errorInfo.code = errorInfo.code || c.error_internal.code;
  errorInfo.msg = errorInfo.msg || c.error_internal.msg;
  if (errorInfo.player) {
    errorInfo.playerId = errorInfo.player.playerId;
    delete errorInfo.player;
  }
  if (errorInfo.game) {
    errorInfo.gameId = errorInfo.game.gameId;
    delete errorInfo.game;
  }
  throw new _assert2_error(errorInfo);
}

class _assert2_error extends Error {
  constructor(data) {
    super();
    data = data || {};
    this.code = data.code;
    this.message = toJson(data);
  }
}

//删除所有的文件(将所有文件夹置空)
function emptyDir(filePath) {
  if (!isDir(filePath)) {
    return;
  }
  const files = coreFs.readdirSync(filePath);//读取该文件夹
  files.forEach((file) => {
    const nextFilePath = `${filePath}/${file}`;
    const states = coreFs.statSync(nextFilePath);
    if (states.isDirectory()) {
      emptyDir(nextFilePath);
    } else {
      coreFs.unlinkSync(nextFilePath);
      // console.log(`删除文件 ${nextFilePath} 成功`)
    }
  });
  logger.warn('清理目录成功', {filePath});
}

function readdirSync2(dir) {
  let files = coreFs.readdirSync(dir);
  return _.map(_.filter(files, file => {
    return file && file !== '.DS_Store';
  }), file => {
    return corePath.join(dir, file);
  });
}

function indexOf2(src, dst, ignoreCase = false) {
  if (isNull(src) || isNull(dst)) {
    return -1;
  }
  if (_.isString(src)) {
    if (ignoreCase) {
      return _.toLower(src).indexOf(_.toLower(dst));
    }else {
      return (src || '').indexOf(dst);
    }
  } else if (_.isArray(src)) {
    return _.indexOf(src, dst);
  }
  logger.error('invalidOpOnIndexOf', {src, dst});
  return -1;
}

function readdirSync3(dir) {
  let files = coreFs.readdirSync(dir);
  return _.map(_.filter(files, file => {
    let path = corePath.join(dir, file);
    return file && isDir(path);
  }), file => {
    return corePath.join(dir, file);
  });
}

//TAG 新增方法的TAG点，请勿删除改行，在此行以上进行添加
module.exports = {
  readdirSync3: readdirSync3,
  indexOf2: indexOf2,
  readFiles: readFiles,
  readdirSync2: readdirSync2,
  emptyDir: emptyDir,
  assert2: assert2,
  parseJson2: parseJson2,
  rewardsStr2Obj: rewardsStr2Obj,
  getFileSize: getFileSize,
  isNumber: isNumber,
  pushAll: pushAll,
  includes: includes,
  sheet_to_aoas: sheet_to_aoas,
  sheet_to_aoa: sheet_to_aoa,
  readJson: readJson,
  minifyFile: minifyFile,
  concat: concat,
  check2: check2,
  clone2: clone2,
  newError: newError,
  homedir2: homedir2,
  md5File: md5File,
  md5: md5,
  md5Dir: md5Dir,
  httpGet: httpGet,
  httpPost: httpPost,
  now: now,
  isNull: isNull,
  isNotNull: isNotNull,
  getRandomIndexOnArray: getRandomIndexOnArray,
  pick: pick,
  readLines: readLines,
  osHome: osHome,
  getHome: getHome,
  encryptPsd: encryptPsd,
  currentDateTime: currentDateTime,
  toJson: toJson,
  toJsonPretty: toJsonPretty,
  parseJson: parseJson,
  silentParseJson: silentParseJson,
  generateShortId: generateShortId,
  uid: uid,
  sleep: sleep,
  getTimeYear2Day: getTimeYear2Day,
  getCheckParamStr: getCheckParamStr,
  getCheckParamInt: getCheckParamInt,
  timesSuccess: timesSuccess,
  getParamStr: getParamStr,
  getParamInt: getParamInt,
  addInt: addInt,
  push: push,
  check: check,
  isDir: isDir,
  isFile: isFile,
  file2JsonSync: file2JsonSync,
  addNumber: addNumber,
  toNumber: toNumber,
  readFileSync: readFileSync,
  writeFileSync: writeFileSync,
  currentDate: currentDate,
  toDateFromYear2Second: toDateFromYear2Second,
  toFailJson: toFailJson,
  toSuccessJson: toSuccessJson,
  getUrlData: getUrlData,
  getUrlData2: getUrlData2,
  n11nPoemText: n11nPoemText,
  exeSysCmdSpawn: exeSysCmdSpawn,
  exeSysCmd: exeSysCmd,
  toBase64: toBase64,
  fromBase64: fromBase64,
  readFile: readFile,
  renderWithMustache: renderWithMustache,
  mkdirSync: mkdirSync,
  joinUrl: joinUrl,
  getHrefOrigin: getHrefOrigin,
  rmdirSync: rmdirSync,
  getReqDataOnEvent: getReqDataOnEvent,
  render2File: render2File,
  readDirs: readDirs,
  getCheerio: getCheerio,
  logError: logError,
  check4: check4,
};
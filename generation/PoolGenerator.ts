import _ from 'lodash';
import chalk from 'chalk';
import ProgressBar from 'progress';
import {getTestRoundProgressPreset} from '../simulation/utils';
import {
  CallPool,
  CardSeedRecord,
  CommandFactory,
  ICardPreset,
  IRoundHostPreset,
  PlayerRoundProgress,
  RoundHost
} from '../src';
import assert from 'assert';
import {RandomSequence} from '../src/RandomSequence';
import has = Reflect.has;

export type PoolGenerationOption = {
  ruleId: number;
  callSeed: number;
  maxSeekCount: number;
  poolSize: number;
  bingoAtDistribution: { before: number; weight: number; }[];
  targetDistribution: { val: number; weight: number; before?: number; greater?: boolean; }[];
  caughtRequirements: { before: number; count: number }[];
  bingoAfterCall?: number;
}

const roundPresetPolyfill = getTestRoundProgressPreset();
roundPresetPolyfill.cardCountOptionIndex = 0;

class PoolGenerator {
  private _option: PoolGenerationOption;
  private _host: RoundHost;
  private _rs: RandomSequence;
  private _currentSeed: number;

  private _status: 'generating' | 'succeed' = 'generating'
  private _result: CallPool = null!;

  constructor(option: PoolGenerationOption, currentSeed: number) {
    // this._rule = getRuleById(option.ruleId);
    this._option = option;

    const hostPreset: IRoundHostPreset = {
      callSeed: option.callSeed,
      ruleId: option.ruleId,
      bingoCountRate: 1,
      callInterval: 5e3,
      maxCallCount: 75, // All calls
      maxPlayerCount: 50,
      roundStartTime: 0,
      totalCardCountRange: {lower: 250, upper: 300}
    };

    this._host = new RoundHost(undefined, hostPreset);

    this._rs = new RandomSequence(`${option.ruleId}_${option.callSeed}`);
    this._currentSeed = currentSeed?currentSeed:this._rs.nextIntegerInRange(2147483647);
  }

  private yieldOneCard(): CardSeedRecord {
    const record: CardSeedRecord = {
      seed: this._currentSeed,
      bingoAt: 75,
      targetCount: {},
      caught: []
    };

    const cardPreset: ICardPreset = {seed: this._currentSeed};
    roundPresetPolyfill.cardPresets = [cardPreset];
    const progress = new PlayerRoundProgress(this._host, undefined, roundPresetPolyfill);
    const card = progress.cards[0];
    const host = this._host;
    const targetBeforeCondition = _.reduce(this._option.targetDistribution, function (result: number[], value) {
      if (value.before && _.indexOf(result, value.before) === -1) {
        result.push(value.before);
      }
      return result;
    }, []);
    _.forEach(host.fullCallList, (callNumber, callIndex) => {
      const startTimeThisCall = (callIndex + 1) * host.callInterval;
      CommandFactory.receiveCall(startTimeThisCall).execute(progress);
      if (card.canBingo) {
        record.bingoAt = callIndex - 1;
        return false;
      }
      const theCall = _.last(progress.getReceivedCallList());
      const targetCell = _.find(card.cells, cell => cell.value === theCall);
      if (targetCell) {
        const isValidDaub = CommandFactory.tryDaub(startTimeThisCall, 0, targetCell.index).execute(progress);
        if (isValidDaub) {
          record.caught.push([callIndex, targetCell.index]);
        }
      }

      if (this._option.targetDistribution) {
        record.targetCount[0] = card.targetCount;
      }
      if (targetBeforeCondition.length > 0) {
        _.forEach(targetBeforeCondition, (before) => {
          if (callIndex < before) {
            record.targetCount[before] = card.targetCount;
          }
        });
      }
      if (card.canBingo) {
        record.bingoAt = callIndex;
        return false;
      }
    });
    assert(record.bingoAt <= 75);

    this._currentSeed = this._rs.nextIntegerInRange(2147483647);
    return record;
  }

  tryGenerate(): boolean {
    const sortedDistribution = _.sortedUniqBy(this._option.bingoAtDistribution, d => d.before);
    const sortedTargetDistribution = _.sortedUniqBy(this._option.targetDistribution, d => d.val);
    assert(sortedDistribution.length > 0);
    const totalWeight = _.sumBy(sortedDistribution, d => d.weight);
    const remains = _.map(sortedDistribution, d => _.round((d.weight / totalWeight) * this._option.poolSize));
    let targetRemains: number[] = [];
    const hasTarget = sortedTargetDistribution.length > 0;
    const remainsCopy = _.clone(remains);
    let totalRemain = _.sumBy(remains);
    const caughtRequirements = this._option.caughtRequirements;
    const validCards: CardSeedRecord[] = [];
    const seedDict: Record<number, boolean> = {};

    const sortedDistributionLength = sortedDistribution.length;
    let remainTemplate = _.map(sortedDistribution, (d, i) => `${chalk.green(`~${d.before}`)} :c${i + 1}`).join(' ');
    if (hasTarget) {
      const totalTargetWeight = _.sumBy(sortedTargetDistribution, d => d.weight);
      targetRemains = _.map(sortedTargetDistribution, d => _.round((d.weight / totalTargetWeight) * this._option.poolSize));
      remainTemplate += ' target:' + _.map(sortedTargetDistribution, (d, i) => `${chalk.green(`~${d.val}`)} :c${sortedDistributionLength + i + 1}`).join(' ');
    }
    const progressTokens: Record<string, number> = {seed: 0};
    _.forEach(remains.concat(targetRemains), (r, i) => {
      progressTokens[`c${i + 1}`] = r;
    });
    const bar = new ProgressBar(`Rule ${this._option.ruleId} Call #${this._option.callSeed} :percent ${remainTemplate} Count #:seek #:seed`, {
      incomplete: ' ',
      width: 16,
      total: totalRemain,
    });

    let seeked = 0;
    while (seeked < this._option.maxSeekCount) {
      const record = this.yieldOneCard();
      progressTokens['seed'] = record.seed;
      progressTokens['seek'] = seeked;
      bar.tick(0, progressTokens);
      let isConformTarget = true;
      let targetIndex = -1;
      const distributionIndex = _.findIndex(sortedDistribution, d => record.bingoAt < d.before);
      const hasRemain = distributionIndex !== -1 && remains[distributionIndex] > 0;

      let isCaughtFulfilled = true;
      _.forEach(caughtRequirements, requirement => {
        const caughtCount = _.filter(record.caught, ([callIndex]) => callIndex < requirement.before).length;
        const actualReq = record.bingoAt >= requirement.before ? requirement.count : requirement.count * (record.bingoAt / requirement.before);
        isCaughtFulfilled &&= caughtCount >= actualReq;
      });

      if (this._option.bingoAfterCall) {
        isCaughtFulfilled &&= record.bingoAt >= this._option.bingoAfterCall;
      }

      if (hasTarget) {
        targetIndex = _.findIndex(sortedTargetDistribution, d => d.greater ? record.targetCount[d.before || 0] >= d.val : record.targetCount[d.before || 0] == d.val);
        isConformTarget = targetRemains[targetIndex] > 0;
      }

      const isNewSeed = !seedDict[record.seed]; // Just in case of collisions

      if (hasRemain && isCaughtFulfilled && isNewSeed && isConformTarget) {
        validCards.push(record);
        seedDict[record.seed] = true;
        remains[distributionIndex] -= 1;
        if (hasTarget) {
          targetRemains[targetIndex] -= 1;
          progressTokens[`c${sortedDistributionLength + targetIndex + 1}`] -= 1;
        }
        totalRemain -= 1;
        progressTokens[`c${distributionIndex + 1}`] -= 1;
        bar.tick(1, progressTokens);
      }

      seeked += 1;
      // Success
      if (totalRemain <= 0) {
        // I think this will be good
        const sortedRecords = _.sortBy(validCards, c => c.bingoAt);
        this._result = {callSeed: this._option.callSeed, cards: sortedRecords};

        console.log(`======== # Call Seed ${this._option.callSeed} ========`);
        _.forEach(sortedDistribution, (d, i) => {
          console.log(chalk.inverse(` ~ ${d.before}`) + chalk.green(` ${remainsCopy[i]} / ${seeked}\n`));
        });

        this._status = 'succeed';
        return true;
      }
    }

    return false;
  }

  getResult(): CallPool {
    if (this._status !== 'succeed') throw new Error('Failed generation.');
    return this._result;
  }
}

export default PoolGenerator;

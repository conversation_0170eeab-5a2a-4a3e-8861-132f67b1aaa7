import _ from 'lodash';
import chalk from 'chalk';
import ProgressBar from 'progress';
import {getTestRoundProgressPreset} from '../simulation/utils';
import {
  CallPool, CardSeedGroupRecord,
  CommandFactory,
  ICardPreset,
  IRoundHostPreset,
  PlayerRoundProgress,
  RoundHost
} from '../src';
import {RandomSequence} from '../src/RandomSequence';

export type PoolGenerationGroupOption = {
  ruleId: number;
  callSeed: number;
  poolSize: number;
  maxSeekCount: number;
  callEnd: number;
  SeekCountCard: { bingoCount: number; weight: number; }[][];
  caughtRequirements: { before: number; count: number }[];
}

const roundPresetPolyfill = getTestRoundProgressPreset();

class PoolGeneratorGroup {
  private _option: PoolGenerationGroupOption;
  private _host: RoundHost;
  private _rs: RandomSequence;
  private _currentSeed: number;

  private _status: 'generating' | 'succeed' = 'generating'
  private _result: CallPool = null!;

  constructor(option: PoolGenerationGroupOption) {
    // this._rule = getRuleById(option.ruleId);
    this._option = option;

    const hostPreset: IRoundHostPreset = {
      callSeed: option.callSeed,
      ruleId: option.ruleId,
      bingoCountRate: 1,
      callInterval: 5e3,
      maxCallCount: 75, // All calls
      maxPlayerCount: 50,
      roundStartTime: 0,
      totalCardCountRange: {lower: 250, upper: 300}
    };

    this._host = new RoundHost(undefined, hostPreset);

    this._rs = new RandomSequence(`${option.ruleId}_${option.callSeed}`);
    this._currentSeed = this._rs.nextIntegerInRange(2147483647);
  }

  private yieldOneCard(count: number): CardSeedGroupRecord {
    const record: CardSeedGroupRecord = {
      seed: [],
      bingoAt: [],
      bingoCount: 0,
      caught: []
    };
    const seed = record.seed;
    roundPresetPolyfill.cardPresets = [];
    for (let k = 0; k < count; k++) {
      record.bingoAt[k] = 75;
      record.caught[k] = [];
      const cardPreset: ICardPreset = {seed: this._currentSeed};
      seed.push(this._currentSeed);
      this._currentSeed = this._rs.nextIntegerInRange(2147483647);
      roundPresetPolyfill.cardPresets.push(cardPreset);
    }
    roundPresetPolyfill.cardCountOptionIndex = count - 1;

    const progress = new PlayerRoundProgress(this._host, undefined, roundPresetPolyfill);
    const host = this._host;
    let bingoCount = 0;
    for (let callIndex = 0; callIndex < host.fullCallList.length; callIndex++) {
      for (let k = 0; k < count; k++) {
        const card = progress.cards[k];
        const startTimeThisCall = (callIndex + 1) * host.callInterval;
        CommandFactory.receiveCall(startTimeThisCall).execute(progress);
        const theCall = _.last(progress.getReceivedCallList());
        const targetCell = _.find(card.cells, cell => cell.value === theCall);
        if (!targetCell) continue;

        if (card.canBingo) {
          continue;
        } else {
          const isValidDaub = CommandFactory.tryDaub(startTimeThisCall, k, targetCell.index).execute(progress);
          if (isValidDaub) {
            record.caught[k].push([callIndex, targetCell.index]);
          }
        }
        if (card.canBingo && callIndex < this._option.callEnd * 1.1) {
          record.bingoAt[k] = callIndex - 1;
          bingoCount++;
        }
      }
    }
    record.bingoCount = bingoCount;
    return record;
  }

  tryGenerate(): boolean {
    const result: { [key: string]: CardSeedGroupRecord[] } = {};
    let successCount = 0;
    let runCount = 0;
    for (let k = 0; k < 4; k++) {
      runCount++;
      const SeekCountCard = this._option.SeekCountCard[k];
      const totalWeight = _.sumBy(SeekCountCard, d => d.weight);
      const remains = _.map(SeekCountCard, d => _.round((d.weight / totalWeight) * this._option.poolSize));
      let totalRemain = _.sumBy(remains);
      const caughtRequirements = this._option.caughtRequirements;
      const seedDict: Record<string, boolean> = {};

      const remainTemplate = _.map(SeekCountCard, (d, i) => `${chalk.green(`~${d.bingoCount}`)} :c${i + 1}`).join(' ');
      const progressTokens: Record<string, string> = {seed: ''};
      const bar = new ProgressBar(`Rule ${this._option.ruleId} Call #${this._option.callSeed} :percent ${remainTemplate} Count #:seek #:seed`, {
        incomplete: ' ',
        width: 16,
        total: totalRemain,
      });
      const validCards: CardSeedGroupRecord[] = [];
      let seeked = 0;
      while (seeked < this._option.maxSeekCount) {
        const record = this.yieldOneCard(k + 1);
        progressTokens['seed'] = record.seed.join(',');
        progressTokens['seek'] = seeked + '';
        _.forEach(remains.concat(remains), (r, i) => {
          progressTokens[`c${i + 1}`] = r + '';
        });
        bar.tick(0, progressTokens);
        const distributionIndex = _.findIndex(SeekCountCard, d => record.bingoCount === d.bingoCount);
        const hasRemain = distributionIndex !== -1 && remains[distributionIndex] > 0;
        const isNewSeed = !seedDict[record.seed.join(',')];

        let isCaughtFulfilled = true;
        _.forEach(caughtRequirements, requirement => {
          _.forEach(record.caught, (caught, i) => {
            const caughtCount = _.filter(caught, ([callIndex]) => callIndex < requirement.before).length;
            const actualReq = record.bingoAt[i] >= requirement.before ? requirement.count : requirement.count * (record.bingoAt[i] / requirement.before);
            isCaughtFulfilled &&= caughtCount >= actualReq;
          });
        });

        if (isCaughtFulfilled && hasRemain && isNewSeed) {
          validCards.push(record);
          seedDict[record.seed.join(',')] = true;
          if (record.bingoCount === 4) {
            //console.log(record.seed.join(','));
          }
          remains[distributionIndex] -= 1;
          totalRemain -= 1;
          progressTokens[`c${distributionIndex + 1}`] = (Number(progressTokens[`c${distributionIndex + 1}`]) - 1) + '';
          bar.tick(1, progressTokens);
        }
        seeked += 1;
        // Success
        if (totalRemain <= 0) {
          // I think this will be good
          const sortedRecords = _.sortBy(validCards, c => c.bingoCount);
          result[k + ''] = sortedRecords;

          //console.log(`======== # Call Seed ${this._option.callSeed} ========`);
          _.forEach(SeekCountCard, (d, i) => {
            //console.log(chalk.inverse(` ~ ${d.bingoCount}`) + chalk.green(` ${remainsCopy[i]} / ${seeked}\n`));
          });
          successCount++;
          break;
        }
      }
      if (successCount != runCount) {
        return false;
      }
    }
    if (successCount >= runCount) {
      this._status = 'succeed';
      this._result = {callSeed: this._option.callSeed, cardsGroups: result};
      return true;
    }

    return false;
  }

  getResult(): CallPool {
    if (this._status !== 'succeed') throw new Error('Failed generation.');
    return this._result;
  }
}

export default PoolGeneratorGroup;

const error_internal = {code: 500, msg: 'internal error.'};// 服务器内部致命错误
const _ = require('lodash');
const corePath = require('path');
const coreFs = require('fs');
const {w, logger} = require('../common/index');
const coreAssert = require('assert');
const winston = require('winston');
let crypto;

(async function() {
  // initLogger();
  let pkgDir = corePath.join(__dirname, '../src');
  logger.info('打包开始', {pkgDir});
  let genDir = __dirname;
  let md5 = await md5Dir(pkgDir);
  let verText = `
  /**
 * 自动生成文件，请勿修改。
 * 每次执行：【build-lib-2】时会自动生成此文件。
 */
const ver = '${md5}';
export {
    ver
};
`;
  let verFile = corePath.join(__dirname, '../src/ver.ts');
  w.writeFileSync(verFile, verText);

  let cmd = `cd ${genDir} && npm run build-lib-cmd`;
  await exeSysCmd(cmd);
  logger.info('打包到lib结束', {pkgDir, cmd});

  await generateLocalCardPoolCfg();
  logger.warn('生成pool描述文件结束，拷贝server/poolCfg.json文件到服务器路径：bingo-core/poolCfg.json');

  logger.warn('全部结束', {md5, pkgDir});
})();

function formatCmd(cmd) {
// 注意，反斜杠、斜杠等一定不能格式化，否则会影响例如更新alias的参数：\$LATEST
// cmd = _.replace(cmd, //g, "");
  cmd = _.replace(cmd, /\n/g, ' ');
  cmd = _.replace(cmd, /\s+/g, ' ');
  return cmd;
}

/**
 * 执行系统命令
 * @param cmd
 * @param option 配置
 * @param {boolean}[option.silent=false] 是否静默，如果为是，会为命令追加：【> /dev/null】
 */
async function exeSysCmd(cmd, option = {}) {
  cmd = formatCmd(cmd);
  const SYS_EXEC = require('child_process').exec;
  if (option?.silent === true) {
    cmd += ' > /dev/null';
  }
  logger.debug('exe cmd start.', {cmd});
  let data = '';
  const cmdOption = {maxBuffer: 1024 * 500};
  return new Promise((resolve, reject) => {
    SYS_EXEC(cmd, cmdOption, (error, stdout, stderr) => {
      if (error) {
        return reject(error);
      }
      if (stdout) {
        data += stdout.toString();
      }
      if (stderr) {
        data += stderr.toString();
      }
      return resolve(data);
    });
  });
}

// function writeFileSync(file, data, options) {
//   coreFs.writeFileSync(file, data, options);
//   logger.info('写文件结束', {file, options});
// }

async function md5Dir(dir) {
  let excludeFiles = ['src/ver.ts'];
  let files = await readFiles(dir);
  check(_.size(files) > 0, {msg: 'dir files empty.', dir});
  let allMd5Str = '';
  for (let file of files) {
    let relative = _.replace(file, corePath.join(__dirname, '../'), '');
    if (_.includes(excludeFiles, relative)) {
      logger.warn('排除md5文件', {file});
      continue;
    }
    let fileMd5 = md5File(file);
    allMd5Str += fileMd5;
  }
  return md5(allMd5Str);
}

function check(condition, errorInfo, logObj) {
  if (condition) {
    return;
  }
  errorInfo = errorInfo || error_internal;
  errorInfo.code = errorInfo.code || error_internal.code;
  errorInfo.msg = errorInfo.msg || error_internal.msg;
  if (logObj && logObj.player) {
    logObj.playerId = logObj.player.playerId;
    delete logObj.player;
  }
  _.assign(errorInfo, logObj);
  throw new WptError(errorInfo);
}

function md5File(file) {
  const md5_file = require('md5-file');
  return md5_file.sync(file);
}

async function readFiles(dir) {
  let files = await _readFiles(dir);
  return _.filter(files, file => {
    return file && corePath.basename(file) !== '.DS_Store';
  });
}

// 递归读取所有路径
async function _readFiles(dir) {
  return new Promise(function(resolve, reject) {
    walkDir(dir, function(err, results) {
      if (err) {
        reject(err);
      }
      resolve(results);
    });
  });
}

function walkDir(dir, done) {
  let results = [];
  coreFs.readdir(dir, function(err, list) {
    if (err) {
      return done(err);
    }
    let i = 0;
    (function next() {
      let file = list[i++];
      if (!file) {
        return done(null, results);
      }
      file = corePath.resolve(dir, file);
      coreFs.stat(file, function(err, stat) {
        if (stat && stat.isDirectory()) {
          walkDir(file, function(err, res) {
            results = results.concat(res);
            next();
          });
        } else {
          results.push(file);
          next();
        }
      });
    })();
  });
}

function md5(obj, option = {}) {
  crypto = crypto || require('crypto');
  if (isNull(obj)) {
    return '';
  }
  let json = option?.json ?? true;
  if (json) {
    obj = JSON.stringify(obj);
  }
  return crypto.createHash('md5').update(obj).digest('hex');
}

class WptError extends Error {
  constructor(data, msg) {
    super();
    this.code = _.get(data, 'code', error_internal.code);
    if (data) {
      // 兼容自定义数据
      _.assign(this, data);
      if (data.msg) {
        this.message = data.msg;
      }
      // 兼容dynamoDB的eror message
      if (data.message) {
        this.message = data.message;
      }
      // 传递回传客户端的参数
      this.resAttrs = data.resAttrs;
    }
    // 优先使用自定义的msg
    msg = _.toString(msg);
    if (msg) {
      this.message = msg;
    }
    // 没有任何匹配，使用默认
    this.message = this.message || error_internal.msg;
  }
}

function isNotNull(token) {
  return !isNull(token);
}

function isNull(token) {
  return !(token || token === false || token === 0);
}

function initLogger() {
  if (logger) {
    return;
  }
  logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(winston.format.timestamp(),
        winston.format(info => {
          if (info.player) {
            info.playerId = _.get(info.player, 'playerId');
            delete info.player;
          }
          if (info.game) {
            info.gameId = _.get(info.game, 'gameId');
            delete info.game;
          }
          if (info.userInfo) {
            info.userId = _.get(info.userInfo, 'userId');
            delete info.userInfo;
          }
          if (info.e && info.e instanceof Error) {
            // if (!c.isLambdaEnv) {
            //     console.error("error(no lambda env).\n", info);
            // }
            console.error(info);
            if (info.e.player) {
              info.e.playerId = info.e.player.playerId;
              delete info.e.player;
            }
            // stack的截取需要按实际日志进行控制
            if (info.e.stack) {
              info.e.stack = _.truncate(info.e.stack,
                  {length: c.limit_loggerLength});
            }
          }
          if (info.context) {
            info.awsRequestId = info.context.awsRequestId;
            delete info.context;
          }
          return info;
        })(), winston.format.colorize(), winston.format.simple()), // format: winston.format.combine(winston.format.timestamp(), winstonInfoFormat(), winston.format.colorize(), winston.format.simple()),
    transports: [
      new winston.transports.Console()],
    exitOnError: true,
  });
}

async function generateLocalCardPoolCfg() {
  const corePath = require('path');
  const coreFs = require('fs');
  // if (this._manifest[ruleId]) return;

  // const rulePath = path.join(this._poolPath, ruleId.toString());
  let dirs = w.readdirSync3(
      corePath.join(__dirname, '../simulation/pools'));

  let finalObj = {};
  _.each(dirs, rulePath => {
    _(coreFs.readdirSync(rulePath, {withFileTypes: true})).forEach((dirent) => {
      if (!dirent.isFile()) return;
      const matchResult = dirent.name.match(
          /rule_(\d+)_call_(group_)?(\d+).json/);
      if (!matchResult) return;
      const [, ruleIdStr, , callSeedStr] = matchResult;
      w.assert2(ruleIdStr, callSeedStr);
      if (!finalObj[ruleIdStr]) finalObj[ruleIdStr] = [];
      const profile = {
        ruleId: _.toInteger(ruleIdStr),
        callSeed: _.toInteger(callSeedStr),
        path: dirent.name,
      };
      finalObj[ruleIdStr].push(profile);
    });
  });
  let poolCfgPath = corePath.join(__dirname, '../server/poolCfg.json');
  w.writeFileSync(poolCfgPath, w.toJson(finalObj));
}
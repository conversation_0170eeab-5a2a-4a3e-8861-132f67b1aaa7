// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import _ from 'lodash';
import assert from 'assert';
import fs from 'fs';
import path from 'path';
import PoolGenerator, {PoolGenerationOption} from './PoolGenerator';

let argsArr = null;
if (process.argv && process.argv.length >= 3) {
    argsArr = process.argv; // argsArr[2]:是否删除种子文件 argsArr[3]:一个进程跑多少个种子
} else {
    throw "process.argv err";
}
const params: { ruleId: number, callIdFrom: number, callCount: number; }[] = [
    // { ruleId: 101, callIdFrom: 300, callIdTo: 700 },
    // { ruleId: 201, callIdFrom: 206, callIdTo: 250 },
    // { ruleId: 202, callIdFrom: 205, callIdTo: 250 },
    // { ruleId: 203, callIdFrom: 203020, callIdTo: 203050 },
    // { ruleId: 204, callIdFrom: 204000, callIdTo: 204050 },
    // { ruleId: 205, callIdFrom: 205, callIdTo: 250 },
    // { ruleId: 206, callIdFrom: 205, callIdTo: 250 },
    // { ruleId: 207, callIdFrom: 204, callIdTo: 250 },
    // { ruleId: 208, callIdFrom: 208005, callIdTo: 208050 },
    // { ruleId: 209, callIdFrom: 209005, callIdTo: 209050 },
    // { ruleId: 210, callIdFrom: 210005, callIdTo: 210050 },
    // { ruleId: 211, callIdFrom: 211051, callIdTo: 211100 },
    // { ruleId: 212, callIdFrom: 212005, callIdTo: 212050 },
    // { ruleId: 213, callIdFrom: 213005, callIdTo: 213050 },
    // { ruleId: 214, callIdFrom: 2140032, callIdTo: 214050 },
    // {ruleId: 215, callIdFrom: 215005, callIdTo: 215050},,
    // {ruleId: 216, callIdFrom: 216240, callCount: 50},
    // {ruleId: 217, callIdFrom: 217000, callCount: 50},
    // {ruleId: 218, callIdFrom: 218000, callCount: 50},
    // {ruleId: 219, callIdFrom: 219200, callCount: 14},
    // {ruleId: 220, callIdFrom: 220000, callCount: 50},
    // {ruleId: 224, callIdFrom: 224000, callCount: 50},
    // {ruleId: 221, callIdFrom: Math.ceil(Math.random() * 1000000), callCount: 50},
    // {ruleId: 222, callIdFrom: Math.ceil(Math.random() * 1000000), callCount: 50},
    // {ruleId: 227, callIdFrom: Math.ceil(Math.random() * 1000000), callCount: 50},
    // {ruleId: 226, callIdFrom: Math.ceil(Math.random() * 1000000), callCount: 50},
    // {ruleId: 231, callIdFrom: Math.ceil(Math.random() * 1000000), callCount: 50},
    // {ruleId: 500, callIdFrom: Math.ceil(Math.random() * 1000000), callCount: 50},
    // {ruleId: 225, callIdFrom: Math.ceil(Math.random() * 1000000), callCount: 50},
    // {ruleId: 501, callIdFrom: Math.ceil(Math.random() * 1000000), callCount: 50},
    // {ruleId: 502, callIdFrom: Math.ceil(Math.random() * 1000000), callCount: 50},
    //{ruleId: 226, callIdFrom: Math.ceil(Math.random() * 1000000), callCount: 50},
    // {ruleId: 229, callIdFrom: Math.ceil(Math.random() * 1000000), callCount: 50},
    // {ruleId: 230, callIdFrom: Math.ceil(Math.random() * 1000000), callCount: 50},
    // {ruleId: 232, callIdFrom: Math.ceil(Math.random() * 1000000), callCount: 50},
    //{ruleId: 233, callIdFrom: Math.ceil(Math.random() * 1000000), callCount: 50},
    //{ruleId: 234, callIdFrom: Math.ceil(Math.random() * 1000000), currentSeed: 1143299271, callCount: 50},
    {ruleId: 234, callIdFrom: 585269, currentSeed: 715659049, callCount: argsArr && argsArr[3] ? argsArr[3] : 50
    },
    //{ruleId: 235, callIdFrom: Math.ceil(Math.random() * 1000000), callCount: 50},
    //{ruleId: 504, callIdFrom: Math.ceil(Math.random() * 1000000), callCount: 50},
];

///// Common /////
const getOption101 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 101,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 300000,
    bingoAtDistribution: [
        {before: 9, weight: 10},
        {before: 12, weight: 10},
        {before: 15, weight: 15},
        {before: 18, weight: 15},
        {before: 20, weight: 10},
        {before: 24, weight: 10},
        {before: 38, weight: 15},
        {before: 50, weight: 15},
    ],
    caughtRequirements: [{before: 25, count: 10}],
});

///// Bomberman /////
const getOption201 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 201,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 300000,
    bingoAtDistribution: [
        {before: 10, weight: 10},
        {before: 12, weight: 10},
        {before: 15, weight: 15},
        {before: 18, weight: 15},
        {before: 20, weight: 10},
        {before: 24, weight: 10},
        {before: 38, weight: 15},
        {before: 50, weight: 15},
    ],
    caughtRequirements: [{before: 25, count: 10}],
});

///// Easter Egg /////
const getOption202 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 202,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 300000,
    bingoAtDistribution: [
        {before: 9, weight: 10},
        {before: 12, weight: 10},
        {before: 15, weight: 15},
        {before: 18, weight: 15},
        {before: 20, weight: 10},
        {before: 24, weight: 10},
        {before: 38, weight: 15},
        {before: 50, weight: 15},
    ],
    caughtRequirements: [{before: 25, count: 10}],
});

///// Bubble Burst /////
const getOption203 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 203,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 10000000,
    bingoAtDistribution: [
        {before: 18, weight: 5},
        {before: 20, weight: 20},
        {before: 25, weight: 25},
        {before: 40, weight: 25},
        {before: 50, weight: 10},
        {before: 70, weight: 5},
        {before: 75, weight: 10},
    ],
    caughtRequirements: [{before: 20, count: 7}],
});

///// Cupid /////
const getOption204 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 204,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 1000000,
    bingoAtDistribution: [
        {before: 10, weight: 3},
        {before: 12, weight: 5},
        {before: 15, weight: 12},
        {before: 18, weight: 15},
        {before: 20, weight: 15},
        {before: 25, weight: 15},
        {before: 50, weight: 15},
        {before: 60, weight: 20},
    ],
    caughtRequirements: [{before: 20, count: 7}],
});

///// Bamboo /////
const getOption205 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 205,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 1000000,
    bingoAtDistribution: [
        {before: 14, weight: 20},
        {before: 18, weight: 30},
        {before: 20, weight: 10},
        {before: 24, weight: 10},
        {before: 38, weight: 15},
        {before: 50, weight: 15},
    ],
    caughtRequirements: [{before: 25, count: 10}],
});

///// Clover /////
const getOption206 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 206,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 300000,
    bingoAtDistribution: [
        {before: 9, weight: 10},
        {before: 12, weight: 10},
        {before: 15, weight: 15},
        {before: 18, weight: 15},
        {before: 20, weight: 10},
        {before: 24, weight: 10},
        {before: 38, weight: 15},
        {before: 50, weight: 15},
    ],
    caughtRequirements: [{before: 25, count: 10}],
});

///// Flower Fairy /////
const getOption207 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 207,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 3000000,
    bingoAtDistribution: [
        {before: 11, weight: 10},
        {before: 13, weight: 10},
        {before: 15, weight: 15},
        {before: 18, weight: 15},
        {before: 20, weight: 10},
        {before: 24, weight: 10},
        {before: 38, weight: 15},
        {before: 50, weight: 15},
    ],
    caughtRequirements: [{before: 25, count: 10}],
});

///// Hanabi /////
const getOption208 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 208,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 10000000,
    bingoAtDistribution: [
        {before: 13, weight: 5},
        {before: 15, weight: 15},
        {before: 18, weight: 30},
        {before: 20, weight: 10},
        {before: 24, weight: 10},
        {before: 38, weight: 10},
        {before: 50, weight: 5},
        {before: 74, weight: 1},
        {before: 75, weight: 14},
    ],
    caughtRequirements: [{before: 25, count: 10}],
});

///// Fast Food /////
const getOption209 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 209,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 10000000,
    bingoAtDistribution: [
        {before: 10, weight: 4},
        {before: 13, weight: 10},
        {before: 15, weight: 13},
        {before: 18, weight: 18},
        {before: 20, weight: 15},
        {before: 24, weight: 10},
        {before: 38, weight: 10},
        {before: 50, weight: 10},
        {before: 75, weight: 5},
        {before: 76, weight: 5},
    ],
    caughtRequirements: [{before: 25, count: 10}],
});

///// Tile Matching /////
const getOption210 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 210,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 10000000,
    bingoAtDistribution: [
        {before: 11, weight: 3},
        {before: 13, weight: 7},
        {before: 15, weight: 10},
        {before: 18, weight: 18},
        {before: 24, weight: 15},
        {before: 38, weight: 10},
        {before: 50, weight: 10},
        {before: 70, weight: 12},
        {before: 75, weight: 13},
        {before: 76, weight: 2},
    ],
    caughtRequirements: [{before: 20, count: 8}],
});

///// Penguin /////
const getOption211 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 211,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 10000000,
    bingoAtDistribution: [
        {before: 16, weight: 5},
        {before: 18, weight: 20},
        {before: 24, weight: 25},
        {before: 38, weight: 10},
        {before: 50, weight: 15},
        {before: 70, weight: 20},
        {before: 75, weight: 5},
    ],
    caughtRequirements: [{before: 20, count: 8}],
});

///// Building Blocks /////
const getOption212 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 212,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 10000000,
    bingoAtDistribution: [
        {before: 18, weight: 5},
        {before: 20, weight: 15},
        {before: 24, weight: 25},
        {before: 38, weight: 10},
        {before: 50, weight: 20},
        {before: 62, weight: 20},
        {before: 66, weight: 5},
    ],
    caughtRequirements: [{before: 10, count: 4}, {before: 25, count: 12}],
});

///// Royal Flush /////
const getOption213 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 213,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 10000000,
    bingoAtDistribution: [
        {before: 16, weight: 3},
        {before: 18, weight: 5},
        {before: 20, weight: 17},
        {before: 24, weight: 20},
        {before: 38, weight: 10},
        {before: 50, weight: 20},
        {before: 62, weight: 20},
        {before: 66, weight: 5},
    ],
    caughtRequirements: [{before: 10, count: 4}, {before: 25, count: 12}],
});

///// Royal Flush /////
const getOption214 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 214,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 10000000,
    bingoAtDistribution: [
        {before: 16, weight: 3},
        {before: 18, weight: 5},
        {before: 20, weight: 17},
        {before: 24, weight: 20},
        {before: 38, weight: 20},
        {before: 50, weight: 25},
        {before: 57, weight: 8},
        {before: 62, weight: 2},
    ],
    caughtRequirements: [{before: 10, count: 4}, {before: 25, count: 12}],
});

///// Piggy /////
const getOption215 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 215,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 10000000,
    bingoAtDistribution: [
        {before: 16, weight: 3},
        {before: 18, weight: 5},
        {before: 20, weight: 17},
        {before: 24, weight: 20},
        {before: 38, weight: 20},
        {before: 50, weight: 25},
        {before: 57, weight: 8},
        {before: 62, weight: 2},
    ],
    caughtRequirements: [{before: 10, count: 4}, {before: 25, count: 12}],
});

///// Race /////
const getOption216 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 216,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 800000,
    bingoAtDistribution: [
        {before: 16, weight: 3},
        {before: 18, weight: 5},
        {before: 20, weight: 13},
        {before: 24, weight: 24},
        {before: 38, weight: 23},
        {before: 50, weight: 26},
        {before: 57, weight: 5},
        {before: 62, weight: 1},
    ],
    targetDistribution: [
        {val: 0, weight: 5, before: 25},
        {val: 1, weight: 10, before: 25},
        {val: 2, weight: 130},
        {val: 3, weight: 210},
        {val: 4, weight: 200},
        {val: 5, weight: 80},
        {val: 6, weight: 50},
    ],
    caughtRequirements: [{before: 25, count: 12}],
});

const getOption217 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 217,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 500000,
    bingoAtDistribution: [
        {before: 16, weight: 3},
        {before: 18, weight: 5},
        {before: 20, weight: 17},
        {before: 24, weight: 20},
        {before: 38, weight: 20},
        {before: 50, weight: 25},
        {before: 57, weight: 8},
        {before: 62, weight: 2},
    ],
    caughtRequirements: [{before: 10, count: 4}, {before: 25, count: 12}],
});

const getOption218 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 218,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 500000,
    bingoAtDistribution: [
        {before: 16, weight: 3},
        {before: 18, weight: 5},
        {before: 20, weight: 17},
        {before: 24, weight: 20},
        {before: 38, weight: 20},
        {before: 50, weight: 25},
        {before: 57, weight: 8},
        {before: 62, weight: 2},
    ],
    caughtRequirements: [{before: 10, count: 4}, {before: 25, count: 12}],
});

const getOption219 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 219,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 500000,
    bingoAtDistribution: [
        {before: 16, weight: 3},
        {before: 18, weight: 5},
        {before: 20, weight: 17},
        {before: 24, weight: 20},
        {before: 38, weight: 20},
        {before: 50, weight: 25},
        {before: 57, weight: 8},
        {before: 62, weight: 2},
    ],
    caughtRequirements: [{before: 10, count: 4}, {before: 25, count: 12}],
});

const getOption220 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 220,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 500000,
    bingoAtDistribution: [
        {before: 16, weight: 3},
        {before: 18, weight: 5},
        {before: 20, weight: 17},
        {before: 24, weight: 20},
        {before: 38, weight: 20},
        {before: 50, weight: 25},
        {before: 57, weight: 8},
        {before: 62, weight: 2},
    ],
    caughtRequirements: [{before: 10, count: 4}, {before: 25, count: 12}],
});

const getOption224 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 224,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 400000,
    bingoAtDistribution: [
        {before: 16, weight: 3},
        {before: 18, weight: 5},
        {before: 20, weight: 17},
        {before: 24, weight: 20},
        {before: 38, weight: 20},
        {before: 50, weight: 25},
        {before: 57, weight: 8},
        {before: 62, weight: 2},
    ],
    targetDistribution: [
        {val: 1, weight: 10},
        {val: 2, weight: 30},
        {val: 3, weight: 60, greater: true},
    ],
    caughtRequirements: [{before: 8, count: 3}, {before: 25, count: 12}],
    bingoAfterCall: 8,
});

const getOption221 = (callSeed: number): PoolGenerationOption => (Object.assign(getOption(callSeed), {
    maxSeekCount: 700000,
    caughtRequirements: [{before: 10, count: 2}, {before: 25, count: 8}],
    bingoAtDistribution: [
        {before: 18, weight: 8},
        {before: 20, weight: 17},
        {before: 24, weight: 20},
        {before: 38, weight: 20},
        {before: 50, weight: 25},
        {before: 57, weight: 8},
        {before: 62, weight: 2},
    ],
}));

const getOption222 = (callSeed: number): PoolGenerationOption => (Object.assign(getOption(callSeed), {
    maxSeekCount: 800000,
    caughtRequirements: [{before: 10, count: 2}, {before: 25, count: 11}],
}));
const getOption230 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 230,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 500000,
    bingoAtDistribution: [
        {before: 16, weight: 3},
        {before: 18, weight: 5},
        {before: 20, weight: 17},
        {before: 24, weight: 20},
        {before: 38, weight: 20},
        {before: 50, weight: 25},
        {before: 57, weight: 8},
        {before: 62, weight: 2},
    ],
    caughtRequirements: [{before: 10, count: 4}, {before: 25, count: 8}],
});
const getOption232 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 232,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 500000,
    bingoAtDistribution: [
        {before: 16, weight: 3},
        {before: 18, weight: 5},
        {before: 20, weight: 17},
        {before: 24, weight: 20},
        {before: 38, weight: 20},
        {before: 50, weight: 25},
        {before: 57, weight: 9},
        {before: 62, weight: 1},
    ],
    caughtRequirements: [{before: 10, count: 4}, {before: 25, count: 8}],
});
//通用取值 其他关卡 根据需求来调整
const getOption = (callSeed: number): PoolGenerationOption => ({
    ruleId: 0,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 500000,
    bingoAtDistribution: [
        {before: 16, weight: 3},
        {before: 18, weight: 5},
        {before: 20, weight: 17},
        {before: 24, weight: 20},
        {before: 38, weight: 20},
        {before: 50, weight: 25},
        {before: 57, weight: 8},
        {before: 62, weight: 2},
    ],
    caughtRequirements: [{before: 10, count: 4}, {before: 25, count: 12}],
});


const getOption231 = (callSeed: number): PoolGenerationOption => ({
    ruleId: 231,
    callSeed: callSeed,
    poolSize: 300,
    maxSeekCount: 500000,
    bingoAtDistribution: [
        {before: 16, weight: 3},
        {before: 18, weight: 5},
        {before: 20, weight: 17},
        {before: 24, weight: 20},
        {before: 38, weight: 20},
        {before: 50, weight: 25},
        {before: 57, weight: 8},
        {before: 62, weight: 2},
    ],
    caughtRequirements: [{before: 10, count: 4}, {before: 25, count: 12}],
    bingoAfterCall: 6,
});

const optionBuilders: Record<number, (ruleId: number) => PoolGenerationOption> = {};
optionBuilders[101] = getOption101;
optionBuilders[201] = getOption201;
optionBuilders[202] = getOption202;
optionBuilders[203] = getOption203;
optionBuilders[204] = getOption204;
optionBuilders[205] = getOption205;
optionBuilders[206] = getOption206;
optionBuilders[207] = getOption207;
optionBuilders[208] = getOption208;
optionBuilders[209] = getOption209;
optionBuilders[210] = getOption210;
optionBuilders[211] = getOption211;
optionBuilders[212] = getOption212;
optionBuilders[213] = getOption213;
optionBuilders[214] = getOption214;
optionBuilders[215] = getOption215;
optionBuilders[216] = getOption216;
optionBuilders[217] = getOption217;
optionBuilders[218] = getOption218;
optionBuilders[219] = getOption219;
optionBuilders[220] = getOption220;
optionBuilders[224] = getOption224;
optionBuilders[221] = getOption221;
optionBuilders[222] = getOption222;
optionBuilders[231] = getOption231;
optionBuilders[230] = getOption230;
optionBuilders[232] = getOption232;
optionBuilders[0] = getOption;

_.forEach(params, p => {
    const dirName = path.resolve(__dirname + '/../simulation/pools/' + p.ruleId);
    if (argsArr && argsArr[2] && Number(argsArr[2])) {
        if (fs.existsSync(dirName)) {
            fs.rmSync(dirName, {recursive: true});
        }
        fs.mkdirSync(dirName);
    }
    const getOption = optionBuilders[p.ruleId] || optionBuilders[0];
    assert(getOption);
    let count = 0;
    let callId = p.callIdFrom;
    while (count < p.callCount) {
        const generationOption = getOption(callId);
        if (generationOption.ruleId === 0) {
            generationOption.ruleId = p.ruleId;
        }
        assert(generationOption.ruleId === p.ruleId);
        const generator = new PoolGenerator(generationOption, p.currentSeed);
        const succeed = generator.tryGenerate();
        if (succeed) {
            count++;
            const result = generator.getResult();
            fs.writeFileSync(path.resolve(dirName, `rule_${p.ruleId}_call_${callId}.json`), JSON.stringify(result));
        }
        callId++;
    }
});
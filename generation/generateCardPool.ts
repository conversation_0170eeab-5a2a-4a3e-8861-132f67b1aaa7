// @ts-nocheck
import _ from 'lodash';
import chalk from 'chalk';
import fs from 'fs';
import path from 'path';
import assert from 'assert';
import ProgressBar from 'progress';
import CardSeedGenerator, { CallPoolLegacy, CardSeedRecordLegacy, RequiredDaubCount } from "../src/CardSeedGenerator";
import { getTestRoundProgressPreset } from '../simulation/utils';

export type GenerationOption = {
  ruleId: number;
  maxSeedSeekCount: number;
  callSeedStart: number;
  callSeedCount: number;
  bingoAtStep1: number;
  bingoAtStep2: number;
  bingoAtStep1Rate: number;
  bingoAtStep2Rate: number;
  minimumDaubCountNaturalBingo: number;
  minimumDaubCountPowerupBingo: number;
  minimumDaubCountNoBingo: number;
}

const eachTypeRequiredCount = 100;
const ruleIds = [101, 201, 202, 203, 204, 205];

const ruleIdMapToOption = new Map<number, GenerationOption>();
// Common
ruleIdMapToOption.set(101, {
  ruleId: 101,
  maxSeedSeekCount: 1000000,
  callSeedStart: 50,
  callSeedCount: 50,
  bingoAtStep1: 11,
  bingoAtStep2: 17,
  bingoAtStep1Rate: 0.2,
  bingoAtStep2Rate: 0.5,
  minimumDaubCountNaturalBingo: 8,
  minimumDaubCountPowerupBingo: 8,
  minimumDaubCountNoBingo: 8,
});

// Bomberman
ruleIdMapToOption.set(201, {
  ruleId: 201,
  maxSeedSeekCount: 1000000,
  callSeedStart: 50,
  callSeedCount: 50,
  bingoAtStep1: 11,
  bingoAtStep2: 17,
  bingoAtStep1Rate: 0.2,
  bingoAtStep2Rate: 0.5,
  minimumDaubCountNaturalBingo: 7,
  minimumDaubCountPowerupBingo: 8,
  minimumDaubCountNoBingo: 8,
});

// Easter Egg
ruleIdMapToOption.set(202, {
  ruleId: 202,
  maxSeedSeekCount: 1000000,
  callSeedStart: 50,
  callSeedCount: 50,
  bingoAtStep1: 11,
  bingoAtStep2: 17,
  bingoAtStep1Rate: 0.2,
  bingoAtStep2Rate: 0.5,
  minimumDaubCountNaturalBingo: 8,
  minimumDaubCountPowerupBingo: 8,
  minimumDaubCountNoBingo: 8,
});

// Bubble Burst
ruleIdMapToOption.set(203, {
  ruleId: 203,
  maxSeedSeekCount: 100000000,
  callSeedStart: 66,
  callSeedCount: 50,
  bingoAtStep1: 17,
  bingoAtStep2: 20,
  bingoAtStep1Rate: 0,
  bingoAtStep2Rate: 0,
  minimumDaubCountNaturalBingo: 0,
  minimumDaubCountPowerupBingo: 8,
  minimumDaubCountNoBingo: 8,
});

// Cupid
ruleIdMapToOption.set(204, {
  ruleId: 204,
  maxSeedSeekCount: 1000000,
  callSeedStart: 50,
  callSeedCount: 50,
  bingoAtStep1: 11,
  bingoAtStep2: 17,
  bingoAtStep1Rate: 0.2,
  bingoAtStep2Rate: 0.5,
  minimumDaubCountNaturalBingo: 8,
  minimumDaubCountPowerupBingo: 8,
  minimumDaubCountNoBingo: 8,
});

// Bamboo
ruleIdMapToOption.set(205, {
  ruleId: 205,
  maxSeedSeekCount: 1000000,
  callSeedStart: 1000,
  callSeedCount: 50,
  bingoAtStep1: 12,
  bingoAtStep2: 17,
  bingoAtStep1Rate: 0.1,
  bingoAtStep2Rate: 0.5,
  minimumDaubCountNaturalBingo: 8,
  minimumDaubCountPowerupBingo: 8,
  minimumDaubCountNoBingo: 8,
});

const option = ruleIdMapToOption.get(205);
assert(option);

const testPreset = getTestRoundProgressPreset();
_(option.callSeedStart).range(option.callSeedStart + option.callSeedCount).forEach(callSeed => {
  let releaseFactor = 0;
  const generator = new CardSeedGenerator(callSeed, option.ruleId, 24, testPreset);

  const naturalBingoAtBelowStep1Count = _.floor(eachTypeRequiredCount * option.bingoAtStep1Rate);
  const naturalBingoAtBelowStep2Count = _.floor(eachTypeRequiredCount * option.bingoAtStep2Rate);
  const naturalBingoOtherCount = eachTypeRequiredCount - naturalBingoAtBelowStep1Count - naturalBingoAtBelowStep2Count;
  assert(naturalBingoOtherCount >= 0 && naturalBingoAtBelowStep1Count >= 0 && naturalBingoAtBelowStep2Count >= 0);
  const naturalBingoAtBelowStep1: CardSeedRecordLegacy[] = [];
  const naturalBingoAtBelowStep2: CardSeedRecordLegacy[] = [];
  const naturalBingoOther: CardSeedRecordLegacy[] = [];
  const powerupBingo: CardSeedRecordLegacy[] = [];
  const noBingo: CardSeedRecordLegacy[] = [];

  let totalCounter = 0;
  let naturalCounter = 0;
  let powerupCounter = 0;
  let noCounter = 0;
  const bar = new ProgressBar(`Rule ${option.ruleId} Call #${callSeed} Seed #:seed [:bar]:percent | ${chalk.green('Na')} :c1 ${chalk.yellow('Po')} :c2 ${chalk.red('No')} :c3`, {
    incomplete: ' ',
    width: 16,
    total: eachTypeRequiredCount * 3,
  });

  let isPoorSeed = false;
  while (true) {
    const record = generator.yieldOne();
    totalCounter += 1;
    if (record.bingoAt !== -1 && record.daubCount >= option.minimumDaubCountNaturalBingo) {
      naturalCounter += 1;
      // BingoAt count
      if (record.bingoAt < (option.bingoAtStep1 + releaseFactor) && naturalBingoAtBelowStep1.length < naturalBingoAtBelowStep1Count) {
        naturalBingoAtBelowStep1.push(record);
        bar.tick(1, { seed: record.seed });
      } else if (record.bingoAt < (option.bingoAtStep2 + releaseFactor) && naturalBingoAtBelowStep2.length < naturalBingoAtBelowStep2Count) {
        naturalBingoAtBelowStep2.push(record);
        bar.tick(1, { seed: record.seed });
      } else if (naturalBingoOther.length < naturalBingoOtherCount) {
        naturalBingoOther.push(record);
        bar.tick(1, { seed: record.seed });
      }
    } else if (record.requiredDaubCountToBingo !== RequiredDaubCount.MoreThanTwo && record.daubCount >= option.minimumDaubCountPowerupBingo) {
      powerupCounter += 1;
      if (powerupBingo.length < eachTypeRequiredCount) {
        powerupBingo.push(record);
        bar.tick(1, { seed: record.seed });
      }
    } else if (record.daubCount >= option.minimumDaubCountNoBingo) {
      noCounter += 1;
      if (noBingo.length < eachTypeRequiredCount) {
        noBingo.push(record);
        bar.tick(1, { seed: record.seed });
      }
    }

    const totalNaturalCount = naturalBingoAtBelowStep1.length + naturalBingoAtBelowStep2.length + naturalBingoOther.length;
    if (totalNaturalCount >= eachTypeRequiredCount && powerupBingo.length >= eachTypeRequiredCount && noBingo.length >= eachTypeRequiredCount) {
      break;
    }

    bar.tick(0, { seed: record.seed, c1: totalNaturalCount, c2: powerupBingo.length, c3: noBingo.length });
    // console.log(`${_.floor((naturalBingoAtBelowStep1.length + naturalBingoAtBelowStep2.length + naturalBingoOther.length) / eachTypeRequiredCount * 100)} %`);

    // Smoothly release restriction
    if (totalCounter > 500000) {
      releaseFactor = _.toSafeInteger((totalCounter - 400000) / 100000);
    }

    // Termination
    if (totalCounter >= option.maxSeedSeekCount) {
      isPoorSeed = true;
      break;
    }
  }

  if (isPoorSeed) {
    console.log(`======== # Call Seed ${callSeed} ========`);
    console.log(`Poor Seed.`);
    return;
  }

  const callPool: CallPoolLegacy = {
    callSeed,
    naturalBingo: _.concat(naturalBingoAtBelowStep1, naturalBingoAtBelowStep2, naturalBingoOther),
    powerupBingo,
    noBingo,
  };

  console.log(`======== # Call Seed ${callSeed} ========`);
  console.log(chalk.inverse(' Natural Bingo ') + chalk.green(` ${naturalCounter} / ${totalCounter}\n`));
  console.log(chalk.inverse(' Powerup Bingo ') + chalk.yellow(` ${powerupCounter} / ${totalCounter}\n`));
  console.log(chalk.inverse(' no Bingo ') + chalk.red(` ${noCounter} / ${totalCounter}\n`));

  fs.writeFileSync(path.resolve(__dirname, `rule_${option.ruleId}_call_${callSeed}.json`), JSON.stringify(callPool));
});

import _ from 'lodash';
import fs from 'fs';
import path from 'path';
import PoolGeneratorGroup from "./PoolGeneratorGroup";

const params: { ruleId: number, callIdFrom: number, callCount: number; }[] = [
  {ruleId: 223, callIdFrom: Math.ceil(Math.random() * 1000000), callCount: 50},
];

_.forEach(params, p => {
  const getOption = {
    callSeed: 0,
    ruleId: p.ruleId,
    poolSize: 90,
    maxSeekCount: 500000,
    callEnd: 27,
    SeekCountCard: [
      [
        {bingoCount: 0, weight: 25},
        {bingoCount: 1, weight: 5},
      ],
      [
        {bingoCount: 0, weight: 10},
        {bingoCount: 1, weight: 17},
        {bingoCount: 2, weight: 3},
      ], [
        {bingoCount: 0, weight: 10},
        {bingoCount: 1, weight: 12},
        {bingoCount: 2, weight: 7},
        {bingoCount: 3, weight: 1},
      ], [
        {bingoCount: 0, weight: 10},
        {bingoCount: 1, weight: 10},
        {bingoCount: 2, weight: 0},
        {bingoCount: 3, weight: 0},
        {bingoCount: 4, weight: 0}
      ]
    ],
    caughtRequirements: []//{before: 10, count: 4}
  };
  let count = 0;
  let callId = p.callIdFrom;
  while (count < p.callCount) {
    getOption.callSeed = callId;
    const generator = new PoolGeneratorGroup(getOption);
    const succeed = generator.tryGenerate();
    if (succeed) {
      count++;
      const result = generator.getResult();
      fs.writeFileSync(path.resolve(__dirname, `rule_${p.ruleId}_call_group_${callId}.json`), JSON.stringify(result));
    }
    callId++;
  }
});
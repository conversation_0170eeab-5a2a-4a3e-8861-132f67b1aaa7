{"name": "bingo-core", "version": "0.2.0", "description": "Bingo Core Library", "main": "index.js", "scripts": {"build": "rollup -c ./rollup.config.js", "build-lib": "node generation/build-lib-2.js", "build-lib-cmd": "tsc -p tsconfig-lib.json", "test-script": "ts-node simulation/test.ts", "generate-cards": "ts-node generation/generate.ts", "simulate-career": "ts-node ./simulation/simulateCareer.ts", "debug-one-round": "ts-node ./simulation/debugOneRound.ts", "debug-one-seed": "ts-node ./simulation/debugOneSeed.ts"}, "repository": {"type": "git", "url": "ssh://git@***********:2222/limingyang/bingo-core.git"}, "author": "", "license": "ISC", "devDependencies": {"@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "@rollup/plugin-typescript": "^8.1.0", "@tsconfig/node12": "^1.0.7", "@types/lodash": "^4.14.168", "@types/mocha": "^8.2.0", "@types/pg": "^7.14.9", "@types/progress": "^2.0.3", "@typescript-eslint/eslint-plugin": "^4.14.0", "@typescript-eslint/parser": "^4.14.0", "chalk": "^4.1.0", "crypto": "^1.0.1", "eslint": "^7.18.0", "lodash": "^4.17.20", "md5-file": "^5.0.0", "mocha": "^8.2.1", "pg": "^8.5.1", "progress": "^2.0.3", "rollup": "^2.37.1", "ts-node": "^9.1.1", "tslib": "^2.1.0", "typescript": "^4.1.3", "winston": "^3.11.0", "xlsx": "^0.16.9", "dayjs": "^1.11.0"}, "peerDependencies": {"lodash": "^4.17.20"}}
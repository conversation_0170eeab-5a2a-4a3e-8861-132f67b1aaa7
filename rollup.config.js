import typescript from '@rollup/plugin-typescript';
import { nodeResolve } from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';

const configForClient = {
  input: 'src/index.ts',
  output: {
    dir: 'compiled',
    format: 'commonjs',
  },
  plugins: [typescript({target: "es5"}), nodeResolve(), commonjs()],
};

// const configForServer = {
//   input: 'src/index.ts',
//   output: {
//     dir: 'compiled/es6',
//     format: 'commonjs',
//   },
//   plugins: [typescript(), nodeResolve(), commonjs()],
// };

export default configForClient;

import {promises as fs} from 'fs';
import _ from 'lodash';
import path from 'path';
import assert from 'assert';
import {CallPool} from '../src';

type CallPoolProfile = {
  ruleId: number;
  callSeed: number;
  path: string;
}

class CardPoolLoader {
  static _poolPath = path.resolve(__dirname, './pools')
  static _manifest: Record<number | string, CallPoolProfile[]> = {}

  private static async ensureManifest(ruleId: number) {
    if (this._manifest[ruleId]) return;

    const rulePath = path.join(this._poolPath, ruleId.toString());

    _(await fs.readdir(rulePath, {withFileTypes: true})).forEach((dirent) => {
      if (!dirent.isFile()) return;
      const matchResult = dirent.name.match(/rule_(\d+)_call_(group_)?(\d+).json/);
      if (!matchResult) return;
      const [, ruleIdStr, , callSeedStr] = matchResult;
      assert(ruleIdStr, callSeedStr);
      if (!this._manifest[ruleIdStr]) this._manifest[ruleIdStr] = [];

      /** @type {CallPoolProfile} */
      const profile = {
        ruleId: _.toInteger(ruleIdStr),
        callSeed: _.toInteger(callSeedStr),
        path: dirent.name
      };
      this._manifest[ruleIdStr].push(profile);
    });
  }

  static async randomOneCall(ruleId: number): Promise<CallPool> {
    await this.ensureManifest(ruleId);
    assert(this._manifest[ruleId]);

    let randomCall: CallPoolProfile | undefined;
    const callJsonsThisRule = this._manifest[ruleId];
    if (!callJsonsThisRule) randomCall = _.sample(this._manifest[101]);
    else randomCall = _.sample(callJsonsThisRule);

    assert(randomCall, 'Invalid rule Id');
    const callPool = (await import(path.resolve(this._poolPath, ruleId.toString(), randomCall.path))).default;

    return callPool;
  }

  static async getPoolOfCallSeed(ruleId: number, callSeed: number): Promise<CallPool> {
    const targetJson = `rule_${ruleId}_call_${callSeed}.json`;

    let exists = false;
    try {
      await fs.access(path.resolve(this._poolPath, ruleId.toString(), targetJson));
      exists = true;
    } catch (err) {
      console.log(err);
    }

    if (!exists) { // fall back to rule 101
      const fallbackJson = `rule_101_call_${callSeed}.json`;
      try {
        await fs.access(path.resolve(this._poolPath, ruleId.toString(), fallbackJson));
      } catch (err) {
        throw new Error('Invalid call seed.');
      }

      const callPool = (await import(path.resolve(this._poolPath, ruleId.toString(), fallbackJson))).default as CallPool;
      return callPool;
    }

    const callPool = (await import(path.resolve(this._poolPath, ruleId.toString(), targetJson))).default as CallPool;
    return callPool;
  }
}

export default CardPoolLoader;

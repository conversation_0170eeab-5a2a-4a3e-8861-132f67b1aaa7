
// let fsx = require('fs-extra')
// let path = require('path')

import * as fs from 'fs';
import * as path from 'path';
import * as pg from 'pg';

const arg_list = process.argv;

interface dbCfg {
    url: string
}

export default class MyPg {
    public client: pg.Client | null = null
    private _cfg: dbCfg | null = null

    constructor(config: dbCfg) {
      this._cfg = config;
    }

    /**
     * connect
     */
    public async connect (cfg: dbCfg | null = null, reConnect = false) {
        cfg != null && (this._cfg = cfg);
        if (this._cfg == null) {
            console.error("connect cfg is null!");
            return;
        }

        reConnect = reConnect || false;
        if (this.client && !reConnect) {
            return this.client;
        }
        this.client = new pg.Client(this._cfg.url);
        await this.client.connect();
        console.log('My pg connected');
    }

    /**
     * executeSql
     */
    public async executeSql (sqlCmd: string) {
        this.client == null && await this.connect();

        const upperCaseSqlCmd = sqlCmd.toUpperCase();
        if ((upperCaseSqlCmd.indexOf("UPDATE ") !== -1 || upperCaseSqlCmd.indexOf("DELETE ") !== -1) && upperCaseSqlCmd.toUpperCase().indexOf("WHERE ") === -1) {
            throw new Error(sqlCmd + " invalid sqlCmd");
        }
       
        let result = null;


        result = await this.client!.query(sqlCmd);
        const item = upperCaseSqlCmd.indexOf("SELECT ") !== -1 ? result.rows : result;
        // console.log("exec sql complete " + JSON.stringify(result));
        return item;

        // try {
        //     result = await this.client!.query(sqlCmd);
        //     const item = upperCaseSqlCmd.indexOf("SELECT ") !== -1 ? result.rows : result;
        //     // console.log("exec sql complete " + JSON.stringify(result));
        //     return item;
        // } catch (e) {
        //     if (this.client!['_ending']) {
        //         await this.connect(null, true);
        //     }
        //     throw e;
        // }
    }
}



import _ from 'lodash';
import {
  CallPool,
  CardFate,
  CardIndexBetIndexMap,
  CardPickingOption,
  CardSeedGroupRecord,
  CardSeedRecord,
  CellEffect,
  getRandomIndexes,
  IAmountPerCardWeight,
  ICardPreset,
  IRoundHostPreset,
  IRoundHostSnapshot,
  IRoundProgressPreset,
  IRoundProgressSnapshot,
  IShadowCardCountWeight,
  NaturalBingoFlag,
  pickCards,
  pickRandomItems,
  pickRandomItemsWithMutation,
  PlayerRoundProgress,
  PowerupId,
  randomOneItemByWeights,
  RandomSequence,
  RoundHost,
  SmartValue
} from '../src';
import {hostOneRound, hostOneRoundNew} from './hostOneRound';
import assert from 'assert';
import chalk from 'chalk';
import path from 'path';
import fs from 'fs';
import CardPoolLoader from './CardPoolLoader';
import {RoundDebugOption} from './debugOneRound';
import {Card, CardEffect, EffectTuple} from "../lib";
import {logger} from "../src/CoreUtils2";
import {getBonusGameById} from "../src/bonus";

const log = (label: string, content = '') => {
  console.log(`\x1b[44m ${label} \x1b[0m ${content}`);
};
const logTable = (label: string, content: any, title = '') => {
  console.log(`\x1b[44m ${label} \x1b[0m ${title}`);
  console.table(content);
};

const constants = {
  pgUrl: '*********************************************/bingo',
  levelTableName: 'CFGBingoLevel',
  roomTableName: 'CFGBingoRoomList',
  extraBingoTableName: 'CFGBingoExtraBingoStages',
  roomNormalBetTableName: 'cfg_public.bingo_cfg_room_normal_bet',
  roomSpecialBetTableName: 'cfg_public.bingo_cfg_room_special_bet',
  betPresetTableName: 'CFGBingoBetPreset',
  betPresetSequenceTableName: 'CFGBingoBetPresetSequence',
  roomCollectionTableName: 'CFGBingoRoomCollection',
  itemGroupsTableName: 'CFGBingoItemGroups',
  callTableName: 'bingo_cfg_call',
  cardTableName: 'bingo_cfg_card',
  paramTableName: 'CFGBingoParam',
};

export interface ICareerConfig {
  initialTicketCount: number;
  maxRoundToPlay: number;
  cardCount: -1 | 1 | 2 | 3 | 4;
  betOption: -1 | 0 | 1 | 2 | 3;
  roomId: number;
  collectRoomId: number;
  randomRepeatCollection: number;
  isRandomDaub: number;
}

export interface IExpDeterminable {
  '总涂抹次数': number,
  'exp*2点击': number,
  'bingo卡片数': number,
}

interface IRewardDeterminable {
  '总涂抹次数': number,
  'exp*2点击': number,
  'bingo卡片数': number,
  'bingo阴影卡数': number,
  '宝箱点击': number,
  'bingo票点击': number,
  'bingo票*2点击': number,
}

interface IReward {
  collectionCount: number;
  exp: number;
  ticket: number;
}

interface ILevelConfig {
  level: number,
  total_exp: number,
  card_bet_unlock: number,
  level_up_reward: any[],
}

interface IParamsConfig {
  location: number,
  num1: string,
  num2: string,
  num3: string,
}


interface IRoomConfig {
  zone_id: number,
  level_unlock_requirement: { player_level: number, stars: number },
  game_rule_id: string,
  room_type: number,
  daub_exp: string,
  bingo_exp: string,
  box_item_id: string,
  extra_ticket: string,
  big_win_param: string,
  bingo_reward: string,
  bingo_cards: number,
  compensate_card: { card: number, weight: number }[][];
  max_call: string,
  call_interval: string,
  powerups_cd: string,
  powerups_charge: string,
  bad_bingo_cd: string,
  shadow_card_modify: CardIndexBetIndexMap<IShadowCardCountWeight[]>;
  initial_box: CardIndexBetIndexMap<IAmountPerCardWeight[]>;
  initial_ticket: CardIndexBetIndexMap<IAmountPerCardWeight[]>;
  powerups: string;
  powerup_quality_weight: { powerUpsIdx: number[], quality: { quality: number, weight: number }[][] };
  powerup_weight: { powerup: number, weight: number }[];
  powerups_min_num: { weight: number, min_num: { powerup: number, num: number }[] }[];
  powerup_click_weight: CardIndexBetIndexMap<{ powerup: number, weight: number }[]>;
  powerup_extra_bingo: string;
  special_transform_id: string;
  powerup_replace: Record<PowerupId, { be_replaced_rate: number, replace_weight: number }>;
  bet_preset_id: number;
  sequence_id: number;
  shadow_card_bingo_confirm: { card_num: number, shadow_card_min: number[], shadow_card_bingo: number[] }[];
  bonus_id: number;
}

type ValuePerBet<T> = [whenBet0: T, whenBet1: T, whenBet2: T, whenBet3: T]
type ValuePerCardCountPerBet<T> = [
  whenCount0: ValuePerBet<T>,
  whenCount1: ValuePerBet<T>,
  whenCount1: ValuePerBet<T>,
  whenCount1: ValuePerBet<T>
]

type CellEffectInitializationCfg = {
  effect_type: number;
  effect_value: number;
  count: SmartValue;
  daub_rate: SmartValue;
}[]

type BetPresetCfg = {
  id: number;
  bet_range: ValuePerCardCountPerBet<number>;
  box_item_id: ValuePerCardCountPerBet<number>;
  initialization_cookie: CellEffectInitializationCfg;
  max_cookie_total: ValuePerCardCountPerBet<number>;
  initialization_topaz: CellEffectInitializationCfg;
  max_topaz_total: ValuePerCardCountPerBet<number>;
}

type ExtraBingoCfg = {
  sid: number;
  rates: number[][];
}

export type BetPresetSequenceCfg = {
  id: number;
  sequence: [fromLevel: number, presetId: number][];
}

export type RoomCollectionCfg = {
  room_id: number;
  collect_phase: [phase1: number, phase2: number, phase3: number];
  collect_reward_1: IItemInfo[];
  collect_reward_2: IItemInfo[];
  collect_reward_3: IItemInfo[];
};

interface IItemInfo {
  value: string | number,
  item_type: number,
  item_id?: number,
}

interface IRandomItemInfo {
  weight: number | string,
  value: string | number,
  item_type: number,
  item_id?: number,
}

interface IItemGroupConfig {
  item_group_id: string,
  include_item: IRandomItemInfo[],
}

interface IRoomCollectionMap {
  [collectionId: number]: number,
}

interface ILog {
  '关卡ID': number,
  '游戏局数': number,
  '玩家等级': number,
  '本局卡片数': number,
  '本局bingo数': number,
  '阴影卡bingo数': number,
  '生成阴影卡的数量': number,
  '排名': string,
  '消耗bingo票': number,
  '使用powerups': string,
  '剩余bingo票': number,
  '触发补偿卡片个数': number,
  '经验-涂抹': number,
  '经验-宝箱': number,
  '经验-bingo': number,
  '经验翻倍': number,
  '票-涂抹': number,
  '票-宝箱': number,
  '票-bingo': number,
  '票-BigWin': number,
  '票翻倍': number,
  '升级奖励': string,
  '获得宝箱': number,
  '获得收集物': number,
  '收集奖励1': string,
  '收集奖励2': string,
  '收集奖励3': string,
  '总星星数': number,
  '手动涂抹次数': number,
  '选卡阶段': string;
  '小游戏数据': string;
  '随机涂抹个数': number,
  '随机涂抹前bingo数': number,
  '消耗随机涂抹道具数量': number,
  '随机涂抹预期bingo': string,
}

enum ItemType {
  Res = 1,
  Powerup = 2,
  ItemGroup = 3,
  Buff = 4,
  Collection = 7,
  RoomKey = 27,
}

enum ResItemId {
  Exp = 1,
  Ticket = 2,
  Diamond = 3,
  MonopolyNormalDice = 5,
  FreeRound = 10,
  VipPoint = 14,
}

const powerups = [{'id': 1, 'maxCountPerRound': 100, 'effectCellCountPerCard': 1, 'quality': 1}, {
  'id': 2,
  'maxCountPerRound': 100,
  'effectCellCountPerCard': 2,
  'quality': 1
}, {'id': 3, 'maxCountPerRound': 100, 'effectCellCountPerCard': 2, 'quality': 1}, {
  'id': 4,
  'maxCountPerRound': 100,
  'effectCellCountPerCard': 2,
  'quality': 2
}, {'id': 5, 'maxCountPerRound': 1, 'effectCellCountPerCard': 1, 'quality': 2}, {
  'id': 6,
  'maxCountPerRound': 1,
  'effectCellCountPerCard': 1,
  'quality': 3
}, {'id': 7, 'maxCountPerRound': 1, 'effectCellCountPerCard': 1, 'quality': 3}];

export default class PlayerCareer {
  public logs: ILog[] = [];

  ///// init configs /////
  private _careerConfig: ICareerConfig;

  ///// Preloaded configs /////
  private _levelInfos: { [level: number]: ILevelConfig } = {};
  private _roomInfos: { [roomId: number]: IRoomConfig } = {};
  private _betPresets: Record<number, BetPresetCfg> = {};
  private _betPresetSequences: Record<number, BetPresetSequenceCfg> = {};
  private _extraBingoStages: Record<number, ExtraBingoCfg> = {};
  // private _roomNormalBets: Record<number, NormalRoomBetCfg> = {};
  // private _roomSpecialBets: SpecialMapBetCfg[] = [];
  private _roomCollectionCfgs: Record<number, RoomCollectionCfg> = {};
  private _itemGroupInfos: { [itemGroupId: number]: IItemGroupConfig } = {};
  // private _callData: callCfg[] = [];
  // private _cardData: { [callId: string]: cardCfg[] } = {};
  private _noBingoParam: { num1: [number, number], num2: number, num3: number } | null = null;
  private _mustBingoCardsParam: number[] = [];
  private _insertionParam: number[] = [];
  private _extraPowerupInsertionParam: number[] = [];
  private _doubleTicketPowerupNoBingoParam: number = null!;
  private _pickingParams: Pick<CardPickingOption, 'phaseDefinition' | 'pickChance' | 'limits' | 'retryCount' | 'daubOnSameCallTolerance'> = null!;
  private _currentCallPool: CallPool = null!;

  ///// User States /////
  private _totalExp = 0;
  private _currentLevel = 1;
  private _currentBingoTicket = 0;
  private _currentRoundConfig: any;
  private _userBonusGameInfo: any = null;
  private _currentRoomId = 1;
  private _collections: { [roomId: number]: IRoomCollectionMap } = {};
  private _playedRoundsInRoom: { [roomId: number]: number } = {};
  private _totalRoundPlayed = 0;
  private _totalCardPlayed = 0;
  private _canPlayAhead = true;
  private _lastRewardClaimedLevel = 1;
  private _collectionRewardClaimdMap: { [roomId: number]: { [phase: number]: boolean } } = {};
  private _availableCollectionRewards: { roomId: number, phase: number }[] = [];
  private _totalStars = 0;
  private _noBingoCardCount = 0;
  private _noBingoRoundCount = 0;
  private _thisRoundLog: ILog = PlayerCareer.newLog();

  // debug
  private _totalNaturalBingoFlags: boolean[] = []
  private _curNaturalBingoFlags: boolean[] = []
  private _actualBingoFlags: boolean[] = []

  // private _hostPreset: BingoCore.IRoundPreset;
  // private _progressPreset: BingoCore.IRoundProgressPreset;

  constructor(config: ICareerConfig) {
    this._careerConfig = config;
  }

  public async init(): Promise<void> {

    // Bingo.enbaleLog = false;
    // const myPg = new MyPg({url: constants.pgUrl});
    // await myPg.connect();

    const levelsRaw = this.getCfg(constants.levelTableName) as ILevelConfig[];
    this._levelInfos = _(levelsRaw).map(raw => ({
      level: _.toSafeInteger(raw.level),
      total_exp: _.toSafeInteger(raw.total_exp),
      card_bet_unlock: _.toSafeInteger(raw.card_bet_unlock),
      level_up_reward: raw.level_up_reward,
    })).keyBy('level').value();

    const roomsRaw = this.getCfg(constants.roomTableName);
    this._roomInfos = _(roomsRaw).keyBy('room_id').value();

    const betPresetCfgRaw = this.getCfg(constants.betPresetTableName);
    this._betPresets = _(betPresetCfgRaw).keyBy('id').value();

    const extraBingoRaw = this.getCfg(constants.extraBingoTableName);
    this._extraBingoStages = _(extraBingoRaw).keyBy('sid').value();

    const betPresetSequenceCfgRaw = this.getCfg(constants.betPresetSequenceTableName);
    this._betPresetSequences = _(betPresetSequenceCfgRaw).keyBy('id').value();

    const roomCollectionCfgRaw = this.getCfg(constants.roomCollectionTableName);
    this._roomCollectionCfgs = _(roomCollectionCfgRaw).keyBy('room_id').value();

    const itemGroupsRaw = this.getCfg(constants.itemGroupsTableName);
    this._itemGroupInfos = _(itemGroupsRaw).keyBy('item_group_id').value();

    // Other params
    const params = this.getCfg(constants.paramTableName) as IParamsConfig[];
    const noBingoParam = _.find(params, param => param.location === 45);
    assert(noBingoParam);
    this._noBingoParam = {
      num1: JSON.parse(noBingoParam.num1),
      num2: _.toNumber(noBingoParam.num2),
      num3: _.toNumber(noBingoParam.num3),
    };
    const mustBingoParam = _.find(params, param => param.location === 41);
    assert(mustBingoParam);
    this._mustBingoCardsParam = JSON.parse(mustBingoParam.num1);

    const insertionParam = _.find(params, param => param.location === 39);
    assert(insertionParam);
    this._insertionParam = JSON.parse(insertionParam.num1);
    this._extraPowerupInsertionParam = JSON.parse(insertionParam.num2);

    const noBingoDoubleTicketDaubableRateParam = _.find(params, param => param.location === 55);
    assert(noBingoDoubleTicketDaubableRateParam);
    this._doubleTicketPowerupNoBingoParam = _.toNumber(noBingoDoubleTicketDaubableRateParam.num1);

    const param76 = _.find(params, p => p.location === 76)!;
    const param77 = _.find(params, p => p.location === 77)!;
    const param78 = _.find(params, p => p.location === 78)!;
    this._pickingParams = {
      phaseDefinition: JSON.parse(param76.num1),
      pickChance: JSON.parse(param76.num2),
      limits: JSON.parse(param76.num3),
      retryCount: _.toSafeInteger(param78.num1),
      daubOnSameCallTolerance: [_.toSafeInteger(param77.num1), JSON.parse(param77.num2), JSON.parse(param77.num3)]
    };

    // init states
    this._currentBingoTicket = this._careerConfig.initialTicketCount;
    this._currentRoomId = 1;
  }

  async debugARound(option: RoundDebugOption): Promise<{ hostPreset: IRoundHostPreset, progressPreset: IRoundProgressPreset, progress: PlayerRoundProgress }> {
    const hostPreset = this.getHostPreset(option.ruleId, option.callSeed);
    const callPool = await CardPoolLoader.getPoolOfCallSeed(option.ruleId, option.callSeed);
    // const naturalBingoCards = _.filter(callPool.cards, c => c.bingoAt < hostPreset.maxCallCount);
    // const nonNaturalBingoCards = _.filter(callPool.cards, c => c.bingoAt > hostPreset.maxCallCount);
    // assert(naturalBingoCards.length * nonNaturalBingoCards.length > 0);

    const cardPresets: ICardPreset[] = _.map(option.cardSeeds, seed => {
      const record: CardSeedRecord | undefined = _.find(callPool.cards, record => record.seed === seed);
      assert(record);
      return record;
    });

    const progressPreset = this.getProgressPreset(cardPresets, option.betOptionIndex);
    progressPreset.powerupBoxSeed = option.powerupBoxSeed;

    const {progress} = hostOneRound(hostPreset, [progressPreset], true);

    const progressForPrediction = new PlayerRoundProgress(new RoundHost(undefined, hostPreset), undefined, progressPreset);

    // 仅从卡片判断的Bingo类型
    const cardPresetPredictions = _(progressForPrediction.cards).map(card => {
      if (card.naturalBingo === NaturalBingoFlag.Yes) return chalk.black.bgGreen(' NATURAL BINGO ');
      return chalk.white.bgRed(' NON-NATURAL BINGO ');
    }).join(' ');
    log(' PRESET PREDICTION ', cardPresetPredictions);

    log(' POWERUP SEED ', progressPreset.powerupBoxSeed.toString());

    // powerup 预测
    const fates = _.map(progressForPrediction.powerupBox.cardFates, fate => {
      if (fate === CardFate.NoChange) return chalk.black.bgGrey(' NO CHAGNE ');
      else if (fate === CardFate.ExtraBingoByDaubOnce) return chalk.black.bgGreen(' DAUB 1 ');
      else if (fate === CardFate.ExtraBingoByDaubTwice) return chalk.black.bgGreen(' DAUB 2 ');
      return chalk.black.bgGreen(' FORCED BINGO ');
    }).join(' ');
    log(' PWOERUP PREDICTION ', fates);

    return {
      hostPreset: hostPreset,
      progressPreset: progressPreset,
      progress: progress
    };
  }

  private async playARound() {
    console.log('\n=====================');

    this._currentRoomId = this.getFarestAvailableRoomId();
    this._thisRoundLog = PlayerCareer.newLog();

    const cardCountOptionIndex = this.getCardCountOptionIndex();
    const cardCount = cardCountOptionIndex + 1;
    const betOptionIndex = this.determineBetOptionIndex();

    const roundConfig = {
      powerup_max_cnt: 8,
      room_id: this._currentRoomId,
      player_card_cnt: cardCount,
      total_card_cnt: 40,
      bet_index: betOptionIndex,
      powerups_cold_time: 15,
      powerups_charge_cnt: 3,
    };
    this._currentRoundConfig = roundConfig;

    // Pick a call seed
    const ruleId = _.toInteger(this._roomInfos[this._currentRoomId].game_rule_id);
    const randomCallPool = await CardPoolLoader.randomOneCall(ruleId);
    this._currentCallPool = randomCallPool;
    const randomCallSeed = this._currentCallPool.callSeed;
    const hostPreset = this.getHostPreset(ruleId, randomCallSeed);
    const hostBingoCount = 41;
    // const hostBingoCount = 4;
    const playerPresets = this.determineClusterPresets(hostPreset, cardCount, hostBingoCount);


    const progressPresets = _.map(playerPresets, (cardPresets, index) => {
      const isMainPlayer = index === 0;
      const progressPreset = this.getProgressPreset(cardPresets, isMainPlayer ? betOptionIndex : 0);
      return progressPreset;
    });
    const progressForPrediction = new PlayerRoundProgress(new RoundHost(undefined, hostPreset), undefined, progressPresets[0]);

    // 仅从卡片判断的Bingo类型
    const cardPresetPredictions = _(progressForPrediction.cards).map(card => {
      if (card.naturalBingo === NaturalBingoFlag.Yes) return chalk.black.bgGreen(' NATURAL BINGO ');
      return chalk.white.bgRed(' NON-NATURAL BINGO ');
    }).join(' ');
    log(' PRESET PREDICTION ', cardPresetPredictions);

    log(' POWERUP SEED ', progressPresets[0].powerupBoxSeed.toString());

    // powerup 预测
    const fates = _.map(progressForPrediction.powerupBox.cardFates, fate => {
      if (fate === CardFate.NoChange) return chalk.black.bgGrey(' NO CHAGNE ');
      else if (fate === CardFate.ExtraBingoByDaubOnce) return chalk.black.bgGreen(' DAUB 1 ');
      else if (fate === CardFate.ExtraBingoByDaubTwice) return chalk.black.bgGreen(' DAUB 2 ');
      return chalk.black.bgGreen(' FORCED BINGO ');
    }).join(' ');
    log(' POWERUP PREDICTION ', fates);

    this.cost();
    if (!this._canPlayAhead) {
      log(' CAREER STOP ', 'Ran out of bingo ticket');
      return;
    }

    ///// Play A Round /////
    log(' PLAY A ROUND ', `RoomId: ${this._currentRoomId} CallSeed: ${randomCallSeed}, CardSeeds: [${_(progressPresets[0].cardPresets).map(c => c.seed).join(', ')}]`);

    // const playerStatistics = BingoCore.playARound(hostPreset, progressPreset);
    const host = new RoundHost(undefined, hostPreset);
    const progresses = _.map(progressPresets, p => host.tryJoinOnePlayerLegacy(p) as PlayerRoundProgress);
    logger.info('hostOneRoundStart', {progressPresets});
    //修正阴影卡位置
    const finalShadowCardsCount = this.modifyShadowCardEffect(progresses[0].cards);
    const {reward: playerStatistics} = hostOneRoundNew(hostPreset, progresses, host, false, !!this._careerConfig.isRandomDaub);

    ///// Settlement /////
    const roundReward = this.gatherRoundReward(playerStatistics);
    this.gainReward(roundReward);
    logTable(' ROUND REWARD ', roundReward);

    const levelUpAndCollectionReward = this.claimLevelUpAndCollectionReward();
    this.gainReward(levelUpAndCollectionReward);

    // 记录
    if (playerStatistics['bingo卡片数'] <= 0) {
      this._noBingoCardCount += cardCount;
      this._noBingoRoundCount += 1;
    } else {
      this._noBingoCardCount = 0;
      this._noBingoRoundCount = 0;
    }
    this._totalRoundPlayed += 1;
    this._totalCardPlayed += cardCount;
    _.update(this, ['_playedRoundsInRoom', this._currentRoomId], playedRound => (playedRound || 0) + 1);
    this._actualBingoFlags.push(...playerStatistics.actualBingos);

    // Record log
    this._thisRoundLog['关卡ID'] = this._currentRoomId;
    this._thisRoundLog['游戏局数'] = this._totalRoundPlayed;
    this._thisRoundLog['玩家等级'] = this._currentLevel;
    this._thisRoundLog['本局卡片数'] = this._currentRoundConfig.player_card_cnt;
    this._thisRoundLog['本局bingo数'] = playerStatistics['bingo卡片数'];
    this._thisRoundLog['阴影卡bingo数'] = playerStatistics['bingo阴影卡数'];
    this._thisRoundLog['生成阴影卡的数量'] = finalShadowCardsCount;
    this._thisRoundLog['排名'] = _.join(playerStatistics['排名'], ',');
    this._thisRoundLog['剩余bingo票'] = this._currentBingoTicket;
    this._thisRoundLog['获得收集物'] = roundReward.collectionCount;
    this._thisRoundLog['获得宝箱'] = playerStatistics['宝箱点击'];
    this._thisRoundLog['使用powerups'] = _.join(playerStatistics['Powerups'], ',');
    this._thisRoundLog['手动涂抹次数'] = playerStatistics['手动涂抹次数'];
    logger.info('BonusGameStart', {progressPresets});
    const betOptions = this.resolveBetOptions(this._currentLevel, this._currentRoomId);
    this._thisRoundLog['小游戏数据'] = this.generateBonusGameInfo(progresses[0], betOptions[cardCount - 1][betOptionIndex] * cardCount);

    //添加随机涂抹统计
    this._thisRoundLog['随机涂抹个数'] = playerStatistics['随机涂抹个数'];
    this._thisRoundLog['随机涂抹前bingo数'] = playerStatistics['随机涂抹前bingo数'];
    if (playerStatistics['随机涂抹个数'] > 0) {
      const daubBetOption = this.resolveRandomDaubBetOptions(this._currentLevel);
      const ticketCost = betOptions[cardCount - 1][betOptionIndex] * cardCount;
      const presetBet = daubBetOption[daubBetOption.length - 1][daubBetOption[daubBetOption.length - 1].length - 1] * 4;
      this._thisRoundLog['消耗随机涂抹道具数量'] = Math.ceil(ticketCost / presetBet * (presetBet / 100));
    }
    this._thisRoundLog['随机涂抹预期bingo'] = progresses[0].overRandomDaubFlag.join(',');

    this.logs.push(this._thisRoundLog);

    const actualBingos = _.map(playerStatistics.actualBingos, flag => {
      if (flag) return chalk.black.bgGreen(' BINGO ');
      return chalk.white.bgRed(' NO BINGO ');
    }).join(' ');
    log(' ACTURAL BINGO ', actualBingos);
    log(' NATURAL PROVIDE RATE ', `${_.filter(this._totalNaturalBingoFlags, f => f).length} / ${this._totalNaturalBingoFlags.length}`);
    log(' ACTUAL BINGO RATE ', `${_.filter(this._actualBingoFlags, f => f).length} / ${this._actualBingoFlags.length}`);
  }

  generateBonusGameInfo(progress: PlayerRoundProgress, ticketCost: number): string {
    const bonusId = this._roomInfos[this._currentRoomId]['bonus_id'];
    const BonusGame = getBonusGameById(this._roomInfos[this._currentRoomId]['bonus_id']);
    if (!BonusGame) {
      return "";
    }
    const {config, checkConfig} = this.getBonusCfg(bonusId);

    const extraParam = this.getBonusExtraParam(bonusId);
    const bonus = new BonusGame(progress, ticketCost, this._userBonusGameInfo, config, this._currentLevel, extraParam);
    const result = bonus.generateSimulationResult(checkConfig);
    this._userBonusGameInfo = bonus.userGameInfo;
    return result;
  }

  private static newLog(): ILog {
    return {
      '关卡ID': 0,
      '游戏局数': 0,
      '玩家等级': 0,
      '本局卡片数': 0,
      '本局bingo数': 0,
      '阴影卡bingo数': 0,
      '生成阴影卡的数量': 0,
      '排名': '',
      '消耗bingo票': 0,
      '使用powerups': '',
      '剩余bingo票': 0,
      '触发补偿卡片个数': 0,
      '经验-涂抹': 0,
      '经验-宝箱': 0,
      '经验-bingo': 0,
      '经验翻倍': 1,
      '票-涂抹': 0,
      '票-宝箱': 0,
      '票-bingo': 0,
      '票-BigWin': 0,
      '票翻倍': 1,
      '升级奖励': '',
      '获得宝箱': 0,
      '获得收集物': 0,
      '收集奖励1': '',
      '收集奖励2': '',
      '收集奖励3': '',
      '总星星数': 0,
      '手动涂抹次数': 0,
      '选卡阶段': '',
      '小游戏数据': '',
      '随机涂抹个数': 0,
      '随机涂抹前bingo数': 0,
      '消耗随机涂抹道具数量': 0,
      '随机涂抹预期bingo': '',
    };
  }

  private modifyShadowCardEffect(cards: any) {
    const {bet_index, player_card_cnt} = this._currentRoundConfig;
    const curIdx = player_card_cnt - 1;
    const roomCfg = this._roomInfos[this._currentRoomId];
    const noShadowCardsIndex: number[] = [];
    const shadowCardsIndex: number[] = [];
    let finalShadowCardsCount = 0;
    _.forEach(cards, card => {
      if (card.hasEffect(CardEffect.CollectionReward)) {
        finalShadowCardsCount++;
        if (this._curNaturalBingoFlags[card.index]) {
          shadowCardsIndex.push(card.index);
        }
      } else if (!this._curNaturalBingoFlags[card.index]) {
        noShadowCardsIndex.push(card.index);
      }
    });
    const shadowCardBingoConfirm = roomCfg.shadow_card_bingo_confirm;
    if (shadowCardBingoConfirm) {
      for (const index of shadowCardsIndex) {
        const card = cards[index] as Card;
        const rs = new RandomSequence(card.seed);
        //不满足判断 bingo阴影卡 移到 其他没有阴影卡的上面
        if (rs.nextDouble() > shadowCardBingoConfirm[curIdx].shadow_card_bingo[bet_index]) {
          _.pullAllWith(card.effects, [CardEffect.CollectionReward], ([t1,], t2) => t1 === t2);
          if (noShadowCardsIndex.length > 0) {
            const idx = rs.nextIntegerInRange(noShadowCardsIndex.length - 1, 0);
            const newIndex = noShadowCardsIndex[idx];
            cards[newIndex].effects.push([CardEffect.CollectionReward, 0]);
            noShadowCardsIndex.splice(idx, 1);
          }
        }
      }
      //检查当前阴影卡个数是否满足条件
      const finalShadowCards = _.filter(cards, card => card.hasEffect(CardEffect.CollectionReward));
      finalShadowCardsCount = finalShadowCards.length;
      let diffCardsCount = shadowCardBingoConfirm[curIdx].shadow_card_min[bet_index] - finalShadowCardsCount;
      if (diffCardsCount > 0) {
        for (const card of cards) {
          if (!card.hasEffect(CardEffect.CollectionReward)) {
            card.effects.push([CardEffect.CollectionReward, 0]);
            diffCardsCount--;
            finalShadowCardsCount++;
          }
          if (diffCardsCount <= 0) {
            break;
          }
        }
      }
    }
    return finalShadowCardsCount;
  }

  private cost(): void {
    const roomId = this._currentRoomId;
    const {bet_index, player_card_cnt} = this._currentRoundConfig;
    const betIndex = bet_index;

    const betOptions = this.resolveBetOptions(this._currentLevel, roomId);
    const actualBet = betOptions[player_card_cnt - 1][betIndex];

    const ticketCost = _.toSafeInteger(actualBet * player_card_cnt);

    if (this._currentBingoTicket < ticketCost) {
      this._canPlayAhead = false;
      return;
    }

    this._currentBingoTicket -= ticketCost;
    this._thisRoundLog['消耗bingo票'] = ticketCost;
    log('TICKET COST', ticketCost.toString() + ';' + this._currentBingoTicket);
  }

  private resolveBetOptions(level: number, roomId: number): ValuePerCardCountPerBet<number> {
    const presetId = this.resolvePresetId(level, roomId);
    const preset = this._betPresets[presetId];
    return preset.bet_range;
  }

  private resolvePreset(level: number, roomId: number): BetPresetCfg {
    const presetId = this.resolvePresetId(level, roomId);
    return this._betPresets[presetId];
  }

  private resolvePresetId(level: number, roomId: number): number {
    const roomCfg = this._roomInfos[roomId];
    let presetId: number;
    if (roomCfg.room_type === 4) {
      const presetSequence = this._betPresetSequences[roomCfg.sequence_id];
      assert(presetSequence, `Invalid preset sequence id: ${roomCfg.sequence_id}`);

      const targetPair = _.findLast(presetSequence.sequence, ([fromLevel]) => fromLevel <= level);
      assert(targetPair, `Invalid preset sequence(id: ${roomCfg.sequence_id}) for user level: ${level}`);

      presetId = targetPair[1];
    } else {
      presetId = roomCfg.bet_preset_id;
    }
    return presetId;
  }

  private resolveRandomDaubBetOptions(level: number): ValuePerCardCountPerBet<number> {
    const presetSequence = this._betPresetSequences[1];
    assert(presetSequence, `Invalid preset sequence id: 1`);

    const targetPair = _.findLast(presetSequence.sequence, ([fromLevel]) => fromLevel <= level);
    assert(targetPair, `Invalid preset sequence(id: 1) for user level: ${level}`);
    const preset = this._betPresets[targetPair[1]];
    return preset.bet_range;
  }

  private determineClusterPresets(hostPreset: IRoundHostPreset, playerCardCount: number, hostBingoCount: number): ICardPreset[][] {
    const result: ICardPreset[][] = [];
    const playerPresets = this.determinePlayerCardPresets(hostPreset, playerCardCount);
    result.push(playerPresets);
    const npcBingoCount = hostBingoCount - 4;
    if (this._currentCallPool.cards) {
      const naturalBingoCards = _.filter(this._currentCallPool.cards, c => c.bingoAt < hostPreset.maxCallCount);
      const nonNaturalBingoCards = _.filter(this._currentCallPool.cards, c => c.bingoAt > hostPreset.maxCallCount);
      assert(naturalBingoCards.length * nonNaturalBingoCards.length > 0);
      _(npcBingoCount).range().chunk(4)
          .forEach((bingoes) => {
            const npcPresets: ICardPreset[] = _(bingoes.length).range().map(() => {
              const rate = _.random(true);
              if (rate < 0.33) return pickRandomItems(naturalBingoCards, 1)[0];
              else if (rate < 0.67) return pickRandomItems(nonNaturalBingoCards, 1)[0];
              else return pickRandomItems(nonNaturalBingoCards, 1)[0];
            }).value();

            result.push(npcPresets);
          });
    } else if (this._currentCallPool.cardsGroups) {
      _(npcBingoCount).range().chunk(4).forEach((bingoes) => {
        let bingoCount = 0;
        _(bingoes.length).range().forEach(() => {
          const rate = _.random(true);
          if (rate < 0.33) bingoCount++;
        });
        let pools = [] as CardSeedGroupRecord[];
        while (pools.length <= 0) {
          pools = (this._currentCallPool.cardsGroups![(bingoes.length - 1) + ""]).filter(v => v.bingoCount === bingoCount);
          bingoCount--;
        }
        const npcPresets: ICardPreset[] = pickRandomItemsWithMutation<CardSeedGroupRecord>(pools, 1)[0].seed.map(v => {
          return {seed: v};
        });
        result.push(npcPresets);
      });
    }


    return result;
  }

  private determinePlayerCardPresets(hostPreset: IRoundHostPreset, cardCount: number): ICardPreset[] {
    if (this._currentCallPool.cards) {
      const naturalBingoCards = _.filter(this._currentCallPool.cards, c => c.bingoAt < hostPreset.maxCallCount);
      const nonNaturalBingoCards = _.filter(this._currentCallPool.cards, c => c.bingoAt > hostPreset.maxCallCount);
      assert(naturalBingoCards.length * nonNaturalBingoCards.length > 0);
    }
    const roomCfg = this._roomInfos[this._currentRoomId];
    const managedBingoRate = roomCfg.bingo_cards;
    const naturalBingoFlags: boolean[] = _(cardCount).range().map(() => _.random(true) < managedBingoRate).value();

    // 前期必定bingo卡片机制
    const careerCardIndexes = _.range(this._totalCardPlayed, this._totalCardPlayed + cardCount);
    const mustBingoIndexes = _(careerCardIndexes).intersection(this._mustBingoCardsParam).map(careerIndex => careerIndex - this._totalCardPlayed).value();
    _.forEach(mustBingoIndexes, cardIndex => {
      assert(cardIndex < naturalBingoFlags.length);
      naturalBingoFlags[cardIndex] = true;
    });

    // 补偿机制
    if (this._noBingoCardCount >= this._noBingoParam!.num1[1] && this._noBingoRoundCount >= this._noBingoParam!.num1[0]) {
      const thisTimeNoBingoCardCount = _.filter(naturalBingoFlags, flag => !flag).length;
      const compensationCountWeights = roomCfg.compensate_card[cardCount - 1];
      if (thisTimeNoBingoCardCount === cardCount) {
        const totalNoBingoCount = this._noBingoCardCount + thisTimeNoBingoCardCount;
        const {num2, num3} = this._noBingoParam!;
        const compensationRate = totalNoBingoCount * totalNoBingoCount / (totalNoBingoCount + num2) / num3;

        if (_.random(true) < compensationRate) {
          const compensationCount = randomOneItemByWeights(compensationCountWeights).card;
          const compensationIndexes = pickRandomItemsWithMutation(_.range(cardCount), compensationCount);
          this._thisRoundLog['触发补偿卡片个数'] = compensationIndexes.length;
          _.forEach(compensationIndexes, i => {
            naturalBingoFlags[i] = true;
          });
        }
      }
    }

    // const cardPresets: ICardPreset[] = _(naturalBingoFlags)
    //   .map(canNaturalBingo => {
    //     if (canNaturalBingo) return pickRandomItems(naturalBingoCards, 1)[0];

    //     // const canPowerupBingo = _.random(true) < 0.5;
    //     // if (canPowerupBingo) return pickRandomItems(pool.powerupBingo, 1)[0];

    //     return pickRandomItems(nonNaturalBingoCards, 1)[0];
    //   })
    //   .map(record => ({
    //     seed: record.seed,
    //   })).value();

    const pickingOption: CardPickingOption = {
      isNaturalBingos: naturalBingoFlags,
      maxCallCount: hostPreset.maxCallCount,
      // phaseDefinition: [0.2, 0.85, 1.1],
      // pickChance: [[0.5, 0.5], [0.5, 0.5], [0.1, 0.1], [0.15, 0.15]],
      // limits: [[1, 1], [1, 1], [1, 1], [1, 1]],
      // retryCount: 10,
      // daubOnSameCallTolerance: [3, 3, 2],
      ...this._pickingParams
    };
    const pickResult: {
      flags?: string[];
      pickTimes?: number;
    } = {};
    const pickedSeed = pickCards(this._currentCallPool, pickingOption, log, pickResult);
    const cardPresets: ICardPreset[] = _.map(pickedSeed, s => ({seed: s}));

    this._thisRoundLog['选卡阶段'] = pickResult.flags!.join(',');

    this._totalNaturalBingoFlags.push(...naturalBingoFlags);
    this._curNaturalBingoFlags = naturalBingoFlags;

    return cardPresets;
  }

  private gainReward(reward: IReward) {
    this.gainExp(reward);
    this.gainTicket(reward);
    this.gainCollection(reward);
  }

  private gainExp(reward: IReward) {
    const {exp} = reward;
    this._totalExp = _.toSafeInteger(this._totalExp + exp);

    let targetLevel = this._currentLevel;
    while (this._levelInfos[targetLevel + 1] && this._totalExp > this._levelInfos[targetLevel + 1].total_exp) {
      targetLevel += 1;
    }

    this._currentLevel = targetLevel;
  }

  private gainTicket(reward: IReward) {
    this._currentBingoTicket += reward.ticket;
  }

  private pickNewCollections(count: number) {
    for (let i = 0; i < count; i++) {
      if (Math.random() < this._careerConfig.randomRepeatCollection) {
        const randomCollectionPseudoId = _.random(1, 12);
        _.update(this._collections, [this._currentRoomId, `"${randomCollectionPseudoId}"`], value => (value || 0) + 1);
      } else {
        const picked = this.pickUniqNewCollections(1);
        _(picked).forEach((id) => {
          _.update(this._collections, [this._currentRoomId, `"${id}"`], value => (value || 0) + 1);
        });
      }
    }
  }

  private gainCollection(reward: IReward) {
    if (reward.collectionCount <= 0) return;
    const collectionCfg = this._roomCollectionCfgs[this._currentRoomId];
    if (!collectionCfg) return;

    this.pickNewCollections(reward.collectionCount);
    const targets = collectionCfg.collect_phase;
    const thisRoomCollectionCount = _.keys(this._collections[this._currentRoomId]).length;
    _([1, 2, 3]).forEach((phase, index) => {
      const phaseTarget = targets[index];
      const reached = thisRoomCollectionCount >= phaseTarget;

      if (reached && !_.get(this._collectionRewardClaimdMap, [this._currentRoomId, phase], false)) {
        this._availableCollectionRewards.push({roomId: this._currentRoomId, phase});
        this._totalStars += 1;
        _.set(this._collectionRewardClaimdMap, [this._currentRoomId, phase], true);
      }
    });
  }

  private claimLevelUpAndCollectionReward(): IReward {
    const result: IReward = {
      collectionCount: 0,
      exp: 0,
      ticket: 0,
    };

    // levels
    let levelUpRewardItems = [] as any[];
    _(_.range(this._lastRewardClaimedLevel, this._currentLevel)).forEach((l) => {
      const levelToClaim = l + 1;
      levelUpRewardItems = _.concat(levelUpRewardItems, this._levelInfos[levelToClaim].level_up_reward);
      this._lastRewardClaimedLevel = levelToClaim;
    });
    this._thisRoundLog['升级奖励'] = JSON.stringify(levelUpRewardItems);

    // collections
    let collectionRewardItems = [] as IItemInfo[];
    const collectionRewardItemsStatistics = [] as IItemInfo[][];
    const collectionCfg = this._roomCollectionCfgs[this._currentRoomId];
    _(this._availableCollectionRewards).forEach(({roomId, phase}) => {
      if (!collectionCfg) return false;
      const key = `collect_reward_${phase}` as 'collect_reward_1' | 'collect_reward_2' | 'collect_reward_3';
      const phaseRewardItems = collectionCfg[key] as IItemInfo[];
      collectionRewardItemsStatistics[phase - 1] = _.concat(collectionRewardItemsStatistics[phase - 1] || [], phaseRewardItems);
      collectionRewardItems = _.concat(collectionRewardItems, phaseRewardItems);
      for (const v of phaseRewardItems) {
        if (v.item_type === ItemType.RoomKey && v.item_id !== this._careerConfig.collectRoomId) {
          this._careerConfig.collectRoomId = _.toSafeInteger(v.item_id);
        }
      }
    });
    this._availableCollectionRewards = [];
    this._thisRoundLog['收集奖励1'] = JSON.stringify(collectionRewardItemsStatistics[0]);
    this._thisRoundLog['收集奖励2'] = JSON.stringify(collectionRewardItemsStatistics[1]);
    this._thisRoundLog['收集奖励3'] = JSON.stringify(collectionRewardItemsStatistics[2]);
    this._thisRoundLog['总星星数'] = this._totalStars;

    const rewardItems = _.concat(levelUpRewardItems, collectionRewardItems);
    rewardItems.length > 0 && logTable('LV & COLLECTION REWARD', rewardItems);

    const claimedReward = PlayerCareer.getRewardFromItemInfos(rewardItems);
    PlayerCareer.combineReward(result, [claimedReward]);

    return result;
  }

  private gatherRoundReward(statistics: IRewardDeterminable): IReward {
    const reward: IReward = {
      collectionCount: 0,
      exp: 0,
      ticket: 0,
    };

    const {bet_index, player_card_cnt, room_id: roomId} = this._currentRoundConfig;
    const betIndex = bet_index;
    const roomInfo = this._roomInfos[roomId];
    const {box_item_id, daub_exp, bingo_exp, bingo_reward, extra_ticket, big_win_param} = roomInfo;
    const presetCfg = this.resolvePreset(this._currentLevel, roomId);
    const betOptions = this.resolveBetOptions(this._currentLevel, roomId);
    const actualBet = betOptions[player_card_cnt - 1][betIndex];
    const boxItemGroupIds = presetCfg.box_item_id;

    ///// Determine Daub & Bingo Reward /////
    const daubCount = statistics['总涂抹次数'];
    const bingoCount = statistics['bingo卡片数'];
    const ticketHitCount = statistics['bingo票点击'];
    const daubExpRate = _.toNumber(daub_exp.split(';')[betIndex]);
    const bingoExpRate = _.toNumber(bingo_exp.split(';')[betIndex]);
    const daubExp = daubCount * daubExpRate * actualBet;
    const bingoExp = bingoCount * bingoExpRate * actualBet;
    reward.exp += daubExp + bingoExp;
    this._thisRoundLog['经验-涂抹'] = daubExp;
    this._thisRoundLog['经验-bingo'] = bingoExp;

    const hitTicketRate = _.toNumber(extra_ticket.split(';')[betIndex]);
    const hitTicketGain = ticketHitCount * hitTicketRate * actualBet;
    const bingoTicketRate = _.toNumber(bingo_reward.split(';')[betIndex]);
    const bingoTicketGain = bingoTicketRate * bingoCount * actualBet;
    reward.ticket += hitTicketGain + bingoTicketGain;
    this._thisRoundLog['票-涂抹'] = hitTicketGain;
    this._thisRoundLog['票-bingo'] = bingoTicketGain;

    ///// Big Win /////
    if (this._currentRoundConfig.player_card_cnt === 4 && bingoCount === 4) {
      let bigWinRate = null;
      const bigWinRates = big_win_param.split(';');
      switch (bet_index) {
        case 2:
          bigWinRate = _.toNumber(bigWinRates[0]);
          break;
        case 3:
          bigWinRate = _.toNumber(bigWinRates[1]);
          break;
      }

      if (bigWinRate !== null) {
        log('BIG WIN');

        const bigWinTicketGain = actualBet * bigWinRate;
        reward.ticket += bigWinTicketGain;
        this._thisRoundLog['票-BigWin'] = bigWinTicketGain;
      }
    }

    ///// Shadow Card Bingo a.k.a Get One Collection /////
    const shadowCardBingoCount = statistics['bingo阴影卡数'];
    reward.collectionCount += shadowCardBingoCount;

    ///// Determine Box Reward /////
    const boxCount = statistics['宝箱点击'];
    const boxItemGroupId = boxItemGroupIds[player_card_cnt - 1][betIndex];
    const {include_item} = this._itemGroupInfos[boxItemGroupId];
    // Parse weights
    _.forEach(include_item, raw => {
      const expression = _.replace(raw.weight as string, /level/g, this._currentLevel.toString());
      raw.weight = _.toNumber(eval(expression));
    });
    // Generate random box reward
    const boxRewardList = _(boxCount).range().map(() => {
      const randomItem = PlayerCareer.getRandomOneInGroup(include_item);
      if (randomItem.value) {
        const expression = _.replace(randomItem.value.toString(), /bet/g, actualBet.toString());
        const actualValue = _.toNumber(eval(expression));
        randomItem.value = actualValue;
      }

      return randomItem;
    }).value();

    const boxReward = PlayerCareer.getRewardFromItemInfos(boxRewardList);
    this._thisRoundLog['票-宝箱'] = boxReward.ticket;
    this._thisRoundLog['经验-宝箱'] = boxReward.exp;
    PlayerCareer.combineReward(reward, [boxReward]);


    ///// Finalize by double factors /////
    const expDoubleFactor = statistics['exp*2点击'] > 0 ? 2 : 1;
    const ticketDoubleFactor = statistics['bingo票*2点击'] > 0 ? 2 : 1;
    reward.exp = _.toSafeInteger(reward.exp * expDoubleFactor);
    reward.ticket = _.toSafeInteger(reward.ticket * ticketDoubleFactor);
    this._thisRoundLog['经验翻倍'] = expDoubleFactor;
    this._thisRoundLog['票翻倍'] = ticketDoubleFactor;

    return reward;
  }

  private static getRewardFromItemInfos(itemList: IItemInfo[]): IReward {
    const reward: IReward = {
      collectionCount: 0,
      exp: 0,
      ticket: 0,
    };

    _(itemList).forEach((randomItem) => {
      switch (randomItem.item_type) {
        case ItemType.Res:
          switch (randomItem.item_id) {
            case ResItemId.Exp:
              reward.exp += randomItem.value as number;
              break;

            case ResItemId.Ticket:
              reward.ticket += randomItem.value as number;
              break;

            case ResItemId.FreeRound: // TODO
            case ResItemId.MonopolyNormalDice:
            case ResItemId.Diamond:
            case ResItemId.VipPoint:
              break;

            default:
              debugger;
              console.log('Unknown res item.');
          }
          break;

        case ItemType.Powerup:
        case ItemType.ItemGroup:
        case ItemType.Buff:
          break;

        case ItemType.Collection:
          reward.collectionCount += 1;
          break;

        default:
          // debugger;
          console.log('Unknown item type.');
      }
    });

    return reward;
  }

  private static combineReward(destination: IReward, rewards: IReward[]) {
    _(rewards).forEach(reward => {
      _.mergeWith(destination, reward, (dest, src) => _.toNumber(dest + src));
    });
  }

  private static getRandomOneInGroup(group: IRandomItemInfo[]): IRandomItemInfo {
    const upperBound = _.sumBy(group, 'weight') - 1;
    const randomValue = _.random(0, upperBound, false);

    let stop = 0;
    for (const item of group) {
      stop += item.weight as number;
      if (randomValue < stop) return item;
    }
    throw new Error('Random one error');
  }

  private getCardCountOptionIndex(): number {
    return this._careerConfig.cardCount === -1 ? _.random(3) : this._careerConfig.cardCount - 1;
  }

  private determineBetOptionIndex(): number {
    const biggest = _.get(this._levelInfos, [this._currentLevel, 'card_bet_unlock'], 4) - 1;
    if (this._careerConfig.betOption === -1) {
      // Random
      return _.random(biggest);
    } else {
      return Math.min(this._careerConfig.betOption, biggest);
    }
    // return biggest;
  }

  private getHostPreset(ruleId: number, callSeed: number): IRoundHostPreset {
    const roomCfg = this._roomInfos[this._currentRoomId];

    return {
      ruleId: ruleId,
      roundStartTime: 0,
      maxCallCount: _.toSafeInteger(roomCfg.max_call),
      callInterval: _.toSafeInteger(roomCfg.call_interval) * 1000,
      totalCardCountRange: {lower: 275, upper: 276},
      maxPlayerCount: 50,
      bingoCountRate: 0.15,
      callSeed: callSeed
    };
  }

  private getProgressPreset(cardPresets: ICardPreset[], betOptionIndex: number): IRoundProgressPreset {
    const roomCfg = this._roomInfos[this._currentRoomId];
    const betOptions = this.resolveBetOptions(this._currentLevel, this._currentRoomId);

    const regionQualityWeights = _.map(roomCfg.powerup_quality_weight.powerUpsIdx, (fromIndex, i, collection) => {
      return {
        fromIndex: fromIndex,
        toIndexExclusive: collection[i + 1] || Number.MAX_SAFE_INTEGER,
        weights: roomCfg.powerup_quality_weight.quality[i],
      };
    });

    const powerupWeights = _.map(roomCfg.powerup_weight, (cfg) => ({
      powerupId: cfg.powerup,
      weight: cfg.weight,
    }));

    const minimumAmountRequirements = _.map(roomCfg.powerups_min_num, cfg => ({
      weight: cfg.weight,
      requirement: _.map(cfg.min_num, r => ({
        powerupId: r.powerup,
        amount: r.num,
      }))
    }));

    const powerupEffectDaubableRates = _.map(roomCfg.powerup_click_weight, c => _.map(c, b => _.map(b, cfg => ({
      powerupId: cfg.powerup,
      rate: cfg.weight,
    }))));


    const powerupReplacements = roomCfg.special_transform_id === '' ?
        [] as { originalId: number, targetId: number }[] :
        _(JSON.parse(roomCfg.special_transform_id)).toPairs().map(([originalId, targetId]) => ({
          originalId: _.toSafeInteger(originalId),
          targetId: _.toSafeInteger(targetId)
        })).value();

    const steps = _.split(roomCfg.powerup_extra_bingo, ';');
    const powerupCausedExtraBingoRate = {
      [PowerupId.DaubOnce]: _.toNumber(steps[0]),
      [PowerupId.DaubTwice]: _.toNumber(steps[1]),
      [PowerupId.ForcedBingo]: _.toNumber(steps[2]),
    };

    const cellEffectInitializationMaps = [
      {effect: CellEffect.TicketReward, map: roomCfg.initial_ticket},
      {effect: CellEffect.BoxReward, map: roomCfg.initial_box}
    ];

    const powerupBalanceReplacements = _.mapValues(roomCfg.powerup_replace, r => ({
      beReplacedRate: r.be_replaced_rate,
      replaceWeight: r.replace_weight
    }));

    const powerupBalances = {1: 10, 2: 10, 3: 10, 4: 10, 5: 10, 6: 10, 7: 10};

    const progressPreset: IRoundProgressPreset = {
      joinRoundTime: 0,
      cardCountOptionIndex: cardPresets.length - 1,
      betOptionIndex,
      cardPresets,
      cardCountOptions: [1, 2, 3, 4],
      powerupBoxSeed: _.random(65536),
      betOptions: betOptions[cardPresets.length - 1],
      powerupsCooldownTime: _.toSafeInteger(roomCfg.powerups_cd),
      daubCountToChargePowerups: _.toSafeInteger(roomCfg.powerups_charge),
      badBingoPenaltyTime: _.toSafeInteger(roomCfg.bad_bingo_cd),
      collectionRewardInitializationMap: roomCfg.shadow_card_modify,
      cellEffectInitializationMaps,
      powerupPresets: powerups,
      regionQualityWeights: regionQualityWeights,
      powerupWeights: powerupWeights,
      powerupMinimumAmountRequirements: minimumAmountRequirements,
      powerupEffectDaubableRateMap: powerupEffectDaubableRates,
      powerupCausedExtraBingoRate: powerupCausedExtraBingoRate as { [PowerupId.DaubOnce]: number, [PowerupId.DaubTwice]: number, [PowerupId.ForcedBingo]: number },
      powerupReplacements: powerupReplacements,
      powerupCorrectionEndIndexByCardCountOptionIndex: this._insertionParam,
      extraBingoPowerupSwapIndexByCardCountOptionIndex: this._extraPowerupInsertionParam,
      powerupBalanceReplacements,
      powerupBalances,
      doubleTicketPowerupNoBingoFactor: this._doubleTicketPowerupNoBingoParam,
      randomDaubBingoConfig: this._extraBingoStages[this._currentRoomId] ? this._extraBingoStages[this._currentRoomId].rates : null
    };

    this.generateBonusGameInit(cardPresets, betOptions, betOptionIndex);

    return progressPreset;
  }

  public generateBonusGameInit(cardPresets: ICardPreset[], betOptions: number[][], betOptionIndex: number) {
    const roomCfg = this._roomInfos[this._currentRoomId];
    switch (roomCfg.bonus_id) {
      case 9: {
        const {config} = this.getBonusCfg(roomCfg.bonus_id);
        if (config) {
          let curCfg = (config.filter((v: any) => _.toNumber(v.Lv) === this._currentLevel));
          if (curCfg.length <= 0 && this._currentLevel > config[config.length - 1].Lv) {
            curCfg = config[config.length - 1];
          }else {
            curCfg = curCfg[0];
          }
          const max = curCfg.maxRechargeIceCrystals;
          if (max) {
            const cardCount = cardPresets.length;
            const daubBetOption = this.resolveRandomDaubBetOptions(this._currentLevel);
            const ticketCost = betOptions[cardCount - 1][betOptionIndex] * cardCount;
            const presetBet = daubBetOption[daubBetOption.length - 1][daubBetOption[daubBetOption.length - 1].length - 1] * 4;
            const count = Math.ceil(ticketCost / presetBet * max);
            const cardCellCountList = new Array(cardPresets.length).fill(0);//每张卡埋的雪花数
            const everyCardCount = Math.floor(count/(cardPresets.length));
            const remainCount = count%(cardPresets.length);
            if(remainCount>0){
              const randomIndexes=getRandomIndexes(cardCellCountList,remainCount);
              cardCellCountList.forEach((item,i)=>{
                if(randomIndexes.includes(i)){
                cardCellCountList[i]+=1;
                }
              });
            }
            if(everyCardCount > 0){
                cardCellCountList.forEach((item,i)=>{
                    cardCellCountList[i]+=everyCardCount;
                });
            }
            //随机分配具体个数
            cardPresets.forEach((v, i) => {
              let cellEffectInitializations = v.cellEffectInitializations;
              if(cardCellCountList[i]>0){
              if (!cellEffectInitializations) {
                cellEffectInitializations = [];
              }
              cellEffectInitializations.push({
                effect: [CellEffect.SnowFlake, 0],
                count: cardCellCountList[i]
              });
              cardPresets[i].cellEffectInitializations = cellEffectInitializations;
            }});
          }
        }
      }
        break;
      default:
        break;
    }
  }

  public getProgressPresetFromSnapshot(roomId: number, snapshot: IRoundProgressSnapshot): IRoundProgressPreset {
    const roomCfg = this._roomInfos[roomId];
    const betOptions = this.resolveBetOptions(this._currentLevel, this._currentRoomId);

    const cardPresets: ICardPreset[] = _.map(snapshot.cards, card => ({
      seed: card.seed,
    }));

    const regionQualityWeights = _.map(roomCfg.powerup_quality_weight.powerUpsIdx, (fromIndex, i, collection) => {
      return {
        fromIndex: fromIndex,
        toIndexExclusive: collection[i + 1] || Number.MAX_SAFE_INTEGER,
        weights: roomCfg.powerup_quality_weight.quality[i],
      };
    });

    const powerupWeights = _.map(roomCfg.powerup_weight, (cfg) => ({
      powerupId: cfg.powerup,
      weight: cfg.weight,
    }));

    const minimumAmountRequirements = _.map(roomCfg.powerups_min_num, cfg => ({
      weight: cfg.weight,
      requirement: _.map(cfg.min_num, r => ({
        powerupId: r.powerup,
        amount: r.num,
      }))
    }));

    const powerupEffectDaubableRates = _.map(roomCfg.powerup_click_weight, c => _.map(c, b => _.map(b, cfg => ({
      powerupId: cfg.powerup,
      rate: cfg.weight,
    }))));


    const powerupReplacements = roomCfg.special_transform_id === '' ?
        [] as { originalId: number, targetId: number }[] :
        _(JSON.parse(roomCfg.special_transform_id)).toPairs().map(([originalId, targetId]) => ({
          originalId: _.toSafeInteger(originalId),
          targetId: _.toSafeInteger(targetId)
        })).value();

    const steps = _.split(roomCfg.powerup_extra_bingo, ';');
    const powerupCausedExtraBingoRate = {
      [PowerupId.DaubOnce]: _.toNumber(steps[0]),
      [PowerupId.DaubTwice]: _.toNumber(steps[1]),
      [PowerupId.ForcedBingo]: _.toNumber(steps[2]),
    };

    const cellEffectInitializationMaps = [
      {effect: CellEffect.TicketReward, map: roomCfg.initial_ticket},
      {effect: CellEffect.BoxReward, map: roomCfg.initial_box}
    ];

    const powerupBalanceReplacements = _.mapValues(roomCfg.powerup_replace, r => ({
      beReplacedRate: r.be_replaced_rate,
      replaceWeight: r.replace_weight
    }));

    const powerupBalances = {1: 10, 2: 10, 3: 10, 4: 10, 5: 10, 6: 10, 7: 10};

    const progressPreset: IRoundProgressPreset = {
      joinRoundTime: snapshot.joinTime,
      cardCountOptionIndex: cardPresets.length - 1,
      betOptionIndex: snapshot.betIndex,
      cardPresets,
      cardCountOptions: [1, 2, 3, 4],
      powerupBoxSeed: snapshot.powerupBox.seed,
      betOptions: betOptions[cardPresets.length - 1],
      powerupsCooldownTime: snapshot.powerupBox.cd,
      daubCountToChargePowerups: snapshot.powerupBox.toCharge,
      badBingoPenaltyTime: snapshot.penalty,
      collectionRewardInitializationMap: roomCfg.shadow_card_modify,
      cellEffectInitializationMaps,
      powerupPresets: powerups,
      regionQualityWeights: regionQualityWeights,
      powerupWeights: powerupWeights,
      powerupMinimumAmountRequirements: minimumAmountRequirements,
      powerupEffectDaubableRateMap: powerupEffectDaubableRates,
      powerupCausedExtraBingoRate: powerupCausedExtraBingoRate as { [PowerupId.DaubOnce]: number, [PowerupId.DaubTwice]: number, [PowerupId.ForcedBingo]: number },
      powerupReplacements: powerupReplacements,
      powerupCorrectionEndIndexByCardCountOptionIndex: this._insertionParam,
      extraBingoPowerupSwapIndexByCardCountOptionIndex: this._extraPowerupInsertionParam,
      powerupBalanceReplacements,
      powerupBalances,
      doubleTicketPowerupNoBingoFactor: this._doubleTicketPowerupNoBingoParam,
      randomDaubBingoConfig: this._extraBingoStages[this._currentRoomId] ? this._extraBingoStages[this._currentRoomId].rates : null
    };

    return progressPreset;
  }

  public getHostPresetFromSnapshot(roomId: number, callSeed: number, snapshot: IRoundHostSnapshot): IRoundHostPreset {
    const roomCfg = this._roomInfos[roomId];

    return {
      ruleId: snapshot.ruleId,
      roundStartTime: snapshot.roundStartTime,
      maxCallCount: _.toSafeInteger(roomCfg.max_call),
      callInterval: snapshot.callInterval,
      totalCardCountRange: {lower: 275, upper: 276},
      maxPlayerCount: snapshot.maxPlayerCount,
      bingoCountRate: 0.15,
      callSeed: callSeed
    };
  }

  private getFarestAvailableRoomId(): number {
    switch (this._careerConfig.roomId) {
      case -1: {
        //按升级进度选择关卡
        let targetRoomId = this._currentRoomId;
        while (this._roomInfos[targetRoomId + 1]) {
          const nextRoomLevelRequirement = this._roomInfos[targetRoomId + 1].level_unlock_requirement.player_level;
          if (this._currentLevel < nextRoomLevelRequirement) break;
          targetRoomId += 1;
        }

        return targetRoomId;
      }
      case -2: {
        //按收集物奖励选择关卡
        if (this._careerConfig.collectRoomId !== this._currentRoomId && !!this._roomInfos[this._careerConfig.collectRoomId]) {
          return this._careerConfig.collectRoomId;
        } else {
          return this._currentRoomId;
        }
      }
      default: {
        assert(this._roomInfos[this._careerConfig.roomId], 'Invalid room id');
        return this._careerConfig.roomId;
      }
    }


  }

  public async startCareer(): Promise<void> {
    let roundPlayed = 0;
    while (roundPlayed < this._careerConfig.maxRoundToPlay) {
      await this.playARound();
      if (!this._canPlayAhead) break;

      roundPlayed += 1;
    }
  }

  getBonusCfg(bonusId: number) {
    let config = null, checkConfig = null;
    switch (bonusId) {
      case 6: {
        config = this.getCfg("CFGIslandStage1017Feature");
        checkConfig = this.getCfg("1017FeatureCheckList");
        break;
      }
      case 8: {
        config = this.getCfg("1023MiniGame");
        break;
      }
      case 9: {
        config = this.getCfg("1025MiniGame");
        break;
      }
      case 10: {
        config = this.getCfg("1026MiniGame");
        break;
      }
      case 11: {
        config = this.getCfg("1028MiniGame");
        break;
      }
    }
    return {config, checkConfig};
  }

  getCfg(cfgFileName: string) {
    const devDir = '/home/<USER>/tmp/bingoCfgs_bingoCore';
    if (fs.existsSync(devDir)) {
      return require(path.join(devDir, cfgFileName + '.json'));
    }
    return require(`../configs/${cfgFileName}.json`);
  }
  getBonusExtraParam(bonusId: number){
      let extraParam = {};
      switch (bonusId) {
          case 11: {
            const daubBetOption = this.resolveRandomDaubBetOptions(this._currentLevel);
            const ticketTotal = daubBetOption[daubBetOption.length - 1][daubBetOption[daubBetOption.length - 1].length - 1] * 4;
              extraParam={
                  ticketTotal,
              };
              break;
          }
      }
      return extraParam;
  }
  pickUniqNewCollections(count: number) {
    count = _.toSafeInteger(count);

    const collectionsThisRoom = [this._roomCollectionCfgs[this._currentRoomId]];

    const newCollections = _.filter(_.range(1, 13), v => {
      return !(_.get(this._collections, [this._currentRoomId, `"${v}"`], 0) > 0);
    });

    // Pick uniq new collections
    if (count <= newCollections.length) {
      return pickRandomItemsWithMutation(newCollections, count);
    }

    /** @type {number[]} */
    const picked = [];

    if (newCollections.length > 0) {
      count -= newCollections.length;
      const pickedNew = pickRandomItemsWithMutation(newCollections, newCollections.length);
      picked.push(...pickedNew);
    }

    // Pick uniq collections
    while (count >= collectionsThisRoom.length) {
      const pickedOwned = pickRandomItems(collectionsThisRoom, collectionsThisRoom.length);
      picked.push(...pickedOwned);
      count -= collectionsThisRoom.length;
    }

    const lastPickedOwned = pickRandomItems(collectionsThisRoom, count);
    picked.push(...lastPickedOwned);

    return picked;
  }
}

const fs = require('fs');
const ruleId = 224;
const dir = `./pools/${ruleId}`;
fs.readdir(dir, (err, files) => {
  if (err) {
    return console.log('目录不存在');
  }
  const statistics = {};
  let total = 0;
  for (let name of files) {
    const json = require(`${dir}/${name}`);
    for (let card of json.cards) {
      statistics[card.bingoAt] = (statistics[card.bingoAt] || 0) + 1;
    }
    total += json.cards.length;
  }
  for (let count in statistics) {
    console.log(count, ':', (statistics[count] / total * 100).toFixed(2) + '%');
  }
});
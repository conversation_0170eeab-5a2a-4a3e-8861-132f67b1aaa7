import _ from 'lodash';
import { CellEffect, IRoundHostPreset, PowerupId } from '../src';
import CardPoolLoader from './CardPoolLoader';
import { hostOneRound } from './hostOneRound';
import PlayerCareer, { ICareerConfig } from './PlayerCareer';
import { getTestRoundProgressPreset } from './utils';

const careerConfig: ICareerConfig = {
  initialTicketCount: 1000,
  maxRoundToPlay: 100,
  cardCount: 4,
  betOption: 3,
  roomId: 1,
};

export type RoundDebugOption = {
  ruleId: number;
  callSeed: number;
  cardSeeds: number[];
  betOptionIndex: number;
  powerupBoxSeed: number;
}

const option: RoundDebugOption = {
  ruleId: 101,
  callSeed: 228,
  cardSeeds: [687245968, 492896607],
  betOptionIndex: 0,
  powerupBoxSeed: 2473,
};

Promise.resolve().then(async function () {
  const career = new PlayerCareer(careerConfig);
  await career.init();

  ///// Real situation /////

  // const range = _.range(30001, 40000);
  // for (const seed of range) {
  //   option.powerupBoxSeed = seed;
  //   const {hostPreset, progressPreset, progress} =  await career.debugARound(option);

  //   if (progress.cards[1].canBingo) {
  //     debugger
  //   }
  // }

  // await career.debugARound(option);

  ///// Dummy situation /////

  const hostPreset: IRoundHostPreset = {
    ruleId: option.ruleId,
    roundStartTime: 0,
    maxCallCount: 25,
    callInterval: 5e3,
    maxPlayerCount: 50,
    totalCardCountRange: { lower: 250, upper: 300 },
    bingoCountRate: 0.15,
    callSeed: option.callSeed,
  };

  const progressPreset = getTestRoundProgressPreset();
  progressPreset.cardPresets = _.map(option.cardSeeds, s => ({ seed: s }));
  progressPreset.cardCountOptionIndex = option.cardSeeds.length - 1;

  const range = _.range(30001, 40000);
  for (const seed of range) {

    progressPreset.powerupBoxSeed = 30032;
    const { progress } = await hostOneRound(hostPreset, [progressPreset], true);

    const forceBingoCellValue = _.find(progress.cards[1].cells, c => c.hasEffect(CellEffect.ForcedBingo))?.value;

    const fastForceBingo = progress.powerupBox.powerupUsages[0].powerupId === PowerupId.ForcedBingo && forceBingoCellValue &&
      _.indexOf(progress.roundHost.fullCallList, forceBingoCellValue) < 10 && progress.cards[1].canBingo;
    if (fastForceBingo) debugger;
  }



  process.exit(0);
});

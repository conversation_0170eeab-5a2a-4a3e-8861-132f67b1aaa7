import { Card, CommandFactory, ICardPreset, IRoundHostPreset, PlayerRoundProgress, RoundHost } from "../src";
import { getRuleById } from "../src/rules";
import { getTestRoundProgressPreset, logProgress } from "./utils";
import _ from 'lodash';
import assert from 'assert';

const ruleId = 101;
const callSeed = 56;
const cardSeed = 388;

function debugOneSeed(ruleId: number, callSeed: number, cardSeed: number) {
  const rule = getRuleById(ruleId);
  const hostPreset: IRoundHostPreset = {
    callSeed: callSeed,
    ruleId: ruleId,
    bingoCountRate: 1,
    callInterval: 5e5,
    maxCallCount: 25,
    maxPlayerCount: 50,
    roundStartTime: 0,
    totalCardCountRange: { lower: 250, upper: 300 }
  };
  const host = new RoundHost(undefined, hostPreset);

  const testProgressPreset = getTestRoundProgressPreset();
  testProgressPreset.cardCountOptionIndex = 0;
  testProgressPreset.cardPresets = [
    { seed: cardSeed, requiredDaubCountToBingo: 0 },
  ];
  const progress = new PlayerRoundProgress(host, undefined, testProgressPreset);
  const theCard = progress.cards[0];

  _.range(hostPreset.maxCallCount).forEach((callIndex) => {
    if (theCard.isBingoed) return false;

    const startTimeThisCall = (callIndex + 1) * hostPreset.callInterval;
    CommandFactory.receiveCall(startTimeThisCall).execute(progress);
    const theCall = _.last(progress.getReceivedCallList());

    const targetCell = _.find(theCard.cells, (cell) => (!cell.isDaubed) && cell.value === theCall);
    if (!targetCell) return;
    const latestTimeThisCall = startTimeThisCall + hostPreset.callInterval;
    const daubResult = CommandFactory.tryDaub(latestTimeThisCall, 0, targetCell.index).execute(progress);
    assert(daubResult);
    logProgress(progress);
    if (theCard.canBingo) CommandFactory.tryBingo(latestTimeThisCall, theCard.index).execute(progress);
  });

  logProgress(progress);

  const progressForPrediction = new PlayerRoundProgress(host, undefined, testProgressPreset);
  const record = rule.predictNaturalBingo(progressForPrediction, host);
  console.table(record);
}

debugOneSeed(ruleId, callSeed, cardSeed);

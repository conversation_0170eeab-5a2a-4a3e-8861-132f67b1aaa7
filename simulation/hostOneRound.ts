// @ts-nocheck
import {
  RoundHost, IRoundHostPreset, CommandFactory, IRoundProgressPreset, PlayerRoundProgress
} from '../src';
import {logProgress} from './utils';
import _ from 'lodash';
import {CardEffect, CellEffect} from '../src/effects';
import {CommandType} from '../src/commands/IRoundCommand';
import {logger} from '../src/CoreUtils2';
import {PowerupId} from "../src";

export interface IRewardDeterminable {
  '总涂抹次数': number;
  '手动涂抹次数': number;
  'exp*2点击': number;
  'bingo卡片数': number;
  '随机涂抹前bingo数': number;
  '随机涂抹个数': number;
  'bingo阴影卡数': number;
  '宝箱点击': number;
  'bingo票点击': number;
  'bingo票*2点击': number;
  '排名': number[];
  'Powerups': number[];
  'actualBingos': boolean[];
}

export function hostOneRound(hostPreset: IRoundHostPreset, progressPresets: IRoundProgressPreset[], enableMainPlayerLog = false): { reward: IRewardDeterminable, progress: PlayerRoundProgress } {
  const host = new RoundHost(undefined, hostPreset);

  const progresses = _.map(progressPresets, p => host.tryJoinOnePlayerLegacy(p) as PlayerRoundProgress);

  //TAG 仅用于测试
  // if (1 === 1) {
  //     return;
  // }

  logger.info('hostOneRoundStart', {progressPresets});

  _.range(hostPreset.maxCallCount).forEach(function (callIndex) {
    if (host.remainingBingoCount <= 0) return false;

    ///// Players Loop /////
    _.forEach(progresses, (progress) => {

      if (host.remainingBingoCount <= 0) return false;

      // Receive calls
      const startTimeThisCall = (callIndex + 1) * hostPreset.callInterval;
      CommandFactory.receiveCall(startTimeThisCall).execute(progress);
      const theCall = _.last(progress.getReceivedCallList());

      _.forEach(progress.cards, (card, cardIndex) => {

        logger.debug('callNow', {cardIndex, callIndex});

        if (host.remainingBingoCount <= 0) return false;
        if (card.isBingoed) {
          logger.debug('cardBingo', {cardIndex, callIndex});
          return false;
        }

        const targetCell = _.find(card.cells, (cell) => (!cell.isDaubed) && cell.value === theCall);
        const latestTimeThisCall = startTimeThisCall + hostPreset.callInterval;
        if (targetCell) {
          const [, daubClues] = CommandFactory.tryDaub(latestTimeThisCall, cardIndex, targetCell.index).executeWithClues(progress);
          const [, usePowerupClues] = CommandFactory.tryUsePowerup(latestTimeThisCall).executeWithClues(progress);
        }
        if (card.canBingo) {
          CommandFactory.tryBingo(latestTimeThisCall, card.index).execute(progress);
        }
      });
    });

    enableMainPlayerLog && logProgress(progresses[0]);
  });

  const playerOneResult = progresses[0].gatherRoundResult();
  // console.log(JSON.stringify(progresses[0].getSnapshot()));
  console.table(playerOneResult);

  const daubCommandCount = _.filter(progresses[0].commandHistory, c => c.type === CommandType.TryDaub).length;
  const playerOneRewardInfo: IRewardDeterminable = {
    '总涂抹次数': playerOneResult.validDaubCount,
    '手动涂抹次数': daubCommandCount,
    'exp*2点击': _.filter(playerOneResult.cellEffects, ([t]) => t === CellEffect.DoubleExp).length,
    'bingo卡片数': playerOneResult.bingoCount,
    'bingo阴影卡数': _.filter(playerOneResult.cardEffects, ([e]) => e === CardEffect.CollectionReward).length,
    '宝箱点击': _.filter(playerOneResult.cellEffects, ([t]) => t === CellEffect.BoxReward).length,
    'bingo票点击': _.filter(playerOneResult.cellEffects, ([t]) => t === CellEffect.TicketReward).length,
    'bingo票*2点击': _.filter(playerOneResult.cellEffects, ([t]) => t === CellEffect.DoubleTicket).length,
    '排名': playerOneResult.ranks,
    'Powerups': playerOneResult.usedPowerups,
    'actualBingos': _(progresses[0].cards).map(card => card.isBingoed).value()
  };

  // console.table(playerOneRewardInfo);
  return {
    reward: playerOneRewardInfo,
    progress: progresses[0]
  };
}


export function hostOneRoundNew(hostPreset: IRoundHostPreset, progresses: PlayerRoundProgress[], host: RoundHost, enableMainPlayerLog = false, isRandomDaub = false): { reward: IRewardDeterminable, progress: PlayerRoundProgress } {
  _.range(hostPreset.maxCallCount).forEach(function (callIndex) {
    if (host.remainingBingoCount <= 0) return false;

    ///// Players Loop /////
    _.forEach(progresses, (progress) => {

      if (host.remainingBingoCount <= 0) return false;

      // Receive calls
      const startTimeLastCall = callIndex * host.callInterval + 1000;
      const startTimeThisCall = (callIndex + 1) * host.callInterval;
      //每秒调用
      for (let i = 0; i < host.callInterval; i += 1000) {
        CommandFactory.receiveCall(startTimeLastCall + i).execute(progress);
      }
      const theCall = _.last(progress.getReceivedCallList());

      _.forEach(progress.cards, (card, cardIndex) => {

        logger.debug('callNow', {cardIndex, callIndex});

        if (host.remainingBingoCount <= 0) return false;
        if (card.isBingoed) {
          logger.debug('cardBingo', {cardIndex, callIndex});
          return;
        }

        const targetCell = _.find(card.cells, (cell) => (!cell.isDaubed) && cell.value === theCall);
        const latestTimeThisCall = startTimeThisCall + hostPreset.callInterval;
        if (targetCell) {
          const [, daubClues] = CommandFactory.tryDaub(latestTimeThisCall, cardIndex, targetCell.index).executeWithClues(progress);
          const [, usePowerupClues] = CommandFactory.tryUsePowerup(latestTimeThisCall).executeWithClues(progress);
        }
        if (card.canBingo) {
          CommandFactory.tryBingo(latestTimeThisCall, card.index).execute(progress);
        }
      });
    });

    enableMainPlayerLog && logProgress(progresses[0]);
  });


  let beforeDaubBingoCount = 0, powerupId = 0;
  if (isRandomDaub && progresses[0].randomDaubBingoConfig) {
    //添加随机涂抹逻辑
    powerupId = Math.random() < 0.5 ? PowerupId.OverRandomDaubOnce : PowerupId.OverRandomDaubTwice;
    beforeDaubBingoCount = _.filter(progresses[0].cards, card => card.isBingoed).length;
    CommandFactory.tryUsePowerup(hostPreset.maxCallCount * hostPreset.callInterval, {powerUpId: powerupId}).executeWithClues(progresses[0]);
    for (const card of progresses[0].cards) {
      if (card.canBingo && !card.isBingoed) {
        CommandFactory.tryBingo(hostPreset.maxCallCount * hostPreset.callInterval, card.index).execute(progresses[0]);
      }
    }
  }
  const playerOneResult = progresses[0].gatherRoundResult();
  // console.log(JSON.stringify(progresses[0].getSnapshot()));
  console.table(playerOneResult);

  const daubCommandCount = _.filter(progresses[0].commandHistory, c => c.type === CommandType.TryDaub).length;
  const playerOneRewardInfo: IRewardDeterminable = {
    '总涂抹次数': playerOneResult.validDaubCount,
    '手动涂抹次数': daubCommandCount,
    'exp*2点击': _.filter(playerOneResult.cellEffects, ([t]) => t === CellEffect.DoubleExp).length,
    'bingo卡片数': playerOneResult.bingoCount,
    '随机涂抹前bingo数': beforeDaubBingoCount,
    '随机涂抹个数': powerupId === PowerupId.OverRandomDaubOnce ? 1 : (powerupId === PowerupId.OverRandomDaubTwice ? 2 : 0),
    'bingo阴影卡数': _.filter(playerOneResult.cardEffects, ([e]) => e === CardEffect.CollectionReward).length,
    '宝箱点击': _.filter(playerOneResult.cellEffects, ([t]) => t === CellEffect.BoxReward).length,
    'bingo票点击': _.filter(playerOneResult.cellEffects, ([t]) => t === CellEffect.TicketReward).length,
    'bingo票*2点击': _.filter(playerOneResult.cellEffects, ([t]) => t === CellEffect.DoubleTicket).length,
    '排名': playerOneResult.ranks,
    'Powerups': playerOneResult.usedPowerups,
    'actualBingos': _(progresses[0].cards).map(card => card.isBingoed).value()
  };

  // console.table(playerOneRewardInfo);
  return {
    reward: playerOneRewardInfo,
    progress: progresses[0]
  };
}

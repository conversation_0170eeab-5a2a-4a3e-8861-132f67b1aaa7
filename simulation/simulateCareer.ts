import PlayerCareer, {ICareerConfig} from './PlayerCareer';
import xlsx from 'xlsx';
import _ from 'lodash';

// process.env.lambdaLogLevel = 'info';
import {logger} from '../src/CoreUtils2';
// logger.init(require('../test/CoreLogger').logger);

let argsArr = ['card=4', 'bet=4', 'round=10000', 'ticket=1000000', 'roomId=1028', 'isRandomDaub=1'];

const args: { [argName: string]: string } = {};
if(process.argv && process.argv.length >= 3){
  argsArr = process.argv;
}
//process.argv.forEach((kv) => {
argsArr.forEach((kv) => {
  const [key, value] = kv.split('=');
  if (value) {
    args[key] = value;
  }
});

(async function () {
  const argCardCount = _.toSafeInteger(args['card']);
  let cardCount = 4;
  if ([-1, 1, 2, 3, 4].includes(argCardCount)) {
    cardCount = argCardCount;
  }

  const argMapToBetOption = {
    '-1': -1,
    '1': 0,
    '2': 1,
    '3': 2,
    '4': 3
  };
  const betOption = argMapToBetOption[args['bet'] as '-1' | '1' | '2' | '3' | '4'];
  if (betOption === undefined) throw new Error('非法选项。');

  const roomId = _.toInteger(args['roomId']);

  const careerConfig: ICareerConfig = {
    initialTicketCount: _.toSafeInteger(args['ticket'] || 1000),
    maxRoundToPlay: _.toSafeInteger(args['round'] || 100),
    cardCount: cardCount as -1 | 1 | 2 | 3 | 4,
    betOption: betOption as -1 | 0 | 1 | 2 | 3,
    roomId: roomId,
    collectRoomId: 1,
    randomRepeatCollection: Number(args['randomRepeatCollection'] || 0),
    isRandomDaub: Number(args['isRandomDaub'] || 0),
  };

  // console.log(`\x1b[44m SIMULATION CONFIG \x1b[0m `);
  // console.table(careerConfig);

  logger.info("开始执行，参数配置", {careerConfig});

  const career = new PlayerCareer(careerConfig);
  await career.init();
  await career.startCareer();

  // Output
  const sheet = xlsx.utils.json_to_sheet(
      career.logs,
      // { header: ['游戏局数', '玩家等级', '本局卡片数', '本局bingo数', '本局获得经验值', '剩余bingo票'] },
  );

  const workBook = xlsx.utils.book_new();
  xlsx.utils.book_append_sheet(workBook, sheet, 'player career');
  xlsx.writeFile(workBook, `player_career-${roomId}-${cardCount}-${betOption === -1 ? betOption : betOption + 1}-${Math.floor(Date.now() / 1000)}.xlsx`);
  process.exit(0);
})();

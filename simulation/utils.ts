import _ from 'lodash';
import chalk from 'chalk';
import { CardCell } from '../src/CardCell';
import { IRoundProgressPreset, PlayerRoundProgress } from '../src/PlayerRoundProgress';
import cfg from '../test/cfg.json';
import { CellEffect, PowerupId } from '../src';

export function getTestRoundProgressPreset(): IRoundProgressPreset {
  const powerup_quality_weight = { 'powerUpsIdx': [0, 2, 4], 'quality': [[{ 'quality': 1, 'weight': 30 }, { 'quality': 2, 'weight': 50 }, { 'quality': 3, 'weight': 20 }], [{ 'quality': 1, 'weight': 10 }, { 'quality': 2, 'weight': 30 }, { 'quality': 3, 'weight': 60 }], [{ 'quality': 1, 'weight': 65 }, { 'quality': 2, 'weight': 10 }, { 'quality': 3, 'weight': 25 }]] };
  const regionQualityWeights = _.map(powerup_quality_weight.powerUpsIdx, (fromIndex, i, collection) => {
    return {
      fromIndex: fromIndex,
      toIndexExclusive: collection[i + 1] || Number.MAX_SAFE_INTEGER,
      weights: powerup_quality_weight.quality[i],
    };
  });

  const powerup_weight = [{ 'powerup': 1, 'weight': 50 }, { 'powerup': 2, 'weight': 25 }, { 'powerup': 3, 'weight': 25 }, { 'powerup': 4, 'weight': 50 }, { 'powerup': 5, 'weight': 150 }, { 'powerup': 6, 'weight': 50 }, { 'powerup': 7, 'weight': 50 }];
  const powerupWeights = _.map(powerup_weight, (cfg) => ({
    powerupId: cfg.powerup,
    weight: cfg.weight,
  }));

  const powerups_min_num = [{ 'weight': 50, 'min_num': [{ 'powerup': 1, 'num': 1 }, { 'powerup': 2, 'num': 0 }, { 'powerup': 3, 'num': 0 }, { 'powerup': 4, 'num': 1 }, { 'powerup': 5, 'num': 0 }, { 'powerup': 6, 'num': 0 }, { 'powerup': 7, 'num': 1 }] }, { 'weight': 50, 'min_num': [{ 'powerup': 1, 'num': 1 }, { 'powerup': 2, 'num': 0 }, { 'powerup': 3, 'num': 0 }, { 'powerup': 4, 'num': 1 }, { 'powerup': 5, 'num': 0 }, { 'powerup': 6, 'num': 0 }, { 'powerup': 7, 'num': 1 }] }];
  const minimumAmountRequirements = _.map(powerups_min_num, cfg => ({
    weight: cfg.weight,
    requirement: _.map(cfg.min_num, r => ({
      powerupId: r.powerup,
      amount: r.num,
    }))
  }));

  // _.values(__debug__.cfg.powerups).map(p => ({id: p.id, maxCountPerRound: p.round_max_used, effectCellCountPerCard: p.product_count, quality: p.quality}))
  const powerupPresets = [{ 'id': 1, 'maxCountPerRound': 100, 'effectCellCountPerCard': 1, 'quality': 1 }, { 'id': 2, 'maxCountPerRound': 100, 'effectCellCountPerCard': 2, 'quality': 1 }, { 'id': 3, 'maxCountPerRound': 100, 'effectCellCountPerCard': 2, 'quality': 1 }, { 'id': 4, 'maxCountPerRound': 100, 'effectCellCountPerCard': 2, 'quality': 2 }, { 'id': 5, 'maxCountPerRound': 1, 'effectCellCountPerCard': 1, 'quality': 2 }, { 'id': 6, 'maxCountPerRound': 1, 'effectCellCountPerCard': 1, 'quality': 3 }, { 'id': 7, 'maxCountPerRound': 1, 'effectCellCountPerCard': 1, 'quality': 3 }];

  const powerup_click_weight = [[[{ 'powerup': 2, 'weight': 0.4 }, { 'powerup': 3, 'weight': 0.4 }, { 'powerup': 5, 'weight': 0.2 }, { 'powerup': 6, 'weight': 0.3 }, { 'powerup': 7, 'weight': 0.9 }], [{ 'powerup': 2, 'weight': 0.4 }, { 'powerup': 3, 'weight': 0.4 }, { 'powerup': 5, 'weight': 0.2 }, { 'powerup': 6, 'weight': 0.3 }, { 'powerup': 7, 'weight': 0.9 }], [{ 'powerup': 2, 'weight': 0.4 }, { 'powerup': 3, 'weight': 0.4 }, { 'powerup': 5, 'weight': 0.2 }, { 'powerup': 6, 'weight': 0.3 }, { 'powerup': 7, 'weight': 0.9 }], [{ 'powerup': 2, 'weight': 0.4 }, { 'powerup': 3, 'weight': 0.4 }, { 'powerup': 5, 'weight': 0.2 }, { 'powerup': 6, 'weight': 0.3 }, { 'powerup': 7, 'weight': 0.9 }]], [[{ 'powerup': 2, 'weight': 0.4 }, { 'powerup': 3, 'weight': 0.4 }, { 'powerup': 5, 'weight': 0.2 }, { 'powerup': 6, 'weight': 0.3 }, { 'powerup': 7, 'weight': 0.9 }], [{ 'powerup': 2, 'weight': 0.4 }, { 'powerup': 3, 'weight': 0.4 }, { 'powerup': 5, 'weight': 0.2 }, { 'powerup': 6, 'weight': 0.3 }, { 'powerup': 7, 'weight': 0.9 }], [{ 'powerup': 2, 'weight': 0.4 }, { 'powerup': 3, 'weight': 0.4 }, { 'powerup': 5, 'weight': 0.2 }, { 'powerup': 6, 'weight': 0.3 }, { 'powerup': 7, 'weight': 0.9 }], [{ 'powerup': 2, 'weight': 0.4 }, { 'powerup': 3, 'weight': 0.4 }, { 'powerup': 5, 'weight': 0.2 }, { 'powerup': 6, 'weight': 0.3 }, { 'powerup': 7, 'weight': 0.9 }]], [[{ 'powerup': 2, 'weight': 0.4 }, { 'powerup': 3, 'weight': 0.4 }, { 'powerup': 5, 'weight': 0.2 }, { 'powerup': 6, 'weight': 0.3 }, { 'powerup': 7, 'weight': 0.9 }], [{ 'powerup': 2, 'weight': 0.4 }, { 'powerup': 3, 'weight': 0.4 }, { 'powerup': 5, 'weight': 0.2 }, { 'powerup': 6, 'weight': 0.3 }, { 'powerup': 7, 'weight': 0.9 }], [{ 'powerup': 2, 'weight': 0.4 }, { 'powerup': 3, 'weight': 0.4 }, { 'powerup': 5, 'weight': 0.2 }, { 'powerup': 6, 'weight': 0.3 }, { 'powerup': 7, 'weight': 0.9 }], [{ 'powerup': 2, 'weight': 0.4 }, { 'powerup': 3, 'weight': 0.4 }, { 'powerup': 5, 'weight': 0.2 }, { 'powerup': 6, 'weight': 0.3 }, { 'powerup': 7, 'weight': 0.9 }]], [[{ 'powerup': 2, 'weight': 0.4 }, { 'powerup': 3, 'weight': 0.4 }, { 'powerup': 5, 'weight': 0.2 }, { 'powerup': 6, 'weight': 0.3 }, { 'powerup': 7, 'weight': 0.9 }], [{ 'powerup': 2, 'weight': 0.4 }, { 'powerup': 3, 'weight': 0.4 }, { 'powerup': 5, 'weight': 0.2 }, { 'powerup': 6, 'weight': 0.3 }, { 'powerup': 7, 'weight': 0.9 }], [{ 'powerup': 2, 'weight': 0.4 }, { 'powerup': 3, 'weight': 0.4 }, { 'powerup': 5, 'weight': 0.2 }, { 'powerup': 6, 'weight': 0.3 }, { 'powerup': 7, 'weight': 0.9 }], [{ 'powerup': 2, 'weight': 0.4 }, { 'powerup': 3, 'weight': 0.4 }, { 'powerup': 5, 'weight': 0.2 }, { 'powerup': 6, 'weight': 0.3 }, { 'powerup': 7, 'weight': 0.9 }]]];
  const powerupEffectDaubableRates = _.map(powerup_click_weight, c => _.map(c, b => _.map(b, cfg => ({
    powerupId: cfg.powerup,
    rate: cfg.weight,
  }))));

  const powerup_extra_bingo = '0.04;0.08;0.5';
  const powerupCausedExtraBingoRate: { [id: number]: number } = {};
  _(powerup_extra_bingo)
    .split(';')
    .map((str, i, collection) => i === 0 ? _.toNumber(str) : _.toNumber(str) - _.toNumber(collection[i - 1]))
    .forEach((rate, index) => {
      if (index === 0) powerupCausedExtraBingoRate[PowerupId.DaubOnce] = rate;
      else if (index === 1) powerupCausedExtraBingoRate[PowerupId.DaubTwice] = rate;
      else powerupCausedExtraBingoRate[PowerupId.ForcedBingo] = rate;
    });

  const progressPreset: IRoundProgressPreset = {
    joinRoundTime: 0,
    cardCountOptionIndex: 0,
    betOptionIndex: 0,
    cardPresets: [
      { seed: 233 },
    ],
    powerupBoxSeed: 233,
    powerupsCooldownTime: 15e3,
    daubCountToChargePowerups: 3,
    badBingoPenaltyTime: 10e3,
    cardCountOptions: [1, 2, 3, 4],
    betOptions: [10, 20, 30, 40],
    collectionRewardInitializationMap: cfg.shadow_card_modify,
    cellEffectInitializationMaps: [
      { effect: CellEffect.BoxReward, map: cfg.initial_box },
      { effect: CellEffect.TicketReward, map: cfg.initial_ticket }
    ],
    powerupPresets: powerupPresets,
    regionQualityWeights: regionQualityWeights,
    powerupWeights: powerupWeights,
    powerupMinimumAmountRequirements: minimumAmountRequirements,
    powerupEffectDaubableRateMap: powerupEffectDaubableRates,
    powerupCausedExtraBingoRate: powerupCausedExtraBingoRate as { [PowerupId.DaubOnce]: number, [PowerupId.DaubTwice]: number, [PowerupId.ForcedBingo]: number },
    powerupReplacements: [/* { originalId: 7, targetId: 4 } */],
    powerupCorrectionEndIndexByCardCountOptionIndex: [1, 2, 3, 3],
    extraBingoPowerupSwapIndexByCardCountOptionIndex: [2, 2, 3, 4],
    powerupBalances: { 1: 10, 2: 10, 3: 10, 4: 10, 5: 10, 6: 10, 7: 10 },
    powerupBalanceReplacements: {
      1: { beReplacedRate: 0.7, replaceWeight: 50 },
      2: { beReplacedRate: 0.7, replaceWeight: 80 },
      3: { beReplacedRate: 0.7, replaceWeight: 100 },
      4: { beReplacedRate: 0.8, replaceWeight: 0 },
      5: { beReplacedRate: 0.8, replaceWeight: 0 },
      6: { beReplacedRate: 0.8, replaceWeight: 0 },
      7: { beReplacedRate: 0.8, replaceWeight: 0 },
    },
    doubleTicketPowerupNoBingoFactor: 0.1,
  };

  return progressPreset;
}



const cellStringNotations = (cell: CardCell, padLength = 7): { value: string, effects: string } => {
  const numberString = _.padEnd(`[${_.padStart(cell.value.toString(), 2, '0')}]`, padLength, ' ');
  const value = cell.isDaubed ? chalk.inverse(numberString) : numberString;
  const effects = chalk.yellow.italic(_.padEnd(_.map(cell.effects, e => e[0]).join(','), padLength, ' '));
  // const effects = chalk.yellow.italic(_.padEnd(cell.findEffectValue(CellEffect.Tag)!.toString(), padLength, ' '));
  return { value, effects };
};

export function logProgress(progress: PlayerRoundProgress): void {
  const cardOutputLines = _.map(progress.cards, (card, index) => {
    const lines = [] as string[];
    const log = (str: string) => lines.push(str);

    const lineLength = 35;
    const cardTitle = `==== Card Index [${index}] ====${card.canBingo ? ' CAN BINGO!' : '==========='}`;
    if (card.isBingoed) {
      log(chalk.black.bgCyanBright(cardTitle));
    } else {
      log(chalk.cyanBright(cardTitle));
    }

    let cardEffectLine;
    if (card.effects.length > 0) {
      cardEffectLine = _.padEnd(`card effects: ${_.map(card.effects, ([t, v]) => `[${t},${v}]`).join(',')}`, lineLength, ' ');
    } else { cardEffectLine = _.padEnd(' ', lineLength, ' '); }
    log(chalk.green.italic(cardEffectLine));

    const collectedCellEffects = _.padEnd(_.map(card.getCollectedEffects, e => e[0]).join(','), lineLength, ' ');
    log(chalk.yellow.italic(collectedCellEffects));

    const cellValues = _(5).range().map(() => [] as string[]).value();
    const cellEffects = _(5).range().map(() => [] as string[]).value();

    _(card.cells).chunk(5).forEach((cellCol) => {
      _.forEach(cellCol, (cell, rowIndex) => {
        const { value, effects } = cellStringNotations(cell);
        cellValues[rowIndex].push(value);
        cellEffects[rowIndex].push(effects);
      });
    });

    _(5).range().forEach((rowIndex) => {
      log(cellValues[rowIndex].join(''));
      log(cellEffects[rowIndex].join(''));
    });

    return lines;
  });

  const gapLines = 13;
  const gap = _.range(gapLines).map(() => '   ');
  // _.forEach(cardOutputLines[0], (line) => console.log(line));


  console.log(chalk.black.bgCyan('# ROUND FRAME \n'));

  const callStrs = _.map(progress.roundHost.fullCallList, (call, index) => {
    if (index === progress.receivedCallIndex) return chalk.inverse(call);
    return call;
  });
  const callList = `Call: [${callStrs.join(', ')}]\n`;
  console.log(callList);

  const powerupStrs = _.map(progress.powerupBox.powerupUsages, (usage, index) => {
    if (index === progress.powerupBox.nextPowerupIndex) return chalk.inverse(usage.powerupId);
    return usage.powerupId;
  });
  const powerupBox = `PowerupBox: [${powerupStrs.join(', ')}]\n`;
  console.log(powerupBox);

  const card12resultLines = _.zip(cardOutputLines[0], gap, cardOutputLines[1] ? cardOutputLines[1] : ['']);
  _.forEach(card12resultLines, (line) => console.log(line.join('')));
  if (cardOutputLines[2]) {
    console.log('');
    const card34resultLines = _.zip(cardOutputLines[2], gap, cardOutputLines[3] ? cardOutputLines[3] : ['']);
    _.forEach(card34resultLines, (line) => console.log(line.join('')));
  }

  console.log('');
}


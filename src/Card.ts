import _ from 'lodash';
import {CardCell} from './CardCell';
import {CardEffect, CellEffect, ProgressEffect} from './effects';
import {IMemento} from './IMemento';
import {RandomSequence, RandomSequenceState} from './RandomSequence';
import {
  EffectTuple,
  findEffect,
  findEffectValue,
  generateRandomOrderedIntegers, getRandomArray,
  hasEffect,
  ICardPreset, pickRandomItemsWithMutation, PlayerRoundProgress, shuffleArr,
  updateEffectValue
} from '.';
import {cardCurrentSeed} from "./rules/common";

const RANGE_PER_COLUMN = 15;
const COLUMN_COUNT = 5;

export type CardBingoTester = (card: Card) => boolean;

export enum EnumBoolean {
  False = 0,
  True = 1
}

export enum NaturalBingoFlag {
  Unknown = 0,
  No = 1,
  Yes = 2,
}

export interface ICardFarseer {
  naturalBingo: NaturalBingoFlag;
  daubOnceTarget: number;
  daubTwiceTarget: [number, number];
}

export interface ICardSnapshot {
  seed: number;
  effects: EffectTuple<CardEffect>[] | CardEffect[];
  cellValues: number[];
  cellDaubed: EnumBoolean[];
  cellEffects: EffectTuple<CellEffect>[][];
  canBingo: boolean;
  isBingoed: boolean;
  rank: number;
  lastTry: number;
  farseer: [naturalBingo: NaturalBingoFlag, once: number, twice0: number, twice1: number];
}


export interface IRandomDaubScene {
  daubIndex: number[];
  isBingo: boolean;
  daubCount: number;
}

// 如果cell有该effect，将不会被选作powerup生成效果的候选
const defaultExclusiveEffects: CellEffect[] = [
  CellEffect.TicketReward,
  CellEffect.BoxReward,
  CellEffect.DoubleExp,
  CellEffect.DoubleTicket,
  CellEffect.ForcedBingo,
  CellEffect.MarkAsDaubByInitialization,
  CellEffect.Bamboo,
  CellEffect.Panda,
  CellEffect.Driver,
  CellEffect.OilDrum,
];

export class Card implements IMemento<ICardSnapshot>, ICardFarseer {
  public readonly index: number;
  public readonly seed: number;
  public readonly cells: Readonly<CardCell[]>;
  /** Cell value map to cell */
  public readonly cellDictionary: Readonly<Record<number, CardCell>>;

  public naturalBingo: NaturalBingoFlag;
  public daubOnceTarget: number;
  public daubTwiceTarget: [number, number];
  public randomDaubScene: IRandomDaubScene[] = [];
  public effects: EffectTuple<CardEffect>[] = [];
  public lastTryBingoTime;
  public canBingo = false;
  public isBingoed = false;
  public rank: number = 0;
  public targetCount = 0;

  constructor(index: number, snapshot?: ICardSnapshot, preset?: ICardPreset) {
    this.index = index;

    if (preset) {
      this.seed = preset.seed;
      const cellValues = Card.generateCellValues(preset.seed);
      this.cells = Object.freeze(_.map(cellValues, (value, index) => new CardCell(index, value, false)));
      this.cellDictionary = _.keyBy(this.cells, c => c.value);

      this.naturalBingo = NaturalBingoFlag.Unknown;
      this.daubOnceTarget = -1;
      this.daubTwiceTarget = [-1, -1];
      this.lastTryBingoTime = 0;
    } else if (snapshot) {
      this.seed = snapshot.seed;
      this.cells = Object.freeze(
          _.map(_.range(COLUMN_COUNT * COLUMN_COUNT), cellIndex => (
              new CardCell(cellIndex, snapshot.cellValues[cellIndex], snapshot.cellDaubed[cellIndex] === EnumBoolean.True,
                  snapshot.cellEffects[cellIndex])
          ))
      );
      this.cellDictionary = _.keyBy(this.cells, c => c.value);
      this.naturalBingo = _.get(snapshot.farseer, 0, NaturalBingoFlag.Unknown);
      this.daubOnceTarget = _.get(snapshot.farseer, 1, -1);
      this.daubTwiceTarget = [_.get(snapshot.farseer, 2, -1), _.get(snapshot.farseer, 3, -1)];
      // For compatibility
      if (_.isNumber(_.get(snapshot.effects, 0))) {
        this.effects = _.map(snapshot.effects, e => [e as CardEffect, 0]);
      } else {
        this.effects = snapshot.effects as EffectTuple<CardEffect>[];
      }

      this.canBingo = snapshot.canBingo;
      this.isBingoed = snapshot.isBingoed;
      this.lastTryBingoTime = snapshot.lastTry;
      this.rank = snapshot.rank
    } else {
      throw new Error('Invalid operation.');
    }
  }

  private static generateCellValues(seed: number): number[] {
    const rs = new RandomSequence(seed);
    const cellValues = _(COLUMN_COUNT).range()
        .map(i => generateRandomOrderedIntegers(i * RANGE_PER_COLUMN + 1, (i + 1) * RANGE_PER_COLUMN, COLUMN_COUNT, rs))
        .reduce((prev, curr) => _.concat(prev, curr), [] as number[]);

    return cellValues;
  }

  /**
   * An effect of daubed cell is defined as collected.
   * @returns Collected cell effects.
   */
  public getCollectedEffects(): EffectTuple<CellEffect>[] {
    const es: EffectTuple<CellEffect>[] = [];

    _.forEach(this.cells, cell => {
      if (!cell.isDaubed) return;
      es.push(...cell.effects);
    });

    return es;
  }

  public markCellAsDaubed(cellIndex: number): void {
    const targetCell = this.cells[cellIndex];
    targetCell.isDaubed = true;
  }

  public testIfCardCanBingo(tester: CardBingoTester): void {
    if (this.canBingo) return;
    this.canBingo = tester(this);
  }

  public markAsCanBingo(): void {
    this.canBingo = true;
  }

  public markAsBingoed(rank: number): void {
    this.canBingo = true;
    this.isBingoed = true;
    this.rank = rank;
  }

  public cellAtIndex(index: number): CardCell {
    return this.cells[index];
  }

  public cellAtLocation(x: number, y: number): CardCell | undefined {
    if (x < 0 || x > 4 || y < 0 || y > 4) return undefined;
    return this.cells[x * CardCell.cellStepCount + y];
  }

  public getEdgeAdjacentCells(cell: CardCell): CardCell[] {
    return _.compact([
      this.cellAtLocation(cell.x - 1, cell.y),
      this.cellAtLocation(cell.x + 1, cell.y),
      this.cellAtLocation(cell.x, cell.y - 1),
      this.cellAtLocation(cell.x, cell.y + 1),
    ]);
  }

  public getEdgeAndVertexAdjacentCells(cell: CardCell): CardCell[] {
    return _.compact([
      this.cellAtLocation(cell.x - 1, cell.y),
      this.cellAtLocation(cell.x + 1, cell.y),
      this.cellAtLocation(cell.x, cell.y - 1),
      this.cellAtLocation(cell.x, cell.y + 1),
      this.cellAtLocation(cell.x + 1, cell.y + 1),
      this.cellAtLocation(cell.x + 1, cell.y - 1),
      this.cellAtLocation(cell.x - 1, cell.y + 1),
      this.cellAtLocation(cell.x - 1, cell.y - 1),
    ]);
  }

  /**
   * 获取一个中心点周围的格子
   * @param cell 中心
   * @param range 范围
   */
  public getAdjacentCells(cell: CardCell, range: number): CardCell[] {
    const adjacentCells = [];
    for (let i = -1 * range; i <= range; i++) {
      for (let j = -1 * range; j <= range; j++) {
        if (i == 0 && j == 0) {
          continue;
        }
        adjacentCells.push(this.cellAtLocation(cell.x + i, cell.y + j));
      }
    }
    return _.compact(adjacentCells);
  }

  /**
   * 获取一个中心点周围的格子的index
   * @param index 中心点
   * @param range 范围
   */
  public getAdjacentCellIndex(index: number, range: number): number[] {
    const x = _.toInteger(index / CardCell.cellStepCount);
    const y = (index % CardCell.cellStepCount);
    const adjacentCells = [];
    for (let i = -1 * range; i <= range; i++) {
      for (let j = -1 * range; j <= range; j++) {
        if (i == 0 && j == 0) {
          continue;
        }
        const newX = x + i, newY = y + j;
        if (newX < 0 || newX > 4 || newY < 0 || newY > 4) continue;
        adjacentCells.push(newX * CardCell.cellStepCount + newY);
      }
    }
    return adjacentCells;
  }

  /**
   * 获取一个中心点四个角的格子
   * @param cell 中心
   */
  public getAdjacentCornerCells(cell: CardCell): CardCell[] {
    return _.compact([
      this.cellAtLocation(cell.x + 1, cell.y + 1),
      this.cellAtLocation(cell.x + 1, cell.y - 1),
      this.cellAtLocation(cell.x - 1, cell.y + 1),
      this.cellAtLocation(cell.x - 1, cell.y - 1),
    ]);
  }


  public getCardsYEdgeCells(y: number): CardCell[] {
    const cells = [];
    for (let x = 0; x < CardCell.cellStepCount; x++) {
      cells.push(this.cellAtLocation(x, y));
    }
    return _.compact(cells);
  }
  public getCardsXEdgeCells(x: number): CardCell[] {
    const cells = [];
    for (let y = 0; y < COLUMN_COUNT; y++) {
      cells.push(this.cellAtLocation(x, y));
    }
    return _.compact(cells);
  }
  public pushEffect(type: CardEffect, value = 0, customData: any = null): void {
    this.effects.push([type, value, customData]);
  }

  public hasEffect(type: CardEffect, value?: number): boolean {
    return hasEffect(this.effects, type, value);
  }

  public findEffect(type: CardEffect): EffectTuple<CardEffect> | undefined {
    return findEffect(this.effects, type);
  }

  public findCustomData<T>(type: CardEffect): T | undefined {
    const effectTuples = findEffect(this.effects, type);
    return effectTuples?.[2];
  }

  public findEffectValue(type: CardEffect): number | undefined {
    return findEffectValue(this.effects, type);
  }

  public updateEffectValue(type: CardEffect, value: number, customData?: any): boolean {
    return updateEffectValue(this.effects, type, value, customData);
  }

  public getNoDaubCells(): CardCell[] {
    return _.filter(this.cells, cell => !cell.isDaubed);
  }

  public getDaubCells(): CardCell[] {
    return _.filter(this.cells, cell => cell.isDaubed);
  }
  public getEffectCells(type: CellEffect):CardCell[]{
    return _.filter(this.cells, (cell)=>{
      if(cell.findEffect(type)){
        return true;
      }
      return  false;
    });
  }

  public getNoDaubCellsInCallList(callList: number[]): CardCell[] {
    return _(this.cells).filter(cell => !cell.isDaubed)
        .intersectionWith(callList, (cell, callValue) => cell.value === callValue).value();
  }

  public getNoDaubCellsNotInCallList(callList: number[]): CardCell[] {
    return _(this.cells).filter(cell => !cell.isDaubed)
        .differenceWith(callList, (cell, callValue) => cell.value === callValue).value();
  }

  public getNoExclusiveEffectCells(exclusiveEffects?: CellEffect[]): CardCell[] {
    return _.filter(this.cells, cell => _.intersectionWith(cell.effects,
        exclusiveEffects === undefined ? defaultExclusiveEffects : exclusiveEffects,
        ([t1,], t2) => t1 === t2).length === 0);
  }
  public getNoDaubCellsRandomCellS(remainCount:number,effectType?:CellEffect,callValue?:number): CardCell[] {
        const cellIndexList: number[] = [];
        _.filter(this.cells, cell => !cell.isDaubed).forEach(cell => {
            if(effectType){
                const effect= cell.findEffect(effectType);
                if(effect&&effect[1]==callValue){
                    cellIndexList.push(cell.index);
                }
            }else {
                cellIndexList.push(cell.index);
            }
        });
        const randomIndexes = getRandomArray(cellIndexList, remainCount, this);//随机几个格子
        return _.filter(this.cells,cell=>randomIndexes.indexOf(cell.index)!=-1);
  }
    //如果remainCount> noDaube 返回没有涂抹的数量
  public getNoDaubCellsRandomIndex(remainCount:number): number[] {
        const cellIndexList: number[] = [];
        _.filter(this.cells, cell => !cell.isDaubed).forEach(cell => {
            cellIndexList.push(cell.index);
        });
        return getRandomArray(cellIndexList, remainCount, this);//随机几个格子index
    }
  public getSnapshot(): ICardSnapshot {
    const snapshot: ICardSnapshot = {
      seed: this.seed,
      effects: this.effects,
      cellValues: [],
      cellDaubed: [],
      cellEffects: [],
      farseer: [this.naturalBingo, this.daubOnceTarget, this.daubTwiceTarget[0], this.daubTwiceTarget[1]],
      canBingo: this.canBingo,
      isBingoed: this.isBingoed,
      rank: this.rank,
      lastTry: this.lastTryBingoTime,
    };

    _.forEach(this.cells, cell => {
      snapshot.cellValues.push(cell.value);
      snapshot.cellDaubed.push(cell.isDaubed ? EnumBoolean.True : EnumBoolean.False);
      snapshot.cellEffects.push(cell.effects);
    });

    return snapshot;
  }

  public pickRandomCellsWithMutation(cells: CardCell[], count: number): CardCell[] {
    const rs = new RandomSequence(this.findSeed());
    const result = pickRandomItemsWithMutation(cells, count, rs);
    this.setSeed(rs);
    return result;
  }

  public findSeed(): RandomSequenceState {
    const effect = this.findEffect(CardEffect.CurrentSeed);
    if (!effect) {
      throw new Error('Invalid operation.');
    }
    return effect[2] as RandomSequenceState;
  }

  public setSeed(rs: RandomSequence): void {
    this.updateEffectValue(CardEffect.CurrentSeed, 0, rs.getState());
  }

  generateRandomDaubScene(progress: PlayerRoundProgress): void {
    const noDaubCells = this.cells.filter(v => !v.isDaubed);
    const rs = new RandomSequence(this.seed);
    shuffleArr(noDaubCells, rs);
    const daubOne = this.generateOneRandomDaubScene(noDaubCells, progress);
    const daubTwo = this.generateTwoRandomDaubScene(noDaubCells, progress);
    this.randomDaubScene = daubOne.filter(v => !!v).concat(daubTwo.filter(v => !!v));
  }

  generateOneRandomDaubScene(noDaubCells: CardCell[], progress: PlayerRoundProgress): IRandomDaubScene[] {
    const daubScene: IRandomDaubScene[] = [];
    for (let i = 0, l = noDaubCells.length; i < l; i++) {
      this.randomDaubSceneDraft(daubScene, [noDaubCells[i].index], progress);
      if (daubScene[0] && daubScene[1]) {
        return daubScene;
      }
    }
    return daubScene;
  }

  generateTwoRandomDaubScene(noDaubCells: CardCell[], progress: PlayerRoundProgress): IRandomDaubScene[] {
    const daubScene: IRandomDaubScene[] = [];
    for (let i = 0, l = noDaubCells.length; i < l; i++) {
      for (let j = i + 1; j < l; j++) {
        this.randomDaubSceneDraft(daubScene, [noDaubCells[i].index, noDaubCells[j].index], progress);
        if (daubScene[0] && daubScene[1]) {
          return daubScene;
        }
      }
    }
    return daubScene;
  }

  randomDaubSceneDraft(daubScene: IRandomDaubScene[], chooseIndex: number[], progress: PlayerRoundProgress): void {
    const tester = progress.roundHost.rule.bingoTester;
    const cardCopy = _.cloneDeep(this);
    const progressCopy = _.cloneDeep(progress);
    for (const index of chooseIndex) {
      progressCopy.pushEffectToCell(cardCopy, cardCopy.cellAtIndex(index), CellEffect.MarkAsDaubByPowerup);
    }
    const canBingo = tester(cardCopy) || cardCopy.canBingo;
    if (!daubScene[Number(canBingo)]) {
      daubScene[Number(canBingo)] = {
        daubIndex: chooseIndex,
        isBingo: canBingo,
        daubCount: chooseIndex.length
      };
    }
  }

}

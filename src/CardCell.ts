import _ = require('lodash');
import {CellEffect, EffectTuple} from './effects';
import {hasEffect, findEffect, findEffectValue, updateEffectValue} from './Utils';

// export type CellCoordinate = 0 | 1 | 2 | 3 | 4;
export class CardCell {
  public readonly x: number;
  public readonly y: number;
  public readonly index: number;
  public readonly value: number;

  // Cell states
  public isDaubed;
  public effects: EffectTuple<CellEffect>[] = [];

  public static cellStepCount = 5;

  constructor(index: number, value: number, isDaubed: boolean, effects?: EffectTuple<CellEffect>[]) {
    this.index = index;
    this.x = _.toInteger(index / CardCell.cellStepCount);
    this.y = (index % CardCell.cellStepCount);
    if (effects) {
      this.effects = _.concat(this.effects, effects);
    }
    this.isDaubed = isDaubed;

    this.value = value;
  }

  /**
   * Push effect directly to cell without triggering beforeCellMeetEffect.
   * @param type
   * @param value
   * @param customData
   */
  public pushEffect(type: CellEffect, value = 0, customData: any = null): void {
    this.effects.push([type, value, customData]);
  }

  public pullAllEffect(type: CellEffect, value?: number): void {
    if (value !== undefined) {
      _.pullAllWith(this.effects, [[type, value]], _.isEqual);
      return;
    }

    _.pullAllWith(this.effects, [type], ([t1,], t2) => t1 === t2);
  }

  public hasEffect(type: CellEffect, value?: number): boolean {
    return hasEffect(this.effects, type, value);
  }

  public findEffect(type: CellEffect): EffectTuple<CellEffect> | undefined {
    return findEffect(this.effects, type);
  }

  public findEffectValue(type: CellEffect): number | undefined {
    return findEffectValue(this.effects, type);
  }

  public updateEffectValue(type: CellEffect, value: number, customData?: any): boolean {
    return updateEffectValue(this.effects, type, value, customData);
  }

  public fetchAllEffectType(): CellEffect[] {
    return this.effects.map(v => v[0]);
  }
}

import _ from 'lodash';
import { PlayerRoundProgress } from '.';

export class ClueTracer {
  private readonly _progress: PlayerRoundProgress;
  private readonly _traceFrom: number;

  constructor(progress: PlayerRoundProgress) {
    if (!progress.clues) throw new Error('The clues of this progress is disabled.');
    this._progress = progress;
    this._traceFrom = progress.clues.length;
  }

  public Retrieve<T>(cardIndex: number): T[] {
    return _(this._progress.clues).slice(this._traceFrom)
      .filter(([index]) => index === cardIndex).map(([, clue]) => clue as T).value();
  }

  public RetrieveAllCard<T>(): [cardIndex: number, clue: T][] {
    return _.slice(this._progress.clues, this._traceFrom) as [number, T][];
  }
}
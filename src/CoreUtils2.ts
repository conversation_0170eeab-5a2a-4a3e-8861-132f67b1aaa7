// @ts-nocheck
import _ from 'lodash';

// 服务器内部致命错误
const error_internal = {code: 500, msg: 'internal core error'};
const error_TryBingo = {code: 10000, msg: 'error_TryBingo',};//其它bingo错误
const error_TryBingo_remainZero = {
    code: 10001,
    msg: 'error_TryBingo_remainZero',
};//bingo剩余数不足
const error_TryBingo_isBingoed = {
    code: 10002,
    msg: 'error_TryBingo_isBingoed',
};//卡片已经bingo了
const error_TryBingo_badTime = {code: 10003, msg: 'error_TryBingo_badTime',};//bingoCd未到
const error_TryBingo_canBingo = {code: 10004, msg: 'error_TryBingo_canBingo',};//卡片不能bingo
const error_TryBingo_zeroContinue = {code: 10005, msg: 'error_TryBingo_zeroContinue',};////剩余bingo数不足，不终结游戏
const error_ReceiveCall = {code: 10100, msg: 'error_ReceiveCall',};//叫号错误
const error_TryUsePowerup = {code: 10200, msg: 'error_TryUsePowerup',};//使用道具错误
const error_TryDaub = {code: 10300, msg: 'error_TryDaub',};//涂抹错误

function randomSetNum(maxNum: number, cardSize: number) {
    let avgNum = _.toInteger(maxNum / cardSize);
    // avgNum = 0
    let res = _.fill(Array(cardSize), avgNum);
    maxNum -= avgNum * cardSize;
    let fillIndex = 0;
    while (maxNum-- > 0) {
        if (fillIndex >= cardSize) {
            fillIndex = 0;
        }
        res [fillIndex++] += 1;
    }
    return res;
}

function push(array, ele) {
    array = array || [];
    if (!array.includes(ele)) {
        array.push(ele);
    }
}

function clone(obj) {
    return _.cloneDeep(obj);
}

class Logger {
    private logger;

    static init(logger) {
        this.logger = logger;
    }

    static info(...data) {
        if (this.logger && this.logger.info) {
            this.logger.info(...data);
        }
    }

    static warn(...data) {
        if (this.logger && this.logger.warn) {
            this.logger.warn(...data);
        }
    }

    static debug(...data) {
        if (this.logger && this.logger.debug) {
            this.logger.debug(...data);
        }
    }

    static error(...data) {
        if (this.logger && this.logger.error) {
            this.logger.error(...data);
        }
    }
}

const no = 0;
const yes = 1;

class CoreError extends Error {
    constructor(data, msg) {
        super();
        this.code = _.get(data, 'code', error_internal.code);
        if (data) {
            // 兼容自定义数据
            _.assign(this, data);
            if (data.msg) {
                this.message = data.msg;
            }
            // 兼容dynamoDB的eror message
            if (data.message) {
                this.message = data.message;
            }
            // 传递回传客户端的参数
            this.resAttrs = data.resAttrs;
        }
        // 优先使用自定义的msg
        msg = _.toString(msg);
        if (msg) {
            this.message = msg;
        }
        // 没有任何匹配，使用默认
        this.message = this.message || error_internal.msg;
    }
}

function check2(condition, errorInfo, logObj = {}, resAttrs = []) {
    if (condition) {
        return;
    }
    errorInfo = errorInfo || c.error_internal;
    errorInfo.code = errorInfo.code || c.error_internal.code;
    errorInfo.msg = errorInfo.msg || c.error_internal.msg;
    if (logObj && logObj.player) {
        logObj.playerId = logObj.player.playerId;
        logObj.robot = logObj.player.robot || c.no;
        delete logObj.player;
    }
    errorInfo.resAttrs = resAttrs;
    _.assign(errorInfo, logObj);
    // logger.error(data.msg, logObj);
    throw new CoreError(errorInfo);
}

export {
    randomSetNum, push, clone, Logger as logger,
    no, yes, check2,
    error_internal,
    error_TryBingo,
    error_TryBingo_remainZero,
    error_TryBingo_isBingoed,
    error_TryBingo_badTime,
    error_TryBingo_canBingo,
    error_ReceiveCall,
    error_TryUsePowerup,
    error_TryDaub,
    error_TryBingo_zeroContinue,
};



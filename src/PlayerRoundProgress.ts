import _ from 'lodash';
import {Card, ICardSnapshot} from './Card';
import {RoundHost} from './RoundHost';
import {IWeight, randomFloat} from './Utils';
import {
  PowerupId,
  IPowerupPreset,
  IQualityWeight,
  IPowerupBoxSnapshot,
  PowerupBox,
  IPowerupTypeWeight
} from './PowerupBox';
import {CardEffect, CellEffect, ProgressEffect} from './effects';
import {CardCell} from './CardCell';
import {
  getCommandHistoryFromSnapshot,
  getHistorySnapshot,
  IRoundCommandSnapshot,
  RoundCommand
} from './commands/IRoundCommand';
import {IMemento} from './IMemento';
import {
  logger,
  check2,
  error_TryBingo,
  error_TryBingo_isBingoed, error_TryBingo_badTime, error_TryBingo_canBingo, error_TryBingo_zeroContinue
} from './CoreUtils2';

import {
  EffectTuple,
  findEffect,
  findEffectValue,
  hasEffect, RandomSequence, RandomSequenceState,
  updateEffectValue
} from '.';
import {initializeProgressSeed} from "./rules/common";

export type CardIndexBetIndexMap<T> = T[][];
export type BetIndexMap<T> = T[];

export enum OperateType {
  None,
  Daub,
  PowerUp
}

export interface IShadowCardCountWeight extends IWeight {
  shadow_cards: number;
}

export interface IAmountPerCardWeight extends IWeight {
  amount: number;
}

export interface CellEffectTypeWeight extends IWeight {
  effect: CellEffect;
}

export interface IPowerupMinimumAmountRequirementWeight extends IWeight {
  requirement: { powerupId: number, amount: number }[]
}

export type CellEffectInitializationMap = {
  /** 特定的 Effect 种类，或者由权重组决定 Effect 种类 */
  effect: CellEffect | CellEffectTypeWeight[]
  map: CardIndexBetIndexMap<IAmountPerCardWeight[]>
}

export interface ICardPreset {
  seed: number;
  cellEffectInitializations?: {
    effect: EffectTuple<CellEffect>;
    count: number;
    daubRate?: number;
  }[];
}

export interface IRoundProgressPreset {
  limitEffects?: string[];//需要全局限定数量的effects，例如[[19,1],[20,1]]会被表示为["19_1","20_1"]
  joinRoundTime: number;
  cardCountOptionIndex: number;
  betOptionIndex: number;
  cardPresets: ICardPreset[];
  powerupBoxSeed: number;
  cardCountOptions: number[];
  betOptions: number[];
  powerupsCooldownTime: number;
  daubCountToChargePowerups?: number;
  daubCountToChargePowerupsArray?: [number, number][];
  badBingoPenaltyTime: number;
  collectionRewardInitializationMap: CardIndexBetIndexMap<IShadowCardCountWeight[]>;
  /** Deprecated. */
  cellEffectInitializationMaps: CellEffectInitializationMap[];
  powerupPresets: IPowerupPreset[];
  regionQualityWeights: { fromIndex: number, toIndexExclusive: number, weights: IQualityWeight[] }[];
  powerupWeights: IPowerupTypeWeight[];
  powerupMinimumAmountRequirements: IPowerupMinimumAmountRequirementWeight[];
  powerupEffectDaubableRateMap: CardIndexBetIndexMap<{ powerupId: number, rate: number }[]>;
  powerupCausedExtraBingoRate: {
    [PowerupId.DaubOnce]: number,
    [PowerupId.DaubTwice]: number,
    [PowerupId.ForcedBingo]: number
  };
  powerupReplacements: { originalId: number, targetId: number }[];
  powerupCorrectionEndIndexByCardCountOptionIndex: number[];
  extraBingoPowerupSwapIndexByCardCountOptionIndex: number[];
  powerupBalances: { [key: number]: number };
  powerupBalanceReplacements: { [key: number]: { beReplacedRate: number, replaceWeight: number } };
  doubleTicketPowerupNoBingoFactor: number;
  randomDaubBingoConfig?: number[][] | null;
  forceBonusGame?: boolean;
  cannotBigWin?: boolean;
  lastChanceInterVal?: number;//单位是毫秒
}

export interface IRoundResult {
  cardEffects: EffectTuple<CardEffect>[];
  cellEffects: EffectTuple<CellEffect>[];
  cardEffectsPerCard: EffectTuple<CardEffect>[][];
  validDaubCount: number;
  bingoCount: number;
  ranks: number[];
  bingoCardIndexes: number[];
  usedPowerups: number[];
}

export interface IRoundProgressSnapshot {
  betIndex: number;
  joinTime: number;
  penalty: number;
  receivedCallIndex: number;
  cards: ICardSnapshot[];
  ranks: number[];
  commands: IRoundCommandSnapshot[];
  powerupBox: IPowerupBoxSnapshot;
  pDaub: number;
  curRoundTime: number;
  operationIndex: number; //操作序号
  effects: EffectTuple<ProgressEffect>[];
  randomDaubBingoConfig: number[][] | null;
  extraBingoDaubHistory: IExtraBingoDaubHistory;
  cannotBigWin: boolean;
  lastChanceInterVal: number;
  lastChanceEndTime: number;
}

export interface IExtraBingoDaubHistory {
  daubCount: number;
  totalDaubCount: number;
  addBingoCount: number;
}

export class PlayerRoundProgress implements IMemento<IRoundProgressSnapshot> {
  ///// States /////
  public readonly roundHost: RoundHost;
  public readonly joinRoundTime: number;
  public readonly badBingoPenaltyTime: number;
  public readonly cardCount: number;
  public readonly betOptionIndex: number;
  public readonly randomDaubBingoConfig: number[][] | null = null;
  public readonly cards: Readonly<Card[]>;
  public readonly powerupBox: PowerupBox;
  public receivedCallIndex = -1;
  public forceBonusGame = false; //强制进入小游戏
  public readonly commandHistory: RoundCommand[] = [];
  public playerDaubCount = 0;
  public curRoundTime = 0;
  public operationIndex = 0;
  public clues: [cardIndex: number, clue: unknown][] | null = null;
  public effects: EffectTuple<ProgressEffect>[];
  public overRandomDaubFlag: boolean[] = [];
  public cannotBigWin = false; //是否不能中big win 当被big win 判定限制时为true
  public extraBingoDaubHistory: IExtraBingoDaubHistory = {
    daubCount: 0,
    totalDaubCount: 0,
    addBingoCount: 0
  };
  private lastChanceInterVal = 0 ; //最后一次机会的间隔时间
  private lastChanceEndTime = 0; //最后一次机会的结束时间
  private _ranks: number[] = [];

  public getSnapshot(): IRoundProgressSnapshot {
    const snapshot: IRoundProgressSnapshot = {
      betIndex: this.betOptionIndex,
      joinTime: this.joinRoundTime,
      penalty: this.badBingoPenaltyTime,
      receivedCallIndex: this.receivedCallIndex,
      cards: _.map(this.cards, card => card.getSnapshot()),
      ranks: this._ranks,
      commands: getHistorySnapshot(this.commandHistory),
      powerupBox: this.powerupBox.getSnapshot(),
      pDaub: this.playerDaubCount,
      curRoundTime: this.curRoundTime,
      effects: this.effects,
      randomDaubBingoConfig: this.randomDaubBingoConfig,
      extraBingoDaubHistory: this.extraBingoDaubHistory,
      operationIndex: this.operationIndex,
      cannotBigWin: this.cannotBigWin,
      lastChanceInterVal: this.lastChanceInterVal,
      lastChanceEndTime: this.lastChanceEndTime,
    };

    return snapshot;
  }

  constructor(host: RoundHost, snapshot?: IRoundProgressSnapshot, preset?: IRoundProgressPreset, enableClues?: boolean) {
    if (preset) {
      const cardCount = preset.cardCountOptions[preset.cardCountOptionIndex];
      if (preset.cardPresets.length !== cardCount) {
        throw new Error('Invalid card count.');
      }

      this.forceBonusGame = !!preset.forceBonusGame;
      this.roundHost = host;
      this.joinRoundTime = preset.joinRoundTime;
      this.badBingoPenaltyTime = preset.badBingoPenaltyTime;
      this.cardCount = cardCount;
      this.betOptionIndex = preset.betOptionIndex;
      this.effects = [];
      this.randomDaubBingoConfig = preset.randomDaubBingoConfig || null;
      this.cannotBigWin = !!preset.cannotBigWin;

      const newCards = _.map(preset.cardPresets, (preset, index) => new Card(index, undefined, preset));
      this.cards = newCards;

      // Initialize effects
      host.rule.initializeCardEffect(preset, this);
      host.rule.initializeCellEffect(preset, this);

      this.farseer();

      // Initialization of powerups box should be later than farseer because usages depend on it.
      this.powerupBox = new PowerupBox(this, undefined, preset);
      this.lastChanceInterVal = preset.lastChanceInterVal || 0;
    } else if (snapshot) {
      this.roundHost = new RoundHost(host);
      this.joinRoundTime = snapshot.joinTime;
      this.badBingoPenaltyTime = snapshot.penalty;

      this.cards = Object.freeze(
          _.map(snapshot.cards, (cardSnapshot, cardIndex) => new Card(cardIndex, cardSnapshot))
      );
      this.cardCount = snapshot.cards.length;
      this.betOptionIndex = snapshot.betIndex;
      this.receivedCallIndex = snapshot.receivedCallIndex;
      this.effects = snapshot.effects;
      this.commandHistory = getCommandHistoryFromSnapshot(snapshot.commands);
      this.powerupBox = new PowerupBox(this, snapshot.powerupBox);
      this.playerDaubCount = _.toInteger(snapshot.pDaub);
      this._ranks = snapshot.ranks;
      this.randomDaubBingoConfig = snapshot.randomDaubBingoConfig;
      this.extraBingoDaubHistory = snapshot.extraBingoDaubHistory;
      this.operationIndex = snapshot.operationIndex;
      this.cannotBigWin = snapshot.cannotBigWin;
      this.lastChanceInterVal = snapshot.lastChanceInterVal;
      this.lastChanceEndTime  = snapshot.lastChanceEndTime;
    } else {
      throw new Error('Invalid operation.');
    }

    // Clues
    if (enableClues) this.clues = [];
  }

  public receiveCall(roundTime: number, payload?: any): void {
    this.roundHost.rule.checkState?.(this);
    const targetCallIndex = _.clamp(
        _.floor(roundTime / this.roundHost.callInterval) - 1,
        -1,
        this.roundHost.fullCallList.length - 1,
    );
    if (payload && payload.triggerLastChance) {
      this.setTriggerLastChance();
    }
    // Cannot receive backward
    if (targetCallIndex <= this.receivedCallIndex) return;

    this.receivedCallIndex = targetCallIndex;
    logger.info('coreReceiveCallEnd', {roundTime, targetCallIndex});
  }

  public getReceivedCallList(): number[] {
    return _.take(this.roundHost.fullCallList, this.receivedCallIndex + 1);
  }

  /** Return if succeed */
  public tryPlayerDaub(roundTime: number, cardIndex: number, cellIndex: number): boolean {
    const targetCard = this.cards[cardIndex];
    if (!targetCard) {
      logger.error('coreDaubErrorTargetCard', {
        roundTime, cardIndex, cellIndex, targetCard
      });
      return false;
    }
    if (targetCard.isBingoed) {
      logger.error('coreDaubErrorBingoed', {
        roundTime, cardIndex, cellIndex, targetCard
      });
      return false;
    }

    const targetCell = targetCard.cells[cellIndex];
    if (!targetCell) {
      logger.error('coreDaubErrorTargetCell', {
        roundTime, cardIndex, cellIndex, targetCard
      });
      return false;
    }

    // Already daubed
    if (targetCell.isDaubed) {
      logger.error('coreDaubErrorDaubed', {
        roundTime, cardIndex, cellIndex, targetCard
      });
      return false;
    }

    // Auto Receive Call
    this.receiveCall(roundTime);

    const isCellValueCalled = _.indexOf(
        this.getReceivedCallList(),
        targetCell.value,
    ) !== -1;
    if (!isCellValueCalled) {
      logger.error('daubErrorNotCalled', {
        roundTime, cardIndex, cellIndex, targetCard
      });
      return false;
    }

    this.forceDaub(targetCard, targetCell, false);
    this.powerupBox?.chargeOnce(roundTime);
    this.playerDaubCount += 1;
    this.operateSuc(OperateType.Daub);

    logger.info('coreTryPlayerDaubEnd', {roundTime, cardIndex, cellIndex});

    return true;
  }

  /**
   * Daub
   * @param card The card.
   * @param cell The cell.
   * @param bypassAfterDaubCallback Use this to control callback behaviour.
   * @param byReceiveCallDaub 正常叫号涂抹
   */
  public forceDaub(card: Card, cell: CardCell, bypassAfterDaubCallback = false, byReceiveCallDaub = true): void {
    if (cell.isDaubed) return;

    if (!byReceiveCallDaub) {
      //关卡内其他技能导致涂抹
      cell.pushEffect(CellEffect.MarkAsDaubByEffect, 1, {cardIdx: card.index, cellIdx: cell.index});
    }

    card.markCellAsDaubed(cell.index);

    // Handle forced bingo effect commonly
    if (cell.hasEffect(CellEffect.ForcedBingo)) {
      card.markAsCanBingo();
    }

    if (!bypassAfterDaubCallback) {
      this.roundHost.rule.afterCellDaub(this, card, cell);
    }
    card.testIfCardCanBingo(this.roundHost.rule.bingoTester);
  }

  public tryBingo(time: number, cardIndex: number): boolean {
    const targetCard = this.cards[cardIndex];

    // check2(false, error_TryBingo_isBingoed, {
    //   msg: 'bingoStubedError', time, cardIndex,
    // });

    check2(targetCard, error_TryBingo, {
      msg: 'coreTryBingoErrorTargetCardNotFound', time,
      cardIndex
    });
    check2(!targetCard.isBingoed, error_TryBingo_isBingoed, {
      msg: 'coreTryBingoErrorIsBingoed', time,
      cardIndex
    });
    check2(time - targetCard.lastTryBingoTime >= this.badBingoPenaltyTime, error_TryBingo_badTime, {
      msg: 'coreTryBingoErrorBadBingoCd',
      time,
      cardIndex,
      lastTryBingoTime: targetCard.lastTryBingoTime,
      badBingoPenaltyTime: this.badBingoPenaltyTime
    });

    targetCard.lastTryBingoTime = time;

    check2(targetCard.canBingo, error_TryBingo_canBingo, {
      msg: 'coreTryBingoErrorCanBingo', time,
      cardIndex
    });

    this.forceBingo(targetCard);
    // TODO: after bingo handler

    logger.debug('coreTryBingoEnd', {time, cardIndex});

    return true;
  }

  public forceBingo(card: Card): void {
    let rank = -99
    if (!this.extraBingoDaubHistory.daubCount) {
      if (this.checkJoinLastChance()) {
        logger.warn('coreForceBingoCp1', {
          remainingBingoCount: this.roundHost.remainingBingoCount,
          lastChanceEndTime: this.lastChanceEndTime,
          curRoundTime: this.curRoundTime,
          lastChanceInterVal: this.lastChanceInterVal,
          startTime: this.roundHost.roundStartTime,
        });
        this._ranks.push(-2);
        rank = -2
      } else {
        logger.warn('coreForceBingoCp2', {
          remainingBingoCount: this.roundHost.remainingBingoCount,
          lastChanceEndTime: this.lastChanceEndTime,
          curRoundTime: this.curRoundTime,
          lastChanceInterVal: this.lastChanceInterVal,
          startTime: this.roundHost.roundStartTime,
        });
        const rankInfo = this.roundHost.requestOneBingo();
        check2(rankInfo, error_TryBingo_zeroContinue, {
          msg: 'forceBingoCp1'
        });
        // @ts-ignore
        this._ranks.push(rankInfo.rank);
        // @ts-ignore
        rank = rankInfo?.rank
      }
    } else {
      logger.warn('coreForceBingoCp3', {
        remainingBingoCount: this.roundHost.remainingBingoCount,
        lastChanceEndTime: this.lastChanceEndTime,
        curRoundTime: this.curRoundTime,
        lastChanceInterVal: this.lastChanceInterVal,
        startTime: this.roundHost.roundStartTime,
      });
      this._ranks.push(-1);
      rank = -1
    }
    card.markAsBingoed(rank);
  }

  public markCardAsCanBingo(card: Card): void {
    card.markAsCanBingo();
  }

  public pushEffectToCell(card: Card, cell: CardCell, effect: CellEffect, value = 0): void {
    if (card.isBingoed) return;
    this.roundHost.rule.beforeCellMeetEffect(this, card, cell, [effect, value]);
    cell.pushEffect(effect, value);
  }

  public pullAllEffectFromCell(card: Card, cell: CardCell, effect: CellEffect, value?: number): void {
    cell.pullAllEffect(effect, value);
  }

  public recordCommand(command: RoundCommand): void {
    this.curRoundTime = command.time;
    this.commandHistory.push(command);
  }

  public getCollectedEffects(): EffectTuple<CardEffect>[] {
    const effects: EffectTuple<CardEffect>[] = [];

    _.forEach(this.cards, card => {
      if (!card.isBingoed) return;
      effects.push(...card.effects);
    });

    return effects;
  }

  public gatherRoundResult(): IRoundResult {
    const allCells = _.reduce(this.cards, (cells, card) => {
      return _.concat(cells, card.cells);
    }, [] as CardCell[]);
    const validDaubCount = _.filter(allCells,
        cell => cell.isDaubed && !cell.hasEffect(CellEffect.MarkAsDaubByInitialization)).length;

    const totalCellEffects: EffectTuple<CellEffect>[] = [];
    _.forEach(this.cards, card => {
      totalCellEffects.push(...card.getCollectedEffects());
    });

    const bingoCardIndexes = _.filter(this.cards, card => card.isBingoed).map(v => v.index);
    const bingoCount = bingoCardIndexes.length;

    const cardEffectsPerCard = _.map(this.cards, card => card.isBingoed ? _.clone(card.effects) : []); // TODO: move to card property

    const result: IRoundResult = {
      validDaubCount: validDaubCount,
      cardEffects: this.getCollectedEffects(),
      cellEffects: totalCellEffects,
      cardEffectsPerCard: cardEffectsPerCard,
      bingoCount: bingoCount,
      bingoCardIndexes: bingoCardIndexes,
      ranks: this._ranks,
      usedPowerups: _.map(_.slice(this.powerupBox.powerupUsages, 0, this.powerupBox.nextPowerupIndex), usage => usage.powerupId),
    };

    return result;
  }

  /**
   * Judge natural bingo and daub targets at RUNTIME.
   */
  private farseer() {
    const host = _.cloneDeep(this.roundHost);
    const progress = _.cloneDeep(this);
    _.forEach(host.fullCallList, (call, index) => {
      _.forEach(progress.cards, card => {
        if (card.canBingo) return;
        const targetCell = card.cellDictionary[call];
        if (!targetCell) return;

        //模拟5秒一个叫号
        progress.curRoundTime = (index + 1) * host.callInterval;
        if (host.rule.checkState) {
          host.rule.checkState(progress);
        }
        progress.tryPlayerDaub(Number.MAX_SAFE_INTEGER, card.index, targetCell.index);
      });
    });

    _.forEach(progress.cards, (card, cardIndex) => {
      const predict = host.rule.farsee(card);

      // Apply to original progress
      this.cards[cardIndex].naturalBingo = predict.naturalBingo;
      this.cards[cardIndex].daubOnceTarget = predict.daubOnceTarget;
      this.cards[cardIndex].daubTwiceTarget = predict.daubTwiceTarget;
    });
  }

  public recordClue<T>(cardIndex: number, getClue: () => T): void {
    if (!_.isArray(this.clues)) return;
    const clue = getClue();
    this.clues.push([cardIndex, clue]);
  }

  public pushEffect(type: ProgressEffect, value = 0, customData: any = null): void {
    this.effects.push([type, value, customData]);
  }

  public hasEffect(type: ProgressEffect, value?: number): boolean {
    return hasEffect(this.effects, type, value);
  }

  public findEffect(type: ProgressEffect): EffectTuple<ProgressEffect> | undefined {
    return findEffect(this.effects, type);
  }

  public findEffectValue(type: ProgressEffect): number | undefined {
    return findEffectValue(this.effects, type);
  }

  public updateEffectValue(type: ProgressEffect, value: number, customData?: any): boolean {
    return updateEffectValue(this.effects, type, value, customData);
  }


  public findSeed(): RandomSequenceState {
    const effect = this.findEffect(ProgressEffect.CurrentSeed);
    if (!effect) {
      throw new Error('Invalid operation.');
    }
    return effect[2] as RandomSequenceState;
  }

  public setSeed(rs: RandomSequence): void {
    this.updateEffectValue(ProgressEffect.CurrentSeed, 0, rs.getState());
  }

  generateCardRandomDaubScene(): void {
    for (const card of this.cards) {
      if (card.isBingoed) {
        continue;
      }
      card.generateRandomDaubScene(this);
    }
  }

  getExtraBingoDaubCount(): number {
    if (!this.randomDaubBingoConfig) {
      return 0;
    }
    return Math.random() < 0.5 ? 1 : 2;
  }

  operateSuc(type: OperateType): void {
    if (type === OperateType.Daub) {
      //
    } else if (type === OperateType.PowerUp) {
      //
    }
    this.operationIndex++;
  }

  private setTriggerLastChance(): void {
    //没有配置和触发过了
    if (this.lastChanceInterVal == null || this.lastChanceInterVal <= 0 || this.lastChanceEndTime > 0) {
      return;
    }
    if (this.roundHost.remainingBingoCount <= 0) {
      this.lastChanceEndTime = this.curRoundTime + this.lastChanceInterVal;
    }
  }

  private checkJoinLastChance(): boolean {
    if (this.lastChanceEndTime > 0 && this.curRoundTime < this.lastChanceEndTime) {
      return true;
    }
    return false;
  }
}

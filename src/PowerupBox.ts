import _ from 'lodash';
import {RandomSequence} from './RandomSequence';
import {IRoundProgressPreset, OperateType, PlayerRoundProgress} from './PlayerRoundProgress';
import {IWeight, pickRandomItems, pickRandomItemsWithMutation, randomOneItemByWeights, swap} from './Utils';
import {CellEffect} from './effects';
import {Card, NaturalBingoFlag} from './Card';
import {IMemento} from './IMemento';
import {logger} from './CoreUtils2';


export enum PowerupId {
  DaubOnce = 1,
  GenerateTicketReward = 2,
  GenerateBoxReward = 3,
  DaubTwice = 4,
  DoubleExp = 5,
  DoubleTicket = 6,
  ForcedBingo = 7,
  OverRandomDaubOnce = 8, //结束时随机涂抹一个 花钱逆天改命
  OverRandomDaubTwice = 9, //结束时随机涂抹两个 花钱逆天改命
}

//不需要在序列里的道具
const SPECIAL_POWER_UP = [PowerupId.OverRandomDaubOnce, PowerupId.OverRandomDaubTwice];

/**
 * 标示卡片是否能够逆天改命，逆天改命的道具有三种类型
 */
export enum CardFate {
  NoChange = 1,
  ExtraBingoByDaubOnce = 2,
  ExtraBingoByDaubTwice = 3,
  ExtraBingoByForcedBingo = 4,
}

enum CardFateCombinationMode {
  None = 0b000,
  DaubOnce = 0b001,
  DaubTwice = 0b010,
  ForcedBingo = 0b100,
  DaubOnceAndDaubTwice = 0b011,
  DaubOnceAndForcedBingo = 0b101,
  DaubTwiceAndForcedBingo = 0b110,
  All = 0b111,
}

export enum IsDaubable {
  WontBeDaubed = 0, // 表示 powerup 生成的 effect 不会被放置在可以被涂抹的位置
  WillBeDaubed = 1, // 反之
}

export interface IQualityWeight extends IWeight {
  quality: number;
}

export interface IPowerupTypeWeight extends IWeight {
  powerupId: number;
}

export interface IPowerupPreset {
  id: number;
  maxCountPerRound: number;
  effectCellCountPerCard: number;
  quality: number;
}

export interface IPowerupUsage {
  powerupId: number;
  originalId?: number;
  daubable?: IsDaubable[][];
}

export interface IPowerupBoxSnapshot {
  seed: number;
  index: number;
  usages: IPowerupUsage[];
  fates: CardFate[];
  charge: number;
  toCharge: number;
  toChargeMap: [number, number][];
  lastTime: number;
  cd: number;
}

export class PowerupBox implements IMemento<IPowerupBoxSnapshot> {
  // public readonly preset: IPowerupBoxPreset;
  private readonly _progress: PlayerRoundProgress;

  public readonly seed: number;
  public readonly powerupsCooldownTime: number;
  public readonly daubCountToChargePowerups: number = -1;
  public daubCountToChargePowerupsMaps: Map<number, number> = new Map();
  // public readonly powerups: number[];
  public readonly powerupUsages: IPowerupUsage[];
  public readonly cardFates: CardFate[];
  public nextPowerupIndex: number;
  public chargeCount: number;
  public lastUsedRoundTime: number;

  constructor(progress: PlayerRoundProgress, snapshot?: IPowerupBoxSnapshot, preset?: IRoundProgressPreset) {
    if (preset) {
      this._progress = progress;
      this.seed = preset.powerupBoxSeed;
      this.powerupsCooldownTime = preset.powerupsCooldownTime;
      if(preset.daubCountToChargePowerupsArray) {
        this.daubCountToChargePowerupsMaps = new Map(preset.daubCountToChargePowerupsArray);
      } else {
        this.daubCountToChargePowerups = preset.daubCountToChargePowerups || -1;
      }
      this.cardFates = this.determineCardFates(preset);
      this.powerupUsages = this.determineUsages(preset, progress);
      this.nextPowerupIndex = 0;
      this.chargeCount = 0;
      this.lastUsedRoundTime = -preset.powerupsCooldownTime;
    } else if (snapshot) {
      this._progress = progress;
      this.seed = snapshot.seed;
      this.powerupsCooldownTime = snapshot.cd;
      this.daubCountToChargePowerups = snapshot.toCharge;
      this.daubCountToChargePowerupsMaps = snapshot.toChargeMap ? new Map(snapshot.toChargeMap) : new Map();
      this.powerupUsages = snapshot.usages;
      this.cardFates = snapshot.fates;
      this.nextPowerupIndex = snapshot.index;
      this.chargeCount = snapshot.charge;
      this.lastUsedRoundTime = snapshot.lastTime;
    } else {
      throw new Error('Invalid operation.');
    }
  }

  public getSnapshot(): IPowerupBoxSnapshot {
    const snapshot: IPowerupBoxSnapshot = {
      seed: this.seed,
      usages: this.powerupUsages,
      index: this.nextPowerupIndex,
      charge: this.chargeCount,
      toCharge: this.daubCountToChargePowerups,
      lastTime: this.lastUsedRoundTime,
      cd: this.powerupsCooldownTime,
      fates: this.cardFates,
      toChargeMap: Array.from(this.daubCountToChargePowerupsMaps.entries())
    };

    return snapshot;
  }

  public static boxLength = 25;

  private determineCardFates(preset: IRoundProgressPreset): CardFate[] {
    //根据是否可以big win 获取可以逆天改命的次数
    let canChangeBingoCount = this._progress.cards.filter(v => v.naturalBingo !== NaturalBingoFlag.Yes).length - (this._progress.cannotBigWin ? 1 : 0);
    const fates = _.map(preset.cardPresets, (cardPreset, index) => {
      const fateRate = new RandomSequence(cardPreset.seed).nextSingle();
      // 没有逆天改命次数直接返回
      if (this._progress.cards[index].naturalBingo === NaturalBingoFlag.No && canChangeBingoCount <= 0) {
        return CardFate.NoChange;
      }
      // 逆天改命次数减一
      if (fateRate <= preset.powerupCausedExtraBingoRate[PowerupId.ForcedBingo] && this._progress.cards[index].naturalBingo === NaturalBingoFlag.No) {
        canChangeBingoCount--;
      }
      if (fateRate <= preset.powerupCausedExtraBingoRate[PowerupId.DaubOnce]) return CardFate.ExtraBingoByDaubOnce;
      if (fateRate <= preset.powerupCausedExtraBingoRate[PowerupId.DaubTwice]) return CardFate.ExtraBingoByDaubTwice;
      if (fateRate <= preset.powerupCausedExtraBingoRate[PowerupId.ForcedBingo]) return CardFate.ExtraBingoByForcedBingo;
      return CardFate.NoChange;
    });
    return fates;
  }

  private determineUsages(preset: IRoundProgressPreset, progress: PlayerRoundProgress): IPowerupUsage[] {
    const powerupIds = this.determinePowerupTypes(preset);

    // Powerup 的使用行为由且仅由 card seed 和 powerup box seed 共同决定
    const rsPerCard = _.map(preset.cardPresets, p => new RandomSequence(Math.abs(p.seed - preset.powerupBoxSeed)));

    const usages = _.map(powerupIds, (powerupId) => {
      const usage: IPowerupUsage = {powerupId};

      _.forEach(preset.powerupReplacements, replacement => {
        if (powerupId === replacement.originalId) {
          usage.originalId = powerupId;
          usage.powerupId = replacement.targetId;
        }
      });

      const daubable = this.determineDaubables(preset, usage.powerupId, rsPerCard);
      if (daubable) usage.daubable = daubable;
      return usage;
    });

    // 主游戏逻辑.xlsx - 监控玩家道具数量
    // 修改 DoubleTicket 被点中的几率
    let anyBingoThisRound = false;
    _.forEach(progress.cards, (card, cardIndex) => {
      if (card.naturalBingo === NaturalBingoFlag.Yes) {
        anyBingoThisRound = true;
        return false;
      }

      if (this.cardFates[cardIndex] === CardFate.NoChange) return;

      anyBingoThisRound = true;
      return false;
    });

    if (!anyBingoThisRound) {
      const daubRates = preset.powerupEffectDaubableRateMap[preset.cardCountOptionIndex][preset.betOptionIndex];
      const doubleTicketDaubableRate = _.find(daubRates, c => c.powerupId === PowerupId.DoubleTicket);
      if (!doubleTicketDaubableRate) return usages;
      const noBingoDoubleTicketDaubableRate = doubleTicketDaubableRate.rate * preset.doubleTicketPowerupNoBingoFactor;

      _.forEach(usages, u => {
        if (u.powerupId !== PowerupId.DoubleTicket) return;
        u.daubable = this.determineDaubablesByRate(preset, PowerupId.DoubleTicket, noBingoDoubleTicketDaubableRate, rsPerCard);
      });
    }

    return usages;
  }

  private determineDaubables(preset: IRoundProgressPreset, powerupId: PowerupId, rsPerCard: RandomSequence[]): IsDaubable[][] | null {
    const powerupPreset = PowerupBox.getPowerupPreset(preset, powerupId);

    const daubRates = preset.powerupEffectDaubableRateMap[preset.cardCountOptionIndex][preset.betOptionIndex];
    const daubRateConfig = _.find(daubRates, (c) => c.powerupId === powerupId);
    if (!daubRateConfig) return null;

    const daubableRate = daubRateConfig.rate;
    const effectCellCount = powerupPreset.effectCellCountPerCard;

    const daubableMap = _(this._progress.cards).map((card, cardIndex) => (
        _(effectCellCount).range().map(() => rsPerCard[cardIndex].nextSingle() < daubableRate ? IsDaubable.WillBeDaubed : IsDaubable.WontBeDaubed).value()
    )).value();

    return daubableMap;
  }

  private determineDaubablesByRate(preset: IRoundProgressPreset, powerupId: PowerupId, daubableRate: number, rsPerCard: RandomSequence[]) {
    const powerupPreset = PowerupBox.getPowerupPreset(preset, powerupId);
    const effectCellCount = powerupPreset.effectCellCountPerCard;
    const daubableMap = _(this._progress.cards).map((card, cardIndex) => (
        _(effectCellCount).range().map(() => rsPerCard[cardIndex].nextSingle() < daubableRate ? IsDaubable.WillBeDaubed : IsDaubable.WontBeDaubed).value()
    )).value();

    return daubableMap;
  }

  public getCurrentChargeTarget(): number {
    const usage = this.powerupUsages[this.nextPowerupIndex];
    let toChargeCount: number;
    if(this.daubCountToChargePowerupsMaps.size > 0 && this.daubCountToChargePowerupsMaps.has(usage.powerupId)) {
        toChargeCount = this.daubCountToChargePowerupsMaps.get(usage.powerupId) as number;
    } else if(this.daubCountToChargePowerups != -1) {
        toChargeCount = this.daubCountToChargePowerups;
    } else {
        throw new Error('Invalid daubCountToChargePowerups');
    }
    return toChargeCount;
  }

  public chargeOnce(roundTime: number): void {
    // 加入时间之前的涂抹不计入充能
    if (roundTime < this._progress.joinRoundTime) return;
    // 冷却时间
    if (roundTime < this.lastUsedRoundTime + this.powerupsCooldownTime) return;

    this.chargeCount = _.clamp(this.chargeCount + 1, 0, this.getCurrentChargeTarget());
  }

  public powerupFullCharged(): boolean {
    let toChargeCount = this.getCurrentChargeTarget();
    return this.chargeCount >= toChargeCount;
  }

  public overRandomDaub(rs: RandomSequence, daubCount: number): boolean {
    this._progress.generateCardRandomDaubScene();
    const cardCount = this._progress.cards.length;
    const noBingoCard = this._progress.cards.filter(v => !v.isBingoed);
    const noBingoCount = noBingoCard.length;
    if (!noBingoCount || !this._progress.randomDaubBingoConfig) {
      return false;
    }
    const bingoRate = this._progress.randomDaubBingoConfig[cardCount - 1][noBingoCount - 1];
    const fetchDaubIndex = function (card: Card, isBingo: boolean, daubCount: number) {
      const map = new Map();
      card.randomDaubScene.forEach(v => map.set(v.isBingo + '_' + v.daubCount, v.daubIndex));
      if (!map.size) {
        return null;
      }
      if (map.has(isBingo + '_' + daubCount)) {
        return {isBingo, realDaubCount: daubCount, daubIndex: map.get(isBingo + '_' + daubCount)};
      }
      if (map.has(!isBingo + '_' + daubCount)) {
        return {isBingo: !isBingo, realDaubCount: daubCount, daubIndex: map.get(!isBingo + '_' + daubCount)};
      }
      if (map.has(isBingo + '_' + (daubCount - 1))) {
        return {isBingo, realDaubCount: daubCount - 1, daubIndex: map.get(isBingo + '_' + (daubCount - 1))};
      }
      if (map.has(!isBingo + '_' + (daubCount - 1))) {
        return {isBingo: !isBingo, realDaubCount: daubCount - 1, daubIndex: map.get(!isBingo + '_' + (daubCount - 1))};
      }
      return null;
    };
    this._progress.extraBingoDaubHistory.daubCount = daubCount;
    this._progress.overRandomDaubFlag = [];
    //如果不能 big win，那么能bingo的卡片数量减1, 保证不能全bingo
    let canBingoCount = noBingoCount - (this._progress.cannotBigWin ? 1 : 0);
    for (const card of noBingoCard) {
      const rate = rs.nextDouble();
      const canBingo = rate < bingoRate && canBingoCount > 0;
      const result = fetchDaubIndex(card, canBingo, daubCount);
      this._progress.overRandomDaubFlag.push(canBingo);
      if (!result) {
        continue;
      }
      if (canBingo) {
        canBingoCount--;
      }
      const {daubIndex, realDaubCount, isBingo} = result;
      this._progress.extraBingoDaubHistory.totalDaubCount += realDaubCount;
      this._progress.extraBingoDaubHistory.addBingoCount += Number(isBingo);
      const randomDaubCell = daubIndex.map((index: number) => card.cellAtIndex(index));
      for (const cell of randomDaubCell) {
        this._progress.pushEffectToCell(card, cell, CellEffect.MarkAsDaubByRocket, daubCount);
      }
    }
    this._progress.operateSuc(OperateType.PowerUp);
    return true;
  }

  public useSpecialPowerUp(powerUpId: PowerupId): boolean {
    if (!SPECIAL_POWER_UP.includes(powerUpId)) {
      throw new Error('Invalid use special powerup');
    }
    const rs = new RandomSequence(this.seed);
    switch (powerUpId) {
      case PowerupId.OverRandomDaubOnce: {
        return this.overRandomDaub(rs, 1);
      }
      case PowerupId.OverRandomDaubTwice: {
        return this.overRandomDaub(rs, 2);
      }
    }
    throw new Error('Invalid use special powerup');
  }

  public usePowerupUsage(roundTime: number, powerUpId?: PowerupId): boolean {
    if (powerUpId) {
      return this.useSpecialPowerUp(powerUpId);
    }
    if (!this.powerupFullCharged()) {
      logger.error('coreTryUsePowerupCommandError-powerupFullCharged', {..._.pick(this, ['chargeCount', 'daubCountToChargePowerups', 'daubCountToChargePowerupsMaps'])});
      return false;
    }
    const usage = this.powerupUsages[this.nextPowerupIndex];
    const rs = new RandomSequence(_.toSafeInteger(this.seed + this.nextPowerupIndex));

    try {
      switch (usage.powerupId) {
        case PowerupId.GenerateBoxReward:
          if (!usage.daubable) throw new Error('Invalid powerup usage');
          this.putCellEffects(CellEffect.BoxReward, usage.daubable, rs);
          break;

        case PowerupId.GenerateTicketReward:
          if (!usage.daubable) throw new Error('Invalid powerup usage');
          this.putCellEffects(CellEffect.TicketReward, usage.daubable, rs);
          break;

        case PowerupId.DoubleExp:
          if (!usage.daubable) throw new Error('Invalid powerup usage');
          this.putCellEffects(CellEffect.DoubleExp, usage.daubable, rs);
          break;

        case PowerupId.DoubleTicket:
          if (!usage.daubable) throw new Error('Invalid powerup usage');
          this.putCellEffects(CellEffect.DoubleTicket, usage.daubable, rs);
          break;

        case PowerupId.ForcedBingo: {
          _(this._progress.cards).forEach((card, cardIndex) => {
            if (card.isBingoed) return;
            const cardFate = this.cardFates[cardIndex];
            if (!cardFate) throw new Error('Invalid powerup usage');

            if (card.naturalBingo === NaturalBingoFlag.Yes) {
              if (!usage.daubable || !usage.daubable[cardIndex]) throw new Error('Invalid powerup usage');
              this.putCellEffectsToCard(CellEffect.ForcedBingo, card, usage.daubable[cardIndex], rs);
              return;
            }

            if (cardFate !== CardFate.ExtraBingoByForcedBingo) {
              this.putEffectToNoExclusiveEffectCellWontBeDaubed(CellEffect.ForcedBingo, card, rs);
              return;
            }

            // 逆天改命
            this.putEffectToNoExclusiveEffectCellWillBeDaubed(CellEffect.ForcedBingo, card, rs);
            return;
          });
          break;
        }

        case PowerupId.DaubOnce: {
          _(this._progress.cards).forEach((card, cardIndex) => {
            if (card.isBingoed) return;
            const cardFate = this.cardFates[cardIndex];
            if (!cardFate) throw new Error('Invalid powerup usage');

            if (card.naturalBingo === NaturalBingoFlag.Yes) {
              const noDaubCells = card.getNoDaubCells();
              if (noDaubCells.length <= 0) return;
              // 如果卡片能够自然bingo，随机放置一次涂抹
              const randomNoDaubCell = pickRandomItemsWithMutation(noDaubCells, 1, rs)[0];
              this._progress.pushEffectToCell(card, randomNoDaubCell, CellEffect.MarkAsDaubByPowerup);
              return;
            }

            if (cardFate !== CardFate.ExtraBingoByDaubOnce || card.daubOnceTarget === -1) {
              // 放置在会被叫号的格子上
              this.putEffectToCellWillBeDaubed(CellEffect.MarkAsDaubByPowerup, card, rs);
              return;
            }

            // 逆天改命
            const targetCellIndex = card.daubOnceTarget;
            if (card.cellAtIndex(targetCellIndex).isDaubed) { // For robustness
              this.putEffectToCellWontBeDaubed(CellEffect.MarkAsDaubByPowerup, card, rs);
            } else {
              this._progress.pushEffectToCell(card, card.cellAtIndex(targetCellIndex), CellEffect.MarkAsDaubByPowerup);
            }
            return;
          });
          break;
        }

        case PowerupId.DaubTwice: {
          _(this._progress.cards).forEach((card, cardIndex) => {
            if (card.isBingoed) return;
            const cardFate = this.cardFates[cardIndex];
            if (!cardFate) throw new Error('Invalid powerup usage');

            if (card.naturalBingo === NaturalBingoFlag.Yes) {
              // 如果卡片能够自然bingo，随机放置两次涂抹
              const noDaubCells = card.getNoDaubCells();
              if (noDaubCells.length >= 2) {
                const randomNoDaubCells = pickRandomItemsWithMutation(noDaubCells, 2, rs);
                _.forEach(randomNoDaubCells, cell => {
                  this._progress.pushEffectToCell(card, cell, CellEffect.MarkAsDaubByPowerup);
                });
                return;
              }

              // 应该极少出现剩余未涂抹格子小于2的情况，这时随机选择涂抹
              const randomCells = pickRandomItems(card.cells, 2, rs);
              _.forEach(randomCells, cell => {
                this._progress.pushEffectToCell(card, cell, CellEffect.MarkAsDaubByPowerup);
              });
              return;
            }

            const cannotBingoInTwoDaub = card.daubOnceTarget === -1 && (card.daubTwiceTarget[0] === -1 || card.daubTwiceTarget[1] === -1);
            if (cardFate !== CardFate.ExtraBingoByDaubTwice || cannotBingoInTwoDaub) {
              // 放置两次在会被叫号的格子上
              this.putEffectToCellWillBeDaubed(CellEffect.MarkAsDaubByPowerup, card, rs);
              this.putEffectToCellWillBeDaubed(CellEffect.MarkAsDaubByPowerup, card, rs);
              return;
            }

            // 逆天改命
            const targetIndexes = [];
            if (card.daubOnceTarget !== -1) targetIndexes.push(card.daubOnceTarget);
            if (card.daubTwiceTarget[0] !== -1 && card.daubTwiceTarget[1] !== -1) targetIndexes.push(...card.daubTwiceTarget);

            const firstAvailableCells = _(targetIndexes).map(i => card.cells[i]).filter(c => c && !c.isDaubed).unionBy(c => c.index);

            _(2).range().forEach(() => {
              const targetCell = firstAvailableCells.pop();
              if (targetCell) {
                this._progress.pushEffectToCell(card, targetCell, CellEffect.MarkAsDaubByPowerup);
              } else { // For robustness
                this.putEffectToCellWontBeDaubed(CellEffect.MarkAsDaubByPowerup, card, rs);
              }

            });
            return;
          });
          break;
        }

        default:
          throw new Error('Unknown powerup.');
      }
    } catch (e) {
      logger.error('coreTryUsePowerupCommandError-switchPowerUpId', {e, ..._.pick(usage, ['powerupId', 'daubable'])});
      return false;
    }

    this._progress.operateSuc(OperateType.PowerUp);
    this.lastUsedRoundTime = roundTime;
    this.chargeCount = 0;
    this.nextPowerupIndex += 1;

    logger.info('coreTryUsePowerupCommandEnd', {roundTime, usage});

    return true;
  }

  private putCellEffects(effect: CellEffect, daubableMap: IsDaubable[][], rs: RandomSequence): void {
    _.forEach(daubableMap, (daubables, cardIndex) => { // Each card
      this.putCellEffectsToCard(effect, this._progress.cards[cardIndex], daubables, rs);
    });
  }

  private putCellEffectsToCard(effect: CellEffect, card: Card, daubables: IsDaubable[], rs: RandomSequence): void {
    _.forEach(daubables, daubable => { // Each generated effect
      if (daubable === IsDaubable.WillBeDaubed) {
        this.putEffectToNoExclusiveEffectCellWillBeDaubed(effect, card, rs);
      } else {
        this.putEffectToNoExclusiveEffectCellWontBeDaubed(effect, card, rs);
      }
    });
  }

  /**
   * 生成一个effect在 没有被涂抹，且将会被涂抹，且没有exclusive effect的格子上
   * @param effect
   * @param card
   * @param rs
   */
  private putEffectToNoExclusiveEffectCellWillBeDaubed(effect: CellEffect, card: Card, rs: RandomSequence): void {
    const cellsWillBeDaubed = card.getNoDaubCellsInCallList(this._progress.roundHost.fullCallList);
    const cellsCanPutEffect = card.getNoExclusiveEffectCells();

    const cellsCanPutEffectAndWillBeDaubed = _.intersection(cellsCanPutEffect, cellsWillBeDaubed);
    // 有正常的候选，将生成在没有被涂抹且会被涂抹且能放置effect的位置
    if (cellsCanPutEffectAndWillBeDaubed.length > 0) {
      const targetCell = pickRandomItemsWithMutation(cellsCanPutEffectAndWillBeDaubed, 1, rs)[0];
      this._progress.pushEffectToCell(card, targetCell, effect);
      return;
    }

    // 无可用候选，不管能否被涂抹，将生成在没有被涂抹且能放置effect的位置
    const noDaubCells = card.getNoDaubCells();
    const noDaubCellsCanPutEffect = _.intersection(cellsCanPutEffect, noDaubCells);
    if (noDaubCellsCanPutEffect.length > 0) {
      const targetCell = pickRandomItemsWithMutation(noDaubCellsCanPutEffect, 1, rs)[0];
      this._progress.pushEffectToCell(card, targetCell, effect);
      return;
    }

    // 已经没有可以生成的位置了，则放弃生成
    return;
  }

  /**
   * 生成一个effect在 没有被涂抹，且不会被涂抹，且没有exclusive effect的格子上
   * @param effect
   * @param card
   * @param rs
   * @returns
   */
  private putEffectToNoExclusiveEffectCellWontBeDaubed(effect: CellEffect, card: Card, rs: RandomSequence): void {
    const cellsWontBeDaubed = card.getNoDaubCellsNotInCallList(this._progress.roundHost.fullCallList);
    const cellsCanPutEffect = card.getNoExclusiveEffectCells();

    const cellsCanPutEffectAndWontBeDaubed = _.intersection(cellsCanPutEffect, cellsWontBeDaubed);
    // 有正常的候选，将生成在没有被涂抹且不会被涂抹且能放置effect的位置
    if (cellsCanPutEffectAndWontBeDaubed.length > 0) {
      const targetCell = pickRandomItemsWithMutation(cellsCanPutEffectAndWontBeDaubed, 1, rs)[0];
      this._progress.pushEffectToCell(card, targetCell, effect);
      return;
    }

    // 无可用候选，不管能否被涂抹，将生成在没有被涂抹且能放置effect的位置
    const noDaubCells = card.getNoDaubCells();
    const noDaubCellsCanPutEffect = _.intersection(cellsCanPutEffect, noDaubCells);
    if (noDaubCellsCanPutEffect.length > 0) {
      const targetCell = pickRandomItemsWithMutation(noDaubCellsCanPutEffect, 1, rs)[0];
      this._progress.pushEffectToCell(card, targetCell, effect);
      return;
    }

    // 已经没有可以生成的位置了，则放弃生成
    return;
  }

  /**
   * 生成一个effect在 没有被涂抹，且将会被涂抹的格子上
   * @param effect
   * @param card
   * @param rs
   */
  private putEffectToCellWillBeDaubed(effect: CellEffect, card: Card, rs: RandomSequence): void {
    const cellsWillBeDaubed = card.getNoDaubCellsInCallList(this._progress.roundHost.fullCallList);

    if (cellsWillBeDaubed.length > 0) {
      const targetCell = pickRandomItemsWithMutation(cellsWillBeDaubed, 1, rs)[0];
      this._progress.pushEffectToCell(card, targetCell, effect);
      return;
    }

    // 无可用候选，生成在任意未被涂抹的格子
    const noDaubCells = card.getNoDaubCells();
    if (noDaubCells.length > 0) {
      const targetCell = pickRandomItemsWithMutation(noDaubCells, 1, rs)[0];
      this._progress.pushEffectToCell(card, targetCell, effect);
      return;
    }

    // 已经没有可以生成的位置了，则放弃生成
    return;
  }

  /**
   * 生成一个effect在 没有被涂抹，且不会被涂抹的格子上
   * @param effect
   * @param card
   * @param rs
   */
  private putEffectToCellWontBeDaubed(effect: CellEffect, card: Card, rs: RandomSequence): void {
    const cellsWontBeDaubed = card.getNoDaubCellsNotInCallList(this._progress.roundHost.fullCallList);

    if (cellsWontBeDaubed.length > 0) {
      const targetCell = pickRandomItemsWithMutation(cellsWontBeDaubed, 1, rs)[0];
      this._progress.pushEffectToCell(card, targetCell, effect);
      return;
    }

    // 无可用候选，生成在任意未被涂抹的格子
    const noDaubCells = card.getNoDaubCells();
    if (noDaubCells.length > 0) {
      const targetCell = pickRandomItemsWithMutation(noDaubCells, 1, rs)[0];
      this._progress.pushEffectToCell(card, targetCell, effect);
      return;
    }

    // 已经没有可以生成的位置了，则放弃生成
    return;
  }

  private static getPowerupPreset(preset: IRoundProgressPreset, powerupId: PowerupId): IPowerupPreset {
    const target = _.find(preset.powerupPresets, {id: powerupId});
    if (!target) throw new Error('Invalid powerup id.');
    return target;
  }

  private static getQualityOfPowerupId(preset: IRoundProgressPreset, id: number): number | undefined {
    const targetPreset = _.find(preset.powerupPresets, {id});
    return targetPreset?.quality;
  }

  public determinePowerupTypes(preset: IRoundProgressPreset): PowerupId[] {
    ///// Reference Doc: 主游戏逻辑.xlsx /////

    const rs = new RandomSequence(preset.powerupBoxSeed);

    const allQualitySorted = _(preset.powerupPresets).map(p => p.quality).sortedUniq().value();
    if (allQualitySorted.length <= 0) throw new Error('Invalid configuration');

    // #1 道具生成序列
    const qualities = _(PowerupBox.boxLength).range().map(powerupIndex => {
      const qualityWeights = _.find(preset.regionQualityWeights, r => (powerupIndex >= r.fromIndex && powerupIndex < r.toIndexExclusive));
      if (!qualityWeights) throw new Error('Invalid configuration');
      const thisQuality = randomOneItemByWeights(qualityWeights.weights, rs).quality;
      return thisQuality;
    }).value();

    const idMapToAvailableCount = _(preset.powerupPresets).keyBy('id').mapValues(p => p.maxCountPerRound).value();
    let prevId: number | null = null;

    const powerupIdsBeforeCorrection = _.map(qualities, quality => {
      const biggestQualityIndex = _.findIndex(allQualitySorted, q => q === quality);

      let options: IPowerupTypeWeight[] | null = null;
      let qualityIndex = biggestQualityIndex;
      while (qualityIndex >= 0) {
        const thisQualityOptions = _.filter(preset.powerupWeights, w => {
          const isThisQuality = PowerupBox.getQualityOfPowerupId(preset, w.powerupId) === allQualitySorted[qualityIndex];
          const isNotPreviousId = w.powerupId !== prevId;
          const isAvailableCount = idMapToAvailableCount[w.powerupId] > 0;
          return isThisQuality && isNotPreviousId && isAvailableCount;
        });
        if (thisQualityOptions.length > 0) {
          options = thisQualityOptions;
          break;
        }
        qualityIndex -= 1;
      }

      if (options === null) throw new Error('Invalid configuration');

      const targetId = randomOneItemByWeights(options, rs).powerupId;
      prevId = targetId;
      idMapToAvailableCount[targetId] -= 1;
      return targetId;
    });

    // #2 修正道具序列
    const insertIndexEnd = preset.powerupCorrectionEndIndexByCardCountOptionIndex[preset.cardCountOptionIndex];
    if (!(insertIndexEnd >= 0)) throw new Error('Invalid configuration');
    const powerupMinimumAmountRequirement = randomOneItemByWeights(preset.powerupMinimumAmountRequirements, rs).requirement;
    const powerupIdMapToMinimumCount = _.mapValues(_.keyBy(powerupMinimumAmountRequirement, 'powerupId'), r => r.amount);
    _.forEach(preset.powerupPresets, powerupPreset => {
      const thisPowerupCount = _.filter(powerupIdsBeforeCorrection, id => id === powerupPreset.id).length;
      const thisPowerupRequirement = powerupIdMapToMinimumCount[powerupPreset.id];
      if (thisPowerupRequirement === undefined) throw new Error('Invalid configuration');
      if (thisPowerupCount < thisPowerupRequirement) {
        const insertIndexOptions = _.range(0, insertIndexEnd + 1);
        const insertIndex = pickRandomItemsWithMutation(insertIndexOptions, 1, rs)[0];

        powerupIdsBeforeCorrection.splice(insertIndex, 0, powerupPreset.id); // In place insertion
      }
    });
    const powerupIds = _.take(powerupIdsBeforeCorrection, PowerupBox.boxLength) as PowerupId[];


    // #3 extra bingo powerup 插入
    let mode = CardFateCombinationMode.None;
    _.forEach(this.cardFates, (fate) => {
      if (fate === CardFate.NoChange) return;
      if (fate === CardFate.ExtraBingoByDaubOnce) {
        mode ^= CardFateCombinationMode.DaubOnce;
        return;
      }
      if (fate === CardFate.ExtraBingoByDaubTwice) {
        mode ^= CardFateCombinationMode.DaubTwice;
        return;
      }
      if (fate === CardFate.ExtraBingoByForcedBingo) {
        mode ^= CardFateCombinationMode.ForcedBingo;
        return;
      }
    });

    if (mode !== CardFateCombinationMode.None) {
      const swapIndexParam = preset.extraBingoPowerupSwapIndexByCardCountOptionIndex[preset.cardCountOptionIndex];
      switch (mode) {
        case CardFateCombinationMode.DaubOnce:
          PowerupBox.ensurePowerupPositionBeforeIndex(powerupIds, [PowerupId.DaubOnce, PowerupId.DaubTwice], swapIndexParam, rs);
          break;
        case CardFateCombinationMode.DaubTwice:
          PowerupBox.ensurePowerupPositionBeforeIndex(powerupIds, [PowerupId.DaubTwice], swapIndexParam, rs);
          break;
        case CardFateCombinationMode.ForcedBingo:
          PowerupBox.ensurePowerupPositionBeforeIndex(powerupIds, [PowerupId.ForcedBingo], swapIndexParam, rs);
          break;
        case CardFateCombinationMode.DaubOnceAndDaubTwice:
          PowerupBox.ensurePowerupPositionBeforeIndex(powerupIds, [PowerupId.DaubOnce, PowerupId.DaubTwice], swapIndexParam, rs);
          break;
        case CardFateCombinationMode.DaubTwiceAndForcedBingo:
          PowerupBox.ensurePowerupPositionBeforeIndex(powerupIds, [PowerupId.DaubTwice, PowerupId.ForcedBingo], swapIndexParam, rs);
          break;
        case CardFateCombinationMode.DaubOnceAndForcedBingo:
          PowerupBox.ensurePowerupPositionBeforeIndex(powerupIds, [PowerupId.DaubOnce, PowerupId.DaubTwice, PowerupId.ForcedBingo], swapIndexParam, rs);
          break;
        case CardFateCombinationMode.All:
          PowerupBox.ensurePowerupPositionBeforeIndex(powerupIds, [PowerupId.DaubOnce, PowerupId.DaubTwice, PowerupId.ForcedBingo], swapIndexParam, rs);
          break;
      }
    }

    // #4 由玩家道具数量余额触发的保证机制
    const balances = _.clone(preset.powerupBalances);
    _.forEach(powerupIds, (originalPowerupId, index) => {
      const remaining = balances[originalPowerupId];
      const replacement = preset.powerupBalanceReplacements[originalPowerupId];
      if (remaining === undefined || replacement === undefined) return;
      if (remaining > 0) {
        balances[originalPowerupId] -= 1;
        return;
      }

      const shouldBeReplaced = rs.nextSingle() < replacement.beReplacedRate;
      if (!shouldBeReplaced) return;

      const replacementCandidates = _(preset.powerupBalanceReplacements).toPairs().filter(([targetId, r]) => {
        if (r.replaceWeight <= 0) return false;

        const targetRemaining = balances[_.toInteger(targetId) as PowerupId];
        if (targetRemaining > 0) return true;

        return false;
      }).map(([targetId]) => _.toInteger(targetId) as PowerupId).value();
      if (replacementCandidates.length === 0) return;

      const replaceTargetId = pickRandomItemsWithMutation(replacementCandidates, 1, rs)[0];

      powerupIds[index] = replaceTargetId;
      balances[replaceTargetId] -= 1;
    });

    return powerupIds;
  }

  private static ensurePowerupPositionBeforeIndex(powerupIds: PowerupId[], targetIds: PowerupId[], targetIndex: number, rs: RandomSequence) {
    _.forEach(targetIds, targetPowerupId => {
      const firstTargetPowerupIndex = _.findIndex(powerupIds, id => id === targetPowerupId);
      if (firstTargetPowerupIndex > targetIndex) {
        const swapIndexCandidates: number[] = [];
        _(powerupIds).take(targetIndex + 1).forEach((id, index) => {
          if (_.indexOf(targetIds, id) !== -1) return;
          swapIndexCandidates.push(index);
        });

        // 没有候选，就不替换
        // assert(swapIndexCandidates.length >= 1, `No swap candidates. Wanna swap ${firstTargetPowerupIndex} to before ${targetIndex} in \n [${powerupIds.toString()}]`);
        if (swapIndexCandidates.length < 1) return;

        const randomIndexToSwap = pickRandomItems(swapIndexCandidates, 1, rs)[0];
        swap(powerupIds, firstTargetPowerupIndex, randomIndexToSwap);
      }
    });
  }
}

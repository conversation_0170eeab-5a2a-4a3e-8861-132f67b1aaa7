export type RandomSequenceState = [number, number, number, number]

/**
 * Random number generator implementation of xorshift128.
 */
export class RandomSequence {
  // private static _int32MaxValue = 2147483647;
  private static _int32MinValue = -2147483648;
  // private static _uint32MaxValue = 4294967295;

  private _x = 0;
  private _y = 0;
  private _z = 0;
  private _w = 0;

  /**
   * @param seed Seed can be a number or string, or an internal state.
   * @param isNew new seed
   */
  constructor(seed: number | string | RandomSequenceState, isNew = false) {
    if (Array.isArray(seed)) {
      // From state
      this._x = seed[0];
      this._y = seed[1];
      this._z = seed[2];
      this._w = seed[3];
      return;
    } else if (seed === ((seed as number) | 0) || (isNew && !isNaN(Number(seed)))) {
      // Number seed
      this._x = Number(seed);
    } else {
      // String seed
      for (let i = 0; i < (seed as string).length; i += 1) {
        this._x ^= (seed as string).charCodeAt(i);
        this.nextInt32();
      }
    }

    // Discard initial 64 values
    for (let k = 0; k < 64; k += 1) {
      this.nextInt32();
    }
  }

  /**
   * Get current state of sequence.
   * @returns Internal state representation.
   */
  getState(): RandomSequenceState {
    return [this._x, this._y, this._z, this._w];
  }

  /**
   * Get a 32 bit (signed) integer.
   * @returns Random 32-bit signed integer.
   */
  nextInt32(): number {
    const t = this._x ^ (this._x << 11);
    this._x = this._y;
    this._y = this._z;
    this._z = this._w;
    return this._w ^= (this._w >>> 19) ^ t ^ (t >>> 8);
  }

  /**
   * Get 32 bits of randomness in a floating number.
   * @returns Floating number in [0, 1).
   */
  nextSingle(): number {
    return (this.nextInt32() >>> 0) / 0x100000000;
  }

  /**
   * Get 56 bits of randomness in a floating number.
   * @returns Floating number in [0, 1).
   */
  nextDouble(): number {
    let top: number;
    let bot: number;
    let result: number;

    do {
      top = this.nextInt32() >>> 11;
      bot = (this.nextInt32() >>> 0) / 0x100000000;
      result = (top + bot) / (1 << 21);
    } while (result === 0);
    return result;
  }

  /**
   * Get an integer in the range [min, max] with 32 bits of randomness,
   * thus the length of range should be less than 4294967295.
   * @param max The max value of the range.
   * @param min The min value of the range, default to 0.
   * @returns Random integer in range [min, max]
   */
  nextIntegerInRange(max: number, min = 0): number {
    return (this.nextInt32() - RandomSequence._int32MinValue) % (max - min + 1) + min;
  }

  /**
   * Get an float number in the range [lower, upper)
   * @param lower The lower bound
   * @param upper The upper bound
   * @returns Random float number in range [lower, upper)
   */
  nextFloatInRange(lower: number, upper: number): number {
    return lower + this.nextSingle() * (upper - lower);
  }
}

import _ from 'lodash';

const RAND_LIMIT = 4294967296.0;

export class RandomSequenceLegacy {
  private _currentRandomNumber: number;
  private _initialSeed: number;

  constructor(seed?: number) {
    if (seed === undefined) {
      this._currentRandomNumber = RandomSequenceLegacy.getRandomSeed();
    } else {
      this._currentRandomNumber = seed;
    }
    this._initialSeed = this._currentRandomNumber;
  }

  private static getRandomSeed(): number {
    return Math.floor(RAND_LIMIT * Math.random());
  }

  public reset(): void {
    this._currentRandomNumber = this._initialSeed;
  }

  public nextRandomNumber(): number {
    const next = (this._currentRandomNumber * 1664525 + 1013904223) % 4294967296;
    this._currentRandomNumber = next;
    return next;
  }

  /** Random integer in [min, max] */
  public nextInteger(min: number, max: number): number {
    return Math.abs(this.nextRandomNumber() % (max - min + 1)) + min;
  }

  public nextFloat(lower: number, upper: number): number {
    this.nextRandomNumber(); // For small seed issue
    let r = this.nextRandomNumber();
    r /= RAND_LIMIT;
    r = (upper - lower) * r + lower;
    return Math.abs(r);
  }

  public nextRate(): number {
    return this.nextFloat(0, 1);
  }

  public generateRandomOrderedIntegers(lower: number, upper: number, count: number): number[] {
    if (upper < lower || count > (upper - lower + 1)) {
      throw new Error('Invalid arguments');
    }

    const numbers = _.range(lower, upper + 1);
    const result: number[] = [];
    _(count).range().forEach((i) => {
      const lastIndex = numbers.length - 1 - i;
      const randomIndex = this.nextInteger(0, lastIndex);
      result.push(numbers[randomIndex]);
      numbers[randomIndex] = numbers[lastIndex]; // Used number to the tail
    });
    
    return result;
  }
}

import _ from 'lodash';
import {IBingoRule} from './rules/IBingoRule';
import {IRoundProgressPreset, PlayerRoundProgress} from './PlayerRoundProgress';
import {getRuleById} from './rules';
import {IMemento} from './IMemento';
import {generateRandomOrderedIntegers} from './Utils';
import {RandomSequence} from './RandomSequence';

export interface IBingoHostAward {
  rank: number;
}

export interface IRoundHostPreset {
  ruleId: number;
  roundStartTime: number;
  callInterval: number;
  callSeed: number;
  maxCallCount: number;
  totalCardCountRange: { lower: number, upper: number };
  bingoCountRate: number;
  maxPlayerCount: number;
}

const MIN_CALL_NUMBER = 1;
const MAX_CALL_NUMBER = 75;

export interface IRoundHostSnapshot {
  ruleId: number;
  callSeed: number;
  fullCallList: number[];
  roundStartTime: number;
  callInterval: number;
  maxPlayerCount: number;
  playerCount: number;
  cardCount: number;
  remainingBingoCount: number;
  bingoHappenedCount: number;
}

export class RoundHost implements IMemento<IRoundHostSnapshot> {
  // public readonly preset: IRoundPreset;
  public readonly ruleId: number;
  public readonly callSeed: number;
  public readonly rule: IBingoRule;
  public readonly fullCallList: number[];
  public readonly callInterval: number;
  public readonly maxPlayerCount: number;
  public roundStartTime: number;
  public playerCount = 0;
  public cardCount = 0;
  public remainingBingoCount = 0;
  public bingoHappenedCount = 0;

  constructor(snapshot?: IRoundHostSnapshot, preset?: IRoundHostPreset) {
    if (preset) {
      // this.preset = preset;
      this.ruleId = preset.ruleId;
      this.callSeed = preset.callSeed;
      this.rule = getRuleById(preset.ruleId);
      this.roundStartTime = preset.roundStartTime;
      this.callInterval = preset.callInterval;
      // const rs = new RandomSequenceLegacy(preset.callSeed);
      // this.fullCallList = rs.generateRandomOrderedIntegers(MIN_CALL_NUMBER, MAX_CALL_NUMBER, preset.maxCallCount);
      const rs = new RandomSequence(preset.callSeed);
      this.fullCallList = generateRandomOrderedIntegers(MIN_CALL_NUMBER, MAX_CALL_NUMBER, preset.maxCallCount, rs);
      const {totalCardCountRange, bingoCountRate} = preset;
      this.remainingBingoCount = _.toSafeInteger(_.random(totalCardCountRange.lower, totalCardCountRange.upper) * bingoCountRate);
      this.maxPlayerCount = preset.maxPlayerCount;

    } else if (snapshot) {
      this.ruleId = snapshot.ruleId;
      this.callSeed = snapshot.callSeed;
      this.rule = getRuleById(snapshot.ruleId);
      this.fullCallList = snapshot.fullCallList;
      this.roundStartTime = snapshot.roundStartTime;
      this.callInterval = snapshot.callInterval;
      this.maxPlayerCount = snapshot.maxPlayerCount;
      this.playerCount = snapshot.playerCount;
      this.cardCount = snapshot.cardCount;
      this.remainingBingoCount = snapshot.remainingBingoCount;
      this.bingoHappenedCount = snapshot.bingoHappenedCount;
    } else {
      throw new Error('Invalid operation');
    }
  }

  getSnapshot(): IRoundHostSnapshot {
    const snapshot: IRoundHostSnapshot = {
      ruleId: this.ruleId,
      callSeed: this.callSeed,
      fullCallList: this.fullCallList,
      roundStartTime: this.roundStartTime,
      callInterval: this.callInterval,
      maxPlayerCount: this.maxPlayerCount,
      playerCount: this.playerCount,
      cardCount: this.cardCount,
      remainingBingoCount: this.remainingBingoCount,
      bingoHappenedCount: this.bingoHappenedCount,
    };

    return snapshot;
  }

  updateFromSnapshot(snapshot: IRoundHostSnapshot): void {
    this.playerCount = snapshot.playerCount;
    this.cardCount = snapshot.cardCount;
    this.remainingBingoCount = snapshot.remainingBingoCount;
    this.bingoHappenedCount = snapshot.bingoHappenedCount;
    this.roundStartTime = snapshot.roundStartTime;
  }

  /** If not allow join, return null. */
  tryJoinOnePlayerLegacy(preset: IRoundProgressPreset): PlayerRoundProgress | null {
    if (this.playerCount >= this.maxPlayerCount) return null;

    this.playerCount += 1;
    return new PlayerRoundProgress(this, undefined, preset, true);
  }

  /**
   * @param cardCount
   * @returns {boolean} Succeed
   */
  tryJoinOnePlayer(cardCount: number): boolean {
    if (this.playerCount >= this.maxPlayerCount) return false;
    this.playerCount += 1;
    this.cardCount += cardCount;

    return true;
  }

  adjustRoundStartTime(timestamp: number): void {
    this.roundStartTime = timestamp;
  }

  getCallCountAtTime(timestamp: number): number {
    const callCount = _.clamp(
        _.floor((timestamp - this.roundStartTime) / this.callInterval),
        0,
        this.fullCallList.length,
    );

    return callCount;
  }

  getFullCallEndAt(): number {

    ///// Call Timing ///// 
    //       |  <---- [call interval] ----> |  <---- [call interval] ----> | <---- ... ----> | <---- [call interval] ----> |
    //  [start time]                     [call 0]                       [call 1]   ...    [call n]                     [end time]

    return this.roundStartTime + this.callInterval * (this.fullCallList.length + 1);
  }

  //放开bingo的限制
  requestOneBingo(): IBingoHostAward | null {
    // if (this.remainingBingoCount <= 0) return null;

    this.remainingBingoCount -= 1;
    if (this.remainingBingoCount <= 0) {
      this.remainingBingoCount = 0;
    }
    this.bingoHappenedCount += 1;
    return {
      rank: this.bingoHappenedCount,
    };
  }
}

import _ from 'lodash';
import {
  <PERSON>Pool,
  Card,
  CardCell,
  CardEffect,
  CardSeedGroupRecord,
  CardSeedRecord,
  EffectTuple,
  NaturalBingoFlag, PlayerRoundProgress, RandomSequenceState
} from '.';
import {IRoundProgressPreset} from './PlayerRoundProgress';
import {RandomSequence} from './RandomSequence';

///// Random utils /////
export interface IWeight {
  weight: number,
}

/**
 * Pick one item randomly by weights.
 * @param group Item weight group.
 * @param randomSequence Random sequence.
 * @returns The picked item.
 */
export function randomOneItemByWeights<T extends IWeight>(group: T[], randomSequence?: RandomSequence): T {
  if (!(group.length > 0)) {
    throw new Error('Invalid operation.');
  }

  const total = _.sumBy(group, 'weight');
  const randomRatio = randomSequence ? randomSequence.nextSingle() : _.random(true);

  let stop = 0;
  for (const item of group) {
    stop += item.weight;
    const thisRatio = stop / total;
    if (randomRatio < thisRatio) return item;
  }

  // Cannot be here
  return _.last(group) as T;
}

/**
 * Pick specific count items randomly, with original items mutation.
 * @param items Items to pick.
 * @param count Pick count.
 * @param randomSequence Random sequence.
 * @returns Picked items.
 */
export function pickRandomItemsWithMutation<T>(items: T[], count: number, randomSequence?: RandomSequence): T[] {
  if (count > items.length) {
    throw new Error('Invalid operation.');
  }

  const pickedItems: T[] = [];
  _(count).range().forEach(() => {
    const pickIndex = randomSequence ? randomSequence.nextIntegerInRange(items.length - 1) : _.random(items.length - 1);
    const picked = _.pullAt(items, pickIndex)[0];
    pickedItems.push(picked);
  });

  return pickedItems;
}

/**
 * Pick specific count items randomly, without original items mutation.
 * @param items Items to pick.
 * @param count Pick count.
 * @param randomSequence Random sequence.
 * @returns Picked items.
 */
export function pickRandomItems<T>(items: Readonly<T[]>, count: number, randomSequence?: RandomSequence): T[] {
  const clonedItems = _.clone(items) as T[];
  const result = pickRandomItemsWithMutation(clonedItems, count, randomSequence);
  return result as T[];
}

/**
 * Judge multiple hit of a chance with persistent result.
 * @param chance The chance of hit.
 * @param n The number of time.
 * @param randomSequence
 * @returns The hit result.
 */
export function judgeManyTimes(chance: number, n: number, randomSequence: RandomSequence): boolean[] {
  if (n < 0) {
    throw new Error('Invalid operation.');
  }
  return _(n).range().map(() => randomSequence.nextSingle() < chance).value();
}

/**
 * Take initial n numbers of shuffled integers [min...max].
 * @param min Min value.
 * @param max Max value.
 * @param n The count to take.
 * @param randomSequence
 */
export function generateRandomOrderedIntegers(min: number, max: number, n: number, randomSequence: RandomSequence): number[] {
  if (max < min || n > (max - min + 1)) {
    throw new Error('Invalid operation.');
  }

  const numbers = _.range(min, max + 1);
  const result: number[] = [];
  for (let i = 0; i < n; i += 1) {
    const lastIndex = numbers.length - 1 - i;
    const randomIndex = randomSequence.nextIntegerInRange(lastIndex);
    result.push(numbers[randomIndex]);
    numbers[randomIndex] = numbers[lastIndex]; // Used number to the tail
  }

  return result;
}

/**
 * Swap two elements in array.
 */
export function swap<T>(array: T[], indexA: number, indexB: number): void {
  const elementA = array[indexA];
  const elementB = array[indexB];
  if (elementA === undefined || elementB === undefined) throw new Error('Index out of range.');
  array[indexB] = elementA;
  array[indexA] = elementB;
}

export enum BingoPredictionType {
  NoBingo = 0,
  NaturalBingo = 1,
  PowerupBingo = 2,
}

/**
 * 由所有卡片种子共同决定的种子
 * @param preset
 */
export function getProgressSeedDeterminedByCardSeeds(preset: IRoundProgressPreset): number {
  let progressSeed = preset.cardPresets[0].seed;
  for (let i = 1; i < preset.cardPresets.length; i += 1) {
    progressSeed = Math.abs(progressSeed - preset.cardPresets[i].seed);
  }
  return progressSeed;
}

export function hasEffect<T>(effects: EffectTuple<T>[], type: T, value?: number): boolean {
  if (value !== undefined) {
    return _.find(effects, ([t, v]) => t === type && v === value) !== undefined;
  }
  return _.find(effects, ([t]) => t === type) !== undefined;
}

export function findEffect<T>(effects: EffectTuple<T>[], effectType: T): EffectTuple<T> | undefined {
  return _.find(effects, ([t]) => t === effectType);
}

export function findEffectValue<T>(effects: EffectTuple<T>[], effectType: T): number | undefined {
  const effect = findEffect(effects, effectType);
  if (effect) return effect[1];
  return undefined;
}

export function updateEffectValue<T>(effects: EffectTuple<T>[], effectType: T, value: number, customData?: any): boolean {
  const e = findEffect(effects, effectType);
  if (!e) return false;

  if (customData != null) {
    e[2] = customData;
  }
  e[1] = value;
  return true;
}

/**
 * Find a connected cell region.
 * @param origin Origin cell is the start point to find region.
 * @param card
 * @param samePredicate Predicate if a cell belongs to the region.
 * @returns The region.
 */
export function findSameCellRegion(origin: CardCell, card: Card, samePredicate: (cell: CardCell, origin: CardCell) => boolean): CardCell[] {
  const region: CardCell[] = [origin];
  const checkedDict: Record<number, boolean> = {[origin.index]: true};
  const searchQueue: CardCell[] = [origin];
  let index = 0;

  while (index < searchQueue.length) {
    const center = searchQueue[index];
    const uncheckedAdjacentCells = _.filter(card.getEdgeAdjacentCells(center), c => !checkedDict[c.index]);
    _.forEach(uncheckedAdjacentCells, ac => {
      checkedDict[ac.index] = true;
      if (!samePredicate(ac, origin)) return;

      region.push(ac);
      searchQueue.push(ac);
    });
    index += 1;
  }

  return region;
}

///// Smart Value /////
type ValueWeightGroup = { weight: number, value: number }[];
type CustomExpression = { expression: string, params: string[] };
export type SmartValue = number | ValueWeightGroup | CustomExpression | string

export function resolveSmartValue(
    smartValue: SmartValue,
    params: Record<string, any> | ((name: string) => any) = {},
    rs?: RandomSequence,
): number {
  if (_.isNumber(smartValue)) return smartValue;

  if (_.isArray(smartValue)) {
    if (!(smartValue.length > 0 && smartValue[0].weight > 0)) throw new Error('Invalid smart value.');

    const weight = randomOneItemByWeights(smartValue, rs);
    return weight.value;
  }

  if (_.isString(smartValue)) {
    const paramList = _([...smartValue.matchAll(/\${(\w+)}/g)]).map(1).uniq().value();
    let expression = smartValue;

    _.forEach(paramList, paramName => {
      expression = _.replace(expression, new RegExp(`\\$\{${paramName}}`, 'g'), paramName);
    });

    return resolveSmartValue({expression: expression, params: paramList}, params, rs);
  }

  if (_.isString(smartValue.expression)) {
    let replacedExpression = smartValue.expression;
    const getParam = (name: string) => _.isFunction(params) ? params(name) : params[name];
    _.forEach(smartValue.params, paramName => {
      replacedExpression = _.replace(replacedExpression, new RegExp(paramName, 'g'), getParam(paramName));
    });

    try {
      const result = eval(replacedExpression);
      return resolveSmartValue(result, params, rs);
    } catch (err) {
      throw Error('Bad value expression.');
    }
  }

  throw new Error('Invalid smart value.');
}

///// Card Picking /////

export type CardPickingOption = {
  isNaturalBingos: boolean[];
  maxCallCount: number;
  phaseDefinition: [early: number, mid: number, late: number];
  pickChance: [early: number, late: number][];
  limits: [early: number, late: number][];
  retryCount: number;
  daubOnSameCallTolerance: [when3cards3daub: number, when4cards3daub: number, when4cards4daub: number];
};

export function groupPickCards(pool: CallPool, option: CardPickingOption,
                               log: (title: string, message: string) => void = _.noop, outResult: { flags?: string[], pickTimes?: number } | null = null): number[] {
  const cardCount = option.isNaturalBingos.length;
  const bingoCount = _.filter(option.isNaturalBingos, v => !!v).length;
  const cardCountOption = cardCount - 1;
  const earlyBingoAt = _.toSafeInteger(option.maxCallCount * option.phaseDefinition[0]);
  const midBingoAt = _.toSafeInteger(option.maxCallCount * option.phaseDefinition[1]);
  const lateBingoAt = _.toSafeInteger(option.maxCallCount * option.phaseDefinition[2]);

  const getFlag = (CardSeedGroupRecord: CardSeedGroupRecord) => {
    const key = [];
    for (const bingoAt of CardSeedGroupRecord.bingoAt) {
      let s = 'none';
      if (bingoAt < option.maxCallCount) {
        if (bingoAt <= earlyBingoAt) s = 'early';
        else if (bingoAt <= midBingoAt) s = 'mid';
        else if (bingoAt <= lateBingoAt) s = 'late';
      }
      key.push(s);
    }
    return key;
  };

  const cards: { [key: string]: number[] } = {};
  const poolGroup = pool.cardsGroups![cardCountOption];
  _.forEach(poolGroup, (c, i) => {
    const key = getFlag(c);
    const cardsKey = _.sortBy(key).join('|');
    if (!cards[cardsKey]) {
      cards[cardsKey] = [];
    }
    cards[cardsKey].push(i);
  });

  const [earlyChance, lateChance] = option.pickChance[cardCountOption];
  let [earlyRemain, lateRemain] = option.limits[cardCountOption];
  let retryRemain = _.clamp(_.toSafeInteger(option.retryCount), 1, 16) as number;
  const best: [group: CardSeedGroupRecord | null, evaluation: [onceDaub3: number, onceDaub4: number]] = [null, [999, 999]];
  while (retryRemain > 0) {
    const poolGroupCopy = JSON.parse(JSON.stringify(poolGroup)) as CardSeedGroupRecord[];
    const cardsCopy = JSON.parse(JSON.stringify(cards));
    retryRemain -= 1;

    const pickedState: string[] = _.map(option.isNaturalBingos, isNaturalBingo => {
      if (!isNaturalBingo) {
        return 'none';
      }
      const chance = _.random(true);
      if (chance <= earlyChance && earlyRemain > 0) {
        earlyRemain -= 1;
        return 'early';
      } else if (chance <= lateChance && lateRemain > 0) {
        lateRemain -= 1;
        return 'late';
      }
      return 'mid';
    });
    const safePickOne = (pickedState: string[]): CardSeedGroupRecord => {
      let picked = null;
      const candidates = cardsCopy[_.sortBy(pickedState, d => d).join('|')];
      if (!candidates || !(candidates.length > 0)) {
        for (let k = bingoCount; k >= 0; k--) {
          const pools = poolGroupCopy.filter(v => v.bingoCount === k);
          if (pools.length > 0) {
            picked = pickRandomItemsWithMutation(pools, 1)[0];
            break;
          }
        }
      } else {
        const idx = pickRandomItemsWithMutation<number>(candidates, 1)[0];
        picked = poolGroupCopy[idx];
      }
      if (!picked) throw new Error('Invalid pick.');
      return picked;
    };

    const picked = safePickOne(pickedState);

    if (picked.seed.length < 3) {
      best[0] = picked;
      break;
    }

    if (picked.seed.length === 3) {
      const onceDaub3TimesIndexes = _.intersection(...picked.caught);
      const onceDaub3Count = onceDaub3TimesIndexes.length;
      if (onceDaub3Count <= option.daubOnSameCallTolerance[0]) {
        best[0] = picked;
        break;
      }

      if (onceDaub3Count < best[1][0]) {
        best[1][0] = onceDaub3Count;
        best[0] = picked;
      }
      break;
    }

    // 4 cards
    const allCaught = _.concat([], ...picked.caught);
    const onceDaubCounts = _(allCaught).countBy(([callIndex]) => callIndex).values().value();
    const once3TimesCount = _.filter(onceDaubCounts, c => c === 3).length;
    const once4TimesCount = _.filter(onceDaubCounts, c => c === 4).length;

    if (once3TimesCount <= option.daubOnSameCallTolerance[1] && once4TimesCount <= option.daubOnSameCallTolerance[2]) {
      best[0] = picked;
      break;
    }

    if (once3TimesCount + once4TimesCount < best[1][0] + best[1][1]) {
      best[0] = picked;
      best[1][0] = once3TimesCount;
      best[1][1] = once4TimesCount;
    }
  }

  log('GROUP PICKING', `[${best[0]?.seed.join(', ')}] , Picked ${option.retryCount - retryRemain} times`);
  if (outResult) {
    outResult.flags = best[0] ? getFlag(best[0]) : [];
    outResult.pickTimes = option.retryCount - retryRemain;
  }
  if (best[0] === null) throw new Error('Invalid pick.');
  return best[0].seed;
}

export function pickCards(pool: CallPool, option: CardPickingOption,
                          log: (title: string, message: string) => void = _.noop, outResult: { flags?: string[], pickTimes?: number } | null = null): number[] {
  if (!pool.cards) {
    return groupPickCards(pool, option, log, outResult);
  }
  const cardCount = option.isNaturalBingos.length;
  const cardCountOption = cardCount - 1;
  const earlyBingoAt = _.toSafeInteger(option.maxCallCount * option.phaseDefinition[0]);
  const midBingoAt = _.toSafeInteger(option.maxCallCount * option.phaseDefinition[1]);
  const lateBingoAt = _.toSafeInteger(option.maxCallCount * option.phaseDefinition[2]);

  const earlyCards: CardSeedRecord[] = [];
  const midCards: CardSeedRecord[] = [];
  const lateCards: CardSeedRecord[] = [];
  const nonNaturalBingoCards: CardSeedRecord[] = [];

  _.forEach(pool.cards, c => {
    if (c.bingoAt < option.maxCallCount) {

      if (c.bingoAt <= earlyBingoAt) earlyCards.push(c);
      else if (c.bingoAt <= midBingoAt) midCards.push(c);
      else if (c.bingoAt <= lateBingoAt) lateCards.push(c);

    } else {
      nonNaturalBingoCards.push(c);
    }
  });

  const safePickOne = (candidateGroups: CardSeedRecord[][]): CardSeedRecord => {
    let picked: CardSeedRecord | null = null;
    for (let i = 0; i < candidateGroups.length; i += 1) {
      const candidates = candidateGroups[i];
      if (!(candidates.length > 0)) continue;

      picked = pickRandomItemsWithMutation(candidates, 1)[0];
      break;
    }
    if (picked === null) throw new Error('Invalid candidates.');
    return picked;
  };

  const [earlyChance, lateChance] = option.pickChance[cardCountOption];
  let [earlyRemain, lateRemain] = option.limits[cardCountOption];
  let retryRemain = _.clamp(_.toSafeInteger(option.retryCount), 1, 16) as number;

  type pickResult = [card: CardSeedRecord, phase: 'early' | 'mid' | 'late' | 'none']
  const best: [pickedCards: pickResult[] | null, evaluation: [onceDaub3: number, onceDaub4: number]] = [null, [999, 999]];

  while (retryRemain > 0) {
    retryRemain -= 1;

    const earlyCardsCloned = _.clone(earlyCards);
    const midCardsCloned = _.clone(midCards);
    const lateCardsCloned = _.clone(lateCards);
    const nonNaturalBingoCardsCloned = _.clone(nonNaturalBingoCards);

    const picked: pickResult[] = _.map(option.isNaturalBingos, isNaturalBingo => {
      if (!isNaturalBingo) {
        return [safePickOne([nonNaturalBingoCardsCloned, lateCardsCloned, midCardsCloned, earlyCardsCloned]), 'none'];
      }

      const chance = _.random(true);
      if (chance <= earlyChance && earlyRemain > 0) {
        earlyRemain -= 1;
        return [safePickOne([earlyCardsCloned, midCardsCloned, lateCardsCloned, nonNaturalBingoCardsCloned]), 'early'];
      } else if (chance <= lateChance && lateRemain > 0) {
        lateRemain -= 1;
        return [safePickOne([lateCardsCloned, midCardsCloned, earlyCardsCloned, nonNaturalBingoCardsCloned]), 'late'];
      }

      return [safePickOne([midCardsCloned, lateCardsCloned, earlyCardsCloned, nonNaturalBingoCardsCloned]), 'mid'];
    });

    if (picked.length < 3) {
      best[0] = picked;
      break;
    }

    if (picked.length === 3) {
      const onceDaub3TimesIndexes = _.intersection(..._.map(picked, ([c]) => c.caught));
      const onceDaub3Count = onceDaub3TimesIndexes.length;
      if (onceDaub3Count <= option.daubOnSameCallTolerance[0]) {
        best[0] = picked;
        break;
      }

      if (onceDaub3Count < best[1][0]) {
        best[1][0] = onceDaub3Count;
        best[0] = picked;
      }
      break;
    }

    // 4 cards
    const allCaught = _.concat([], ..._.map(picked, ([c]) => c.caught));
    const onceDaubCounts = _(allCaught).countBy(([callIndex]) => callIndex).values().value();
    const once3TimesCount = _.filter(onceDaubCounts, c => c === 3).length;
    const once4TimesCount = _.filter(onceDaubCounts, c => c === 4).length;

    if (once3TimesCount <= option.daubOnSameCallTolerance[1] && once4TimesCount <= option.daubOnSameCallTolerance[2]) {
      best[0] = picked;
      break;
    }

    if (once3TimesCount + once4TimesCount < best[1][0] + best[1][1]) {
      best[0] = picked;
      best[1][0] = once3TimesCount;
      best[1][1] = once4TimesCount;
    }
  }

  log('PICKING', `[${_.map(best[0], r => r[1]).join(', ')}] , Picked ${option.retryCount - retryRemain} times`);
  if (outResult) {
    outResult.flags = _.map(best[0], r => r[1]);
    outResult.pickTimes = option.retryCount - retryRemain;
  }

  return _.map(best[0], ([c]) => c.seed);
}

export function shuffleArr<T>(array: T[], randomSequence?: RandomSequence): T[] {
  let i = array.length;
  while (i) {
    const j = randomSequence ? randomSequence.nextIntegerInRange(--i) : _.random(--i);
    [array[j], array[i]] = [array[i], array[j]];
  }
  return array;
}

export function randomInt(min: number, max: number, obj: Card | PlayerRoundProgress): number {
  const rs = new RandomSequence(obj.findSeed());
  const num = rs.nextIntegerInRange(max, min);
  obj.setSeed(rs);
  return num;
}

export function randomFloat(min: number, max: number, obj: Card | PlayerRoundProgress): number {
  const rs = new RandomSequence(obj.findSeed());
  const num = rs.nextFloatInRange(max, min);
  obj.setSeed(rs);
  return num;
}

export function shuffleEffectArr<T>(array: T[], obj: Card | PlayerRoundProgress): T[] {
  let i = array.length;
  while (i) {
    const j = randomInt(0, --i, obj);
    [array[j], array[i]] = [array[i], array[j]];
  }
  return array;
}

export function newPickRandomItems<T>(items: T[], count: number, obj: Card | PlayerRoundProgress): T[] {
  const rs = new RandomSequence(obj.findSeed());
  const result = pickRandomItemsWithMutation(items, count, rs);
  obj.setSeed(rs);
  return result;
}

export function weightRandom(weightArr: number[], obj: Card | PlayerRoundProgress, multi = 1): number {
  const weightRange = [weightArr[0] * multi];
  const weightTotal = weightArr.reduce((prev, cur, idx) => {
    weightRange[idx] = weightRange[idx - 1] + Number(cur) * multi;
    return weightRange[idx];
  });

  const r = randomFloat(0, 1, obj);
  const rand = r * weightTotal;
  let val = -1;
  if (weightTotal > 0) {
    for (let i = 0; i < weightArr.length; i++) {
      if (rand <= weightRange[i]) {
        val = i;
        break;
      }
    }
  }
  return val;
}
export function getRandomIndexes(arr: any[], num: number): number[] {
  const shuffled = arr.map((item, index) => [index, Math.random()])
      .sort((a, b) => a[1] - b[1])
      .map(item => item[0]);
  const selected = new Set<number>();
  for (let i = 0; i < num && i < shuffled.length; i++) {
    selected.add(shuffled[i]);
  }
  return Array.from(selected);
}
export function getRandomArray(arr: any[], num: number,obj: Card | PlayerRoundProgress): number[] {
  const newArr = JSON.parse(JSON.stringify(arr));
  newArr.sort(()=>{
    return 0.5 -randomFloat(0,1, obj);
  });
  const sliceArr = newArr.slice(0, num);
  return sliceArr;
}

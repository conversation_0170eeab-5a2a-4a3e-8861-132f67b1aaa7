import {PlayerRoundProgress} from "../PlayerRoundProgress";

export class BonusGame {
  public userGameInfo: any;
  public config: any;
  public ticketCost: number;
  public playCount = 0;
  public progress: PlayerRoundProgress;
  public userLevel: number;
  public extraParam: any;

  constructor(progress: PlayerRoundProgress, ticketCost: number, archive: any, config = null, userLevel = 0,extraParam: any) {
    this.userLevel = userLevel;
    this.handleUserData(archive);
    this.config = config;
    this.ticketCost = ticketCost;
    this.progress = progress;
    this.extraParam = extraParam;
    this.checkPlayCount();
    if (progress.forceBonusGame && !this.playCount) {
      this.playCount = 1;
    }
  }

  getSeed(): number {
    const cards = this.progress.cards;
    let seed = 0;
    for (const card of cards) {
      seed += card.seed;
    }
    return seed;
  }

  getBaseCost(): number {
    return Math.ceil(this.ticketCost / this.progress.cards.length);
  }

  checkPlayCount(): void {
    this.playCount = 0;
  }

  handleUserData(archive: any): void {
    if (archive) {
      this.userGameInfo = archive;
    } else {
      this.initData();
    }
  }

  initData(): void {
    this.userGameInfo = {};
  }

  generateResult(): boolean {
    return false;
  }

  generateSimulationResult(checkConfig?: any[]): string {
    this.generateResult();
    return this.userGameInfo.rewards.reduce((p: number, c: number) => Number(p) + Number(c), 0);
  }
}
import {BonusGame} from "./BonusGame";
import {pickRandomItemsWithMutation, weightRandom} from "../Utils";
import {initializeProgressSeed} from "../rules/common";

interface HoneyHarvestConfig {
    centerWeight: number[],
    centerCoef: number[],
    externalWeight: number[],
    externalCoef: number[],
    externalRoll: number[],
}

export class HoneyHarvest extends BonusGame {

    static diceNum = 8;//外围骰子数

    initData(): void {
        this.userGameInfo = HoneyHarvest.initUserData();
    }

    static initUserData(): any {
        return {
            rollCount: 0,//小游戏道具道具个数
            result: [], //获得进入小游戏流程
            cost: 0, //进入游戏单张卡的消耗
            centerDiceIndex: -1,//中心骰子的index -1表示没有roll
            diceWinNum: 0,//外围有多少个骰子中奖
            isCollect: true,
        };
    }

    initResult(userGameInfo: any) {
        const collectIndex = new Array(HoneyHarvest.diceNum).fill(0);
        initializeProgressSeed(this.progress);
        for (let i = 0; i < this.userGameInfo.rollCount; i++) {
            const rollResult = [];
            for (let j = 0; j < collectIndex.length; j++) {
                if (!collectIndex[j]) {
                    const rollIndex = weightRandom(this.config.externalWeight, this.progress);
                    const isWin = this.config.externalRoll[rollIndex];
                    collectIndex[j] = isWin;
                    if (isWin) {//如果中
                        rollResult.push(j);
                        this.userGameInfo.diceWinNum++;
                    }
                }
            }
            userGameInfo.result.push(rollResult);
            if (this.userGameInfo.diceWinNum === HoneyHarvest.diceNum) {
                break;
            }
        }
        if (this.userGameInfo.diceWinNum > 0) {
            userGameInfo.centerDiceIndex = weightRandom(this.config.centerWeight, this.progress);
        }
    }

    generateResult(): boolean {
        if (this.playCount > 0) {
            const userGameInfo = this.userGameInfo;
            if (userGameInfo.result.length <= 0) {
                this.initResult(userGameInfo);
            }
            return true;
        }
        return false;
    }

    generateSimulationResult(checkConfig?: any[]): string {
        this.generateResult();
        const config = this.findThisConfig();
        if (config == null) {
            throw new Error('no config');
        }
        const baseCost = this.getBaseCost();
        let value = 0, time = 0, centerTime = 0;
        if (this.userGameInfo.diceWinNum > 0) {
            time += config.externalCoef[this.userGameInfo.diceWinNum - 1];
            value += (config.externalCoef[this.userGameInfo.diceWinNum - 1]) * baseCost;
            if (this.userGameInfo.centerDiceIndex != -1) {
                time *= this.config.centerCoef[this.userGameInfo.centerDiceIndex];
                value *= this.config.centerCoef[this.userGameInfo.centerDiceIndex];
                centerTime = this.config.centerCoef[this.userGameInfo.centerDiceIndex];
            }
        }
        this.initData();
        return "equivalents:" + value + ' time:' + time + ' centerTime:' + centerTime + " playCount:" + this.playCount;
    }

    findThisConfig(isThrow = true): HoneyHarvestConfig {
        if (isThrow && this.config == null) {
            throw new Error('no config');
        }
        return this.config;
    }

    static calProgressAdd(): number {
        return 1;
    }

    checkPlayCount(): void {
        const cards = this.progress.cards;
        for (const card of cards) {
            if (card.isBingoed) {
                this.userGameInfo.rollCount += HoneyHarvest.calProgressAdd();
            }
        }
        this.playCount = this.userGameInfo.rollCount;
    }
}

import {BonusGame} from "./BonusGame";
import {RandomSequence} from "../RandomSequence";
import {logger} from "../CoreUtils2";
import {getRandomIndexes, pickRandomItemsWithMutation} from "../Utils";
import {CellEffect} from "../effects";
import _ from "lodash";

interface Prize {
    grid: number,
    equivalents: number,//单个价值
    accEuateDiamonds: number,
    rewards: any,
    fox: number,
}

interface IceMeltingConfig {
    Lv: number,
    needForIceCrystals: number,
    maxRechargeIceCrystals: number,
    prizes: Prize[],
}

export class IceMelting extends BonusGame {

    static totalFloor = 16;

    initData(): void {
        this.userGameInfo = IceMelting.initUserData(this.userLevel);
    }

    static initUserData(initUserLevel: number): any {
        return {
            snowFlakeCount: 0,//小游戏道具道具个数
            needCount: 0, //总共需要小游戏道具道具个数
            curProgress: 0,
            curMap: null, //生成小游戏地图
            result: [], //获得进入小游戏流程
            curIdx: 0, //玩家展示 实际表现的位置
            collectIndex: [],
            isThief: false,
            choose: -1, //玩家选择，为了恢复狐狸位子
            freePassThief: 1, //是否使用过免费机会
            initUserLevel,//记录初始时候的等级，为了保证小游戏的配置是一样的
        };
    }

    initResult(userGameInfo: any) {
        const rs = new RandomSequence(this.getSeed() + "");
        let curLevel = 0;
        while (curLevel < IceMelting.totalFloor) {
            const map = userGameInfo.curMap[curLevel];
            const idx = rs.nextIntegerInRange(map.length - 1);
            const key = curLevel + '_' + idx;
            if (map[idx]) {
                userGameInfo.result.push(key);
                curLevel++;
            }
        }
    }

    generateResult(): boolean {
        if (this.playCount > 0) {
            //this.userGameInfo.curProgress = 0;
            this.userGameInfo.snowFlakeCount = 0;
            const userGameInfo = this.userGameInfo;
            if (userGameInfo.curMap == null) {
                const config = this.findThisConfig(false);
                if (config == null) {
                    logger.error('Bonus Game No Config!');
                    return false;
                }
                userGameInfo.curMap = IceMelting.initMap(config);
            }
            if (userGameInfo.result.length <= 0) {
                this.initResult(userGameInfo);
            }
            return true;
        }
        return false;
    }

    static initMap(config: IceMeltingConfig): any[][] {
        const map = [];
        for (let i = 0; i < IceMelting.totalFloor; i++) {
            const prize = config.prizes.find(v => i === v.grid - 1);
            if (!prize) {
                throw new Error('no config');
            }
            const rewardsNum = prize.rewards.length;
            const initialUpNum = prize.fox;
            const arr = new Array(initialUpNum + rewardsNum).fill(["fox"]);
            const rewardsArr = Object.values(prize.rewards);
            const picked = pickRandomItemsWithMutation(Object.keys(arr), rewardsNum);
            for (let j = 0; j < rewardsNum; j++) {
                arr[parseInt(picked[j])] = rewardsArr[j];
            }
            map.push(arr);
        }
        return map;
    }

    generateSimulationResult(checkConfig?: any[]): string {
        const oldResult = JSON.parse(JSON.stringify(this.userGameInfo.result));
        this.generateResult();
        const count = this.userGameInfo.result.length;
        const config = this.findThisConfig();
        if (config == null) {
            throw new Error('no config');
        }

        let value = 0, isReset = false, needDiamonds = 0;
        if (count > 0) {
            for (let i = 0; i < count; i++) {
                const key = this.userGameInfo.result[i];
                const [level, idx] = key.split('_');
                if (parseInt(level) >= IceMelting.totalFloor - 1) {
                    isReset = true;
                }
                const map = this.userGameInfo.curMap[level];
                const prize = config.prizes.find(v => i === v.grid - 1);
                if (!prize) {
                    throw new Error('no config');
                }
                if (map[parseInt(idx)] === null && prize.fox === 1) {
                    needDiamonds += prize.accEuateDiamonds;
                }
                value += prize.equivalents;

            }
        }
        if (isReset) {
            this.initData();
        }
        return "equivalents:" + value + ' needDiamonds:' + needDiamonds + " playCount:" + this.playCount;
    }

    findThisConfig(isThrow = true): IceMeltingConfig {
        if (isThrow && this.config == null) {
            throw new Error('no config');
        }
        let config = this.config.find((v: any) => _.toNumber(v.Lv) === this.userGameInfo.initUserLevel);
        if (!config && this.userGameInfo.initUserLevel > this.config[this.config.length - 1].Lv) {
            config = this.config[this.config.length - 1];
            return config;
        }
        return config;
    }

    static calProgressAdd(): number {
        return 1;
    }

    checkPlayCount(): void {
        const cards = this.progress.cards;
        if (!this.userGameInfo.needCount) {//这里为了跑数值，正常的needCount，在initMap时初始化
            const config = this.findThisConfig();
            this.userGameInfo.needCount = config.needForIceCrystals;
        }
        for (const card of cards) {
            const cells = card.cells.filter(c => c.isDaubed && c.hasEffect(CellEffect.SnowFlake));
            if (cells.length > 0) {
                this.userGameInfo.snowFlakeCount += (cells.length * IceMelting.calProgressAdd());
                this.userGameInfo.curProgress = Math.min(100, Math.floor((this.userGameInfo.snowFlakeCount / this.userGameInfo.needCount) * 100));
            }
        }
        this.playCount = this.userGameInfo.curProgress >= 100 ? 1 : 0;
    }
}

export function findConfig(configs: any, userLevel: number): IceMeltingConfig {
    let config = configs.filter((v: any) => _.toNumber(v.Lv) === userLevel);
    if (config.length <= 0 && userLevel > configs[configs.length - 1].Lv) {
        config = configs[configs.length - 1];
        return config;
    }
    return config[0];
}

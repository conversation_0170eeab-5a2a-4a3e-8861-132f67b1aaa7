import {BonusGame} from "./BonusGame";
import {CellEffect} from "../effects";
import {RandomSequence} from "../RandomSequence";
import {randomOneItemByWeights} from "../Utils";

const ENTER_GAME_LIMIT = [1, 1, 2, 2];//进小游戏条件
const GOLD_RATE = 0.05; //开出jp的几率
const JACKPOT_RATE = [700, 200, 75, 25]; //jp档位权重
const PICK_RTP = [0.02, 0.05]; //普通奖励

interface LuckyCloverBaseConfig {
  pickMax: number;
  bigRTP: number;
  jackpotRTP: number[];
  enterLimit: number[];
}

export class LuckyClover extends BonusGame {

  initData(): void {
    this.userGameInfo = {
      rewards: [],
      jackpotTicket: 0,
      collectNum: 0,
      jackpotLevel: 0
    };
  }

  generateResult(): boolean {
    if (this.playCount > 0) {
      const {jackpotRTP, pickMax, bigRTP} = LuckyClover.findBaseConfig();
      const rs = new RandomSequence(this.getSeed(), true);
      const perCost = this.getBaseCost();
      const minTicket = PICK_RTP[0] * perCost;
      const maxTicket = PICK_RTP[1] * perCost;
      let i = 0;
      while (i++ < pickMax) {
        const ticketGain = rs.nextFloatInRange(maxTicket, minTicket);
        this.userGameInfo.rewards.push(Math.ceil(ticketGain));
        const goldP = rs.nextFloatInRange(0, 1);
        if (goldP < GOLD_RATE) {
          const {level} = randomOneItemByWeights(JACKPOT_RATE.map((v, i) => {
            return {weight: v, level: i};
          }));

          let gain = Math.ceil(bigRTP * perCost);
          if (level > 0) {
            gain = Math.ceil(jackpotRTP[level - 1] * perCost);
          }
          this.userGameInfo.jackpotLevel = level;
          this.userGameInfo.jackpotTicket = gain;
          break;
        }
      }
      return true;
    }
    return false;
  }

  generateSimulationResult(checkConfig?: any[]): string {
    this.generateResult();
    const normalRewards = this.userGameInfo.rewards.reduce((p: number, c: number) => Number(p) + Number(c), 0);
    return normalRewards + ';' + this.userGameInfo.jackpotTicket + ';' + this.userGameInfo.jackpotLevel;// + ';' + this.userGameInfo.rewards.join('_');
  }

  static findBaseConfig(): LuckyCloverBaseConfig {
    return {
      pickMax: 20,
      jackpotRTP: [0.3, 0.5, 2],
      bigRTP: 0.15,
      enterLimit: ENTER_GAME_LIMIT
    };
  }

  checkPlayCount(): void {
    const cards = this.progress.cards;
    const cardsCount = cards.length;
    for (const card of cards) {
      const cell = card.cells.find(c => c.isDaubed && c.hasEffect(CellEffect.GoldLuckyClover));
      if (cell) {
        this.userGameInfo.collectNum++;
      }
    }
    const limit = ENTER_GAME_LIMIT[cardsCount - 1];
    this.playCount = this.userGameInfo.collectNum >= limit ? 1 : 0;
  }
}

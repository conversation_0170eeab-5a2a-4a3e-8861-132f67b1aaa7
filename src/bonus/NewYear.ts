import {BonusGame} from "./BonusGame";
import {RandomSequence} from "../RandomSequence";
import {logger} from "../CoreUtils2";
import {pickRandomItemsWithMutation} from "../Utils";

interface NewYearConfig {
  card: number;
  grid: number;
  para: {
    equivalent: number; //单个价值
    rewardsNum: number; //奖励抽取次数
    initialUpNum: number; //初始箭头个数
    rewards: any;
  }
}

export class NewYear extends BonusGame {

  static totalFloor = 6;

  initData(): void {
    this.userGameInfo = NewYear.initUserData();
  }

  static initUserData(): any {
    return {
      curProgress: 0,
      curMap: null, //生成的爬塔地图
      result: [], //获得进入机会后路径
      curIdx: 0, //玩家展示 实际表现的位置
    };
  }

  generateResult(): boolean {
    if (this.playCount > 0) {
      this.userGameInfo.curProgress = 0;
      const rs = new RandomSequence(this.getSeed() + "");
      const configs = this.findThisConfig(false);
      if (configs == null) {
        logger.error('Bonus Game No Config!');
        return false;
      }
      const userGameInfo = this.userGameInfo;
      if (userGameInfo.curMap == null) {
        userGameInfo.curMap = NewYear.initMap(configs, rs);
      }
      let curLevel = 0;
      while (curLevel < NewYear.totalFloor) {
        const map = userGameInfo.curMap[curLevel];
        const idx = rs.nextIntegerInRange(map.length - 1);
        const key = curLevel + '_' + idx;
        if (!map[idx] || !map[idx].length || userGameInfo.result.includes(key)) {
          userGameInfo.result.push(key);
          curLevel++;
        } else {
          userGameInfo.result.push(key);
          break;
        }
      }
      return true;
    }
    return false;
  }

  static initMap(configs: NewYearConfig[], rs: RandomSequence): any[][] {
    const map = [];
    for (let i = 0; i < NewYear.totalFloor; i++) {
      const config = configs.find(v => i === NewYear.totalFloor - v.grid);
      if (!config) {
        throw new Error('no config');
      }
      const {initialUpNum, rewardsNum, rewards} = config['para'];
      const arr = new Array(initialUpNum + rewardsNum).fill([]);
      const rewardsArr = Object.values(rewards);
      const picked = pickRandomItemsWithMutation(Object.keys(arr), rewardsNum);
      for (let j = 0; j < rewardsNum; j++) {
        const rewardIdx = rs.nextIntegerInRange(rewardsArr.length - 1);
        arr[parseInt(picked[j])] = rewardsArr[rewardIdx];
      }
      map.push(arr);
    }
    return map;
  }

  generateSimulationResult(checkConfig?: any[]): string {
    const oldResult = JSON.parse(JSON.stringify(this.userGameInfo.result));
    this.generateResult();
    const count = this.userGameInfo.result.length;
    const configs = this.findThisConfig();
    if (configs == null) {
      throw new Error('no config');
    }

    let value = 0, isReset = false;
    for (let i = this.userGameInfo.curIdx + 1; i < count; i++) {
      const key = this.userGameInfo.result[i];
      const [level, idx] = key.split('_');
      if (parseInt(level) >= NewYear.totalFloor - 1) {
        isReset = true;
      }
      const map = this.userGameInfo.curMap[level];
      if(!map[parseInt(idx)] || !map[parseInt(idx)].length || oldResult.includes(key)) {
        continue;
      }
      const config = configs.find(v => 6 - v.grid === Number(level));
      if (!config) {
        throw new Error('no config');
      }
      value += config.para.equivalent;
    }
    this.userGameInfo.curIdx = count - 1;
    if (isReset) {
      this.initData();
    }
    return value + '';
  }

  findThisConfig(isThrow = true): NewYearConfig[] {
    if (isThrow && this.config == null) {
      throw new Error('no config');
    }
    return this.config.filter((v: any) => v.card === 4);
  }

  static calProgressAdd(ticketCost: number): number {
    return Math.min((Math.pow(1.0003, ticketCost) / 1000) * 100, 4);
  }

  checkPlayCount(): void {
    let daubCount = 0;
    for (const card of this.progress.cards) {
      daubCount += card.cells.filter(v => v.isDaubed).length;
    }
    const add = NewYear.calProgressAdd(this.ticketCost) * daubCount;
    this.userGameInfo.curProgress = Math.min(100, this.userGameInfo.curProgress + add);
    this.playCount = this.userGameInfo.curProgress >= 100 ? 1 : 0;
  }
}

import {BonusGame} from "./BonusGame";
import {weightRandom} from "../Utils";
import _ from "lodash";
import {CellEffect} from "../effects";
import {initializeProgressSeed} from "../rules/common";

interface TreasurePotConfig {
    "level": number,
    "coinChargeEnergy": number,
    "needEnergy": number
}

const jackPotConfig = [0.1, 0.2, 0.4, 1];//jp的单卡押注
const jackPotWightConfig = [40, 30, 20, 10];//各种jp随机到的概率
export class TreasurePot extends BonusGame {

    initData(): void {
        const bigBet = this.extraParam && this.extraParam['ticketTotal'] ? this.extraParam['ticketTotal'] / 4 : 0;
        this.userGameInfo = TreasurePot.initUserData(this.userLevel, bigBet);
    }

    static initUserData(level: number, bigCost: number): any {
        return {
            level,//初始化数据时候的玩家等级
            levelBigCost: bigCost ? bigCost : 0,//进入游戏的当前等级的卡片的最高押注（4张卡片的最高押注）
            curProgress: 0,
            result: [], //获得进入小游戏流程
            curMap: null,//存jp
            isCollect: true,
        };
    }

    static calProgressAdd(ticketCost: number, ticketTotal: number, potConfig: TreasurePotConfig): number {
        const count = Math.ceil(ticketCost / ticketTotal * potConfig.coinChargeEnergy / potConfig.needEnergy * 100);
        return count;
    }

    static findBaseConfig() {
        return {
            jackPotConfig,
            jackPotWightConfig,
        };
    }

    initResult(userGameInfo: any) {
        const chooseOb = [];
        initializeProgressSeed(this.progress);
        for (let i = 0; i < 12; i++) {//最多12次随机，看那种jackPot先出3个一种的
            const rollIndex = weightRandom(jackPotWightConfig, this.progress);
            userGameInfo.result.push(rollIndex);
            if (!chooseOb[rollIndex]) {
                chooseOb[rollIndex] = 0;
            }
            chooseOb[rollIndex] += 1;
            if (chooseOb[rollIndex] >= 3) {
                break;
            }
        }
    }

    generateResult(): boolean {
        if (this.playCount > 0) {
            const userGameInfo = this.userGameInfo;
            if (userGameInfo.result.length <= 0) {
                this.initResult(userGameInfo);
            }
            return true;
        }
        return false;
    }

    generateSimulationResult(checkConfig?: any[]): string {
        this.generateResult();
        let value = 0, jpIndex = 0, isReset = false;
        const chooseOb = [];
        for (let i = 0; i < this.userGameInfo.result.length; i++) {
            if (!chooseOb[this.userGameInfo.result[i]]) {
                chooseOb[this.userGameInfo.result[i]] = 0;
            }
            chooseOb[this.userGameInfo.result[i]] += 1;
            if (chooseOb[this.userGameInfo.result[i]] >= 3) {
                jpIndex = this.userGameInfo.result[i];
                isReset = true;
                break;
            }
        }

        if (isReset) {
            value = (TreasurePot.getJackPotList(this.userGameInfo))[jpIndex];
            this.initData();
        }
        return "equivalents:" + value + ' jpIndex:' + jpIndex + " playCount:" + this.playCount;
    }

    static findThisConfig(isThrow = true, potConfig: TreasurePotConfig[], bonusInfo: any): TreasurePotConfig {
        if (isThrow && potConfig == null) {
            throw new Error('no config');
        }
        let config = potConfig.find((v: any) => _.toNumber(v.level) === bonusInfo.level);
        if (!config && bonusInfo.level > potConfig[potConfig.length - 1].level) {
            config = potConfig[potConfig.length - 1];
            return config;
        }
        return <TreasurePotConfig>config;
    }

    checkPlayCount(): void {
        const cards = this.progress.cards;
        let coinsCount = 0;
        for (const card of cards) {
            const cells = card.cells.filter(c => c.isDaubed && c.hasEffect(CellEffect.CoinTool));
            if (cells.length > 0) {
                coinsCount += cells.length;
            }
        }
        const potConfig = TreasurePot.findThisConfig(true, this.config, this.userGameInfo);
        const add = TreasurePot.calProgressAdd(this.ticketCost, this.extraParam['ticketTotal'], potConfig) * coinsCount;
        this.userGameInfo.curProgress = Math.min(100, this.userGameInfo.curProgress + add);
        this.playCount = this.userGameInfo.curProgress >= 100 ? 1 : 0;
    }

    static getJackPotList(userBonus: any): number [] {
        const rewards = [];
        for (let i = 0; i < jackPotConfig.length; i++) {
            rewards.push(jackPotConfig[i] * userBonus.levelBigCost);
        }
        return rewards;
    }
}

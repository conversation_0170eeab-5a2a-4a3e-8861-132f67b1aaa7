import {BonusGame} from "./BonusGame";
import {CellEffect} from "../effects";
import {RandomSequence} from "../RandomSequence";
import {logger} from "../CoreUtils2";
import {pickRandomItemsWithMutation, randomOneItemByWeights} from "../Utils";
import _ from 'lodash';

const GemJackpotReward = [1, 2, 5, 10, 25, 50, 100]; //宝石jackpot 奖励倍数
const MinJackpotCount = 10; //中jackpot 最小个数
const SuperGemBuff = 0.1; //超级宝石加成

interface TreasureRaiderConfig {
  diamondRate: number;
  upDiamondVariationRate: number;
  pool: PoolReward[]
}

interface BaseTreasureRaiderConfig {
  jackpotReward: number[];
  minJackpotCount: number;
  superBuff: number;
}

interface PoolReward {
  itemType: number;
  itemId: number;
  value: number;
  weight: number;
}

export class TreasureRaider extends BonusGame {

  initData(): void {
    this.userGameInfo = {
      baseGem: [],
      rewards: [],
      superCount: 0,
      jackpotCount: 0
    };
  }

  handleUserData(archive: any): void {
    this.initData();
  }

  generateResult(): boolean {
    if (this.playCount > 0) {
      const rs = new RandomSequence(this.getSeed() + "");
      const config = this.findThisConfig();
      if (config == null) {
        logger.error('Bonus Game No Config!');
        return false;
      }
      const baseGemCount = this.userGameInfo.jackpotCount;
      const rewards = this.userGameInfo.rewards;
      this.userGameInfo.baseGem = pickRandomItemsWithMutation(_.range(0, 15), baseGemCount);
      //生成每个格子的奖励
      for (let i = 0; i < 16 - baseGemCount; i++) {
        const openGemRate = rs.nextFloatInRange(0, 1);
        //点开钻石
        if (openGemRate < config.diamondRate) {
          let gem = 1;
          this.userGameInfo.jackpotCount++;
          const superRate = rs.nextFloatInRange(0, 1);
          if (superRate < config.upDiamondVariationRate) {
            this.userGameInfo.superCount++;
            gem = 2;
          }
          rewards.push(gem);
        } else {
          const reward = randomOneItemByWeights(config.pool, rs);
          rewards.push(reward);
        }
      }
      return true;
    }
    return false;
  }

  generateSimulationResult(checkConfig?: any[]): string {
    this.generateResult();
    const {jackpotCount, superCount, rewards, baseGem} = this.userGameInfo;
    const buff = superCount * SuperGemBuff + 1;
    let ticketPoint = jackpotCount - MinJackpotCount >= 0 ? Math.floor(GemJackpotReward[jackpotCount - MinJackpotCount] * (this.ticketCost / this.progress.cards.length) * buff) : 0;
    if (checkConfig) {
      const checkMap = new Map();
      for (const check of checkConfig) {
        checkMap.set(this.formatCheck(check), check.points);
      }

      for (const reward of rewards) {
        if (typeof reward === "object") {
          const key = this.formatCheck(reward);
          const point = checkMap.get(key);
          ticketPoint += point;
        }
      }
    }
    return [baseGem.length, jackpotCount, superCount, ticketPoint].join(';');
  }

  formatCheck(reward: { itemType: number, itemId: number, value: number }): string {
    return [reward.itemType, reward.itemId, reward.value].join('-');
  }

  findThisConfig(): TreasureRaiderConfig | null {
    const cardsCount = this.progress.cards.length;
    for (const config of this.config) {
      if (cardsCount === config['cardsNum']) {
        const curConfig: TreasureRaiderConfig = {
          diamondRate: config['diamondRate'],
          upDiamondVariationRate: config['upDiamondVariationRate'],
          pool: []
        };

        const rewardPool = config['rewardPool'] as { bets: number; pool: PoolReward[] }[];
        rewardPool.sort((a, b) => b['bets'] - a['bets']);
        const rewards = rewardPool.find(v => v['bets'] <= this.ticketCost);
        if (rewards) {
          curConfig['pool'] = rewards['pool'];
          return curConfig;
        }
        break;
      }
    }
    return null;
  }

  static findBaseConfig(): BaseTreasureRaiderConfig {
    return {
      minJackpotCount: MinJackpotCount,
      jackpotReward: GemJackpotReward,
      superBuff: SuperGemBuff,
    };
  }

  checkPlayCount(): void {
    const cards = this.progress.cards;
    const cardsCount = cards.length;
    this.userGameInfo.jackpotCount = 0;
    for (const card of cards) {
      const cell = card.cells.find(c => c.hasEffect(CellEffect.TreasureGem, 1));
      if (cell) {
        this.userGameInfo.jackpotCount++;
      }
    }
    const limit = cardsCount > 1 ? (cardsCount > 3 ? 3 : 2) : 1;
    this.playCount = this.userGameInfo.jackpotCount >= limit ? 1 : 0;
  }
}

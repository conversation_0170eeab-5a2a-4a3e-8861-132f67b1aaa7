import {TreasureRaider} from './TreasureRaider';
import {LuckyClover} from './LuckyClover';
import {NewYear} from './NewYear';
import {IceMelting} from './IceMelting';
import {HoneyHarvest} from "./HoneyHarvest";
import {TreasurePot} from "./TreasurePot";

function getBonusGameById(bonusId: number) {
    switch (bonusId) {
        case 6:
            return TreasureRaider;
        case 7:
            return LuckyClover;
        case 8:
            return NewYear;
        case 9:
            return IceMelting;
        case 10:
            return HoneyHarvest;
        case 11:
            return TreasurePot;
    }
    return null;
}

export {getBonusGameById, TreasureRaider, LuckyClover, NewYear, IceMelting, HoneyHarvest, TreasurePot};

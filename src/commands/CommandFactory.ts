import {ReceiveCallCommand} from './ReceiveCallCommand';
import {TryBingoCommand} from './TryBingoCommand';
import {TryDaubCommand} from './TryDaubCommand';
import {IUsePowerUpPayload, TryUsePowerupCommand} from './TryUsePowerupCommand';

export class CommandFactory {
  public static receiveCall(time: number, id: number = 0, payload?: any): ReceiveCallCommand {
    return new ReceiveCallCommand(time, id, payload);
  }

  public static tryDaub(time: number, cardIndex: number, cellIndex: number, id: number = 0): TryDaubCommand {
    return new TryDaubCommand(time, {cardIndex, cellIndex}, id);
  }

  public static tryBingo(time: number, cardIndex: number, id: number = 0): TryBingoCommand {
    return new TryBingoCommand(time, cardIndex, id);
  }

  public static tryUsePowerup(time: number, payload: IUsePowerUpPayload, id: number = 0): TryUsePowerupCommand {
    return new TryUsePowerupCommand(time, payload, id);
  }

  public static receiveCall2(roundTime: number, id: number = 0, payload?: any): ReceiveCallCommand {
    return new ReceiveCallCommand(roundTime, id, payload);
  }

  public static tryDaub2(roundTime: number, id: number = 0, payload: any): TryDaubCommand {
    return new TryDaubCommand(roundTime, payload, id);
  }

  public static tryBingo2(roundTime: number, id: number = 0, payload: any): TryBingoCommand {
    let {cardIndex} = payload || {};
    return new TryBingoCommand(roundTime, cardIndex, id);
  }

  public static tryUsePowerUp2(roundTime: number, id: number = 0, payload: any): TryUsePowerupCommand {
    return new TryUsePowerupCommand(roundTime, payload, id);
  }
}

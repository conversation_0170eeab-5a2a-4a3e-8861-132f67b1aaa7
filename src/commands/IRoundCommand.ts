import {PlayerRoundProgress} from '../PlayerRoundProgress';
import {ReceiveCallCommand} from './ReceiveCallCommand';
import {TryBingoCommand} from './TryBingoCommand';
import {IDaubPayload, TryDaubCommand} from './TryDaubCommand';
import {IUsePowerUpPayload, TryUsePowerupCommand} from './TryUsePowerupCommand';
import _ from 'lodash';
import {CommandFactory} from './CommandFactory';

export enum CommandType {
  ReceiveCall = 1,
  TryDaub = 2,
  TryBingo = 3,
  TryUsePowerup = 4,
}

export type RoundCommand = ReceiveCallCommand | TryBingoCommand | TryDaubCommand | TryUsePowerupCommand;

export interface IRoundCommandSnapshot {
  type: CommandType,
  time: number,
  payload?: unknown,
  id: number,
}

export function getHistorySnapshot(history: RoundCommand[]): IRoundCommandSnapshot[] {
  const s: IRoundCommandSnapshot[] = _.map(history, command => {
    let obj = _.pick(command, ['type', 'time', 'id']);
    if (command.payload) {
      // @ts-ignore
      obj.payload = command.payload;
    }
    return obj as IRoundCommandSnapshot;
  });
  return s;
}

export function getCommandHistoryFromSnapshot(historySnapshot: IRoundCommandSnapshot[]): RoundCommand[] {
  const h = _.map(historySnapshot, s => {
    switch (s.type) {
      case CommandType.ReceiveCall:
        return CommandFactory.receiveCall(s.time, s.id, s.payload);

      case CommandType.TryDaub:
        return CommandFactory.tryDaub(s.time, (s.payload as IDaubPayload).cardIndex, (s.payload as IDaubPayload).cellIndex, s.id);

      case CommandType.TryBingo:
        return CommandFactory.tryBingo(s.time, s.payload as number, s.id);

      case CommandType.TryUsePowerup:
        return CommandFactory.tryUsePowerup(s.time, (s.payload as IUsePowerUpPayload), s.id);

      default:
        throw new Error('Invalid command type');
    }
  });

  return h;
}

export interface IRoundCommand<TPayload, TResult> {
  execute(progress: PlayerRoundProgress): TResult;

  type: CommandType;

  /**
   * Relative milliseconds to round start time
   */
  time: number;
  id: number;
  payload: TPayload;
}

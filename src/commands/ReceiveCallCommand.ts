import { PlayerRoundProgress } from '../PlayerRoundProgress';
import { CommandType, IRoundCommand } from './IRoundCommand';

export class ReceiveCallCommand implements IRoundCommand<null, void> {
  public readonly type = CommandType.ReceiveCall;
  public payload = null;
  public time: number;
  public readonly id: number;

  constructor(time: number, id: number = 0, payload?: any) {
    this.time = time;
    this.id = id;
    this.payload = payload
  }

  public execute(progress: PlayerRoundProgress): void {
    progress.recordCommand(this);
    progress.receiveCall(this.time, this.payload);
  }
}


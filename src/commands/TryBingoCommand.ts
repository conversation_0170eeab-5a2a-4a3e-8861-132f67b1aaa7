import _ from 'lodash';
import { PlayerRoundProgress } from '../PlayerRoundProgress';
import { CommandType, IRoundCommand } from './IRoundCommand';
import { logger } from '../CoreUtils2';

export class TryBingoCommand implements IRoundCommand<number, boolean> {
  public readonly type = CommandType.TryBingo;
  public readonly time: number;
  public readonly payload: number;
  public readonly id: number;

  constructor(time: number, cardIndex: number, id: number = 0) {
    this.time = time;
    this.payload = cardIndex;
    this.id = id;
  }

  public execute(progress: PlayerRoundProgress): boolean {
    progress.recordCommand(this);
    logger.debug('coreTryBingoCommand');
    return progress.tryBingo(this.time, this.payload);
  }


}

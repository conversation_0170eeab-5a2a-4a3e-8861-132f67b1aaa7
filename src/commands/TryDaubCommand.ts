import _ from 'lodash';
import { ClueTracer } from '../ClueTracer';
import { PlayerRoundProgress } from '../PlayerRoundProgress';
import { CommandType, IRoundCommand } from './IRoundCommand';
import {logger} from '../CoreUtils2';

export interface IDaubPayload {
  cardIndex: number;
  cellIndex: number;
}

export class TryDaubCommand implements IRoundCommand<IDaubPayload, boolean> {
  public readonly type = CommandType.TryDaub;
  public readonly time: number;
  public readonly id: number;
  public readonly payload: IDaubPayload;

  constructor(time: number, payload: IDaubPayload, id: number = 0) {
    this.time = time;
    this.payload = payload;
    this.id = id;
  }

  /**
   * Return if this daub is a valid daub
   */
  public execute(progress: PlayerRoundProgress): boolean {
    progress.recordCommand(this);
    return progress.tryPlayerDaub(this.time, this.payload.cardIndex, this.payload.cellIndex);
  }

  public executeWithClues<T>(progress: PlayerRoundProgress): [result: boolean, clues: T[]] {
    const tracer = new ClueTracer(progress);
    const result = this.execute(progress);
    const clues = tracer.Retrieve<T>(this.payload.cardIndex);
    return [result, clues];
  }
}

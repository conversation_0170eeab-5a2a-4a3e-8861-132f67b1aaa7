import {ClueTracer} from '../ClueTracer';
import {PlayerRoundProgress} from '../PlayerRoundProgress';
import {CommandType, IRoundCommand} from './IRoundCommand';

export interface IUsePowerUpPayload {
  powerUpId: number;
}

export class TryUsePowerupCommand implements IRoundCommand<IUsePowerUpPayload | null, void> {
  public readonly type = CommandType.TryUsePowerup;
  public readonly payload: IUsePowerUpPayload;
  public readonly time: number;
  public readonly id: number;

  constructor(time: number, payload: IUsePowerUpPayload, id: number = 0) {
    this.time = time;
    this.id = id;
    this.payload = payload || null;
  }

  public execute(progress: PlayerRoundProgress): boolean {
    progress.recordCommand(this);
    return progress.powerupBox.usePowerupUsage(this.time, this.payload?.powerUpId || 0);
  }

  public executeWithClues<T>(progress: PlayerRoundProgress): [result: boolean, clues: [cardIndex: number, clue: T][]] {
    const tracer = new ClueTracer(progress);
    const result = this.execute(progress);
    const clues = tracer.RetrieveAllCard<T>();
    return [result, clues];
  }
}

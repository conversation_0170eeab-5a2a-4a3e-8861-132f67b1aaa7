// Global effect enums
// All effects should be listed here
// including special effects

export enum ProgressEffect {
    DiscoTarget = 1, //disco 关卡收集进度
    CurrentSeed = 2, //当前种子
}

export enum CardEffect {
    CollectionReward = 1, //阴影卡
    Counter = 2,
    CounterTarget = 3,
    FastFoodOrderHeader = 4,
    // Royal Flush
    FlushState = 5,
    //rubber
    RubberCount = 6,//橡皮糖个数
    //圣诞礼物
    ChristmasGifts = 7,
    //卡片当前种子
    CurrentSeed = 8,
    //运动员
    FootballPlayer = 9,
    MazuBalls = 10,
    //主角 通用
    Protagonist = 11,
    //松鼠
    Squirrel = 12,
    //蜂蜜
    Honey = 13,
    //聚宝盆，造成的涂抹
    PotDaubInfo = 14,
    //化妆商品
    Goods = 15,
    //孵化小鸡
    ChicksEgg = 16,
}

export enum CellEffect {
    MarkAsDaubByPowerup = 1,
    TicketReward = 2,
    BoxReward = 3,
    DoubleExp = 4,
    DoubleTicket = 5,
    ForcedBingo = 6,
    Bomb = 7,
    EasterEgg = 8,
    Rabbit = 9,
    MarkAsDaubByRabbit = 10,
    BubbleStatusB = 11,
    BubbleStatusC = 12,
    MarkAsDaubByCupid = 13,
    Bamboo = 14,
    Panda = 15,
    EatenBamboo = 16,
    MarkAsDaubByInitialization = 17,
    Tag = 18,
    TopazReward = 19,//topaz活动产出
    // Clover
    CloverCoin = 20,
    Clover = 21,
    // Fairy
    MarkAsDaubByFairy = 22,
    HoneyPot = 23,
    Fairy = 24,
    // Hanabi
    DaubOneRow = 25,
    DaubOneColumn = 26,
    Daub3x3 = 27,
    RandomDaubOne = 28,
    RandomDaubTwo = 29,
    // Fast Food
    FastFood = 30,
    // Xmas event
    XmasGingerbread = 31,
    // Penguin
    Penguin = 32,
    LuckyTime = 33,//luckyTime活动产出的字母碎片
    PiggyGold = 34,//小猪关卡金币
    PiggyFirework = 35,//小猪关卡烟花
    Fishing = 36,//钓鱼活动产出的小鱼干
    //赛车
    Driver = 37,//车手
    OilDrum = 38,//油桶
    BingoChallenge = 39,//Challenge活动产出的奶酪
    //鲨鱼关卡
    Shark = 40,
    Fish = 41,
    Dragon = 42,//巨龙产出的铁粒
    //糖果关卡
    Boy = 43,
    //收集西瓜
    Watermelon = 44,//西瓜
    GoldWatermelon = 45, //金西瓜
    Scarecrow = 46,//稻草人
    MarkAsDaubByScarecrow = 47,//被稻草人涂抹
    Cat = 48,//招财猫
    TreasureHunt = 49,//宝藏猎人
    Rubber = 50,//橡皮糖
    Pumpkin = 51,//南瓜灯
    PumpkinBoom = 53,//南瓜灯爆炸消除
    CookParty = 52,//做饭活动
    HalloweenParty = 54,//万圣节活动
    //treasure raider
    TreasureBox = 55,//宝箱
    TreasureKey = 56,//宝箱钥匙
    TreasureGem = 57,//宝石
    Garland = 58,//圣诞花环
    GarlandEffect = 59,// 圣诞花环前端占用
    //disco forever
    DiscoLengthways = 60, //纵向消除
    DiscoTransverse = 61, //横向消除
    DiscoCrossShaped = 62, //十字消除
    DiscoRandomDaub = 63, //随机涂抹
    MarkAsDaubByEffect = 64, //被关卡其他特效涂抹

    MapCollection = 65,// 圣诞地图收集活动

    //lucky clover
    SingleLuckyClover = 66,//单个🍀
    DoubleLuckyClover = 67,//两个🍀
    ThirdLuckyClover = 68,//三个🍀
    LuckyCloverHat = 69,//帽子
    GoldLuckyClover = 70,//帽子
    //情人节
    ValentineChocolate = 71,//巧克力
    ValentineRose = 72,//玫瑰
    ValentineRing = 73,//戒指
    ValentineHeart = 74,//爱心
    //足球
    FootballDrink = 75,//饮料

    ZumaBallColor = 76, // 祖玛关卡球上的颜色
    SkiItemVector = 77, // 滑雪道具的速度
    ClownCannon = 78, // 小丑关卡 大炮
    ClownDrum = 79, // 小丑关卡 鼓
    //摩天轮
    Hammer = 80,//锤子
    //新年关卡
    Lantern = 81,//灯笼

    MarkAsDaubByRocket = 82,

    JungleTemple = 83,//丛林神庙
    PineNuts = 84,//松子

    Ice = 85,//冰块
    SnowFlake = 86,//雪花
    //调酒师
    WinBottle = 87,//酒瓶
    //蜜蜂
    bee = 88,//蜜蜂
    honey = 89,//蜂蜜
    //收集星星
    CollectStarHat = 90,//帽子
    CollectStar = 91,//星星
    WitchNight = 92,//女巫之夜
    //聚宝盆
    TreasurePot = 93,//聚宝盆
    CoinTool = 94,//小游戏金币道具
    //太空大战
    SpaceBatter = 96,//太空电池
    SpaceInsect = 97,//太空虫子
    TreasurePotAddPower = 98,//充能

    TopazParadiseReward = 95,// TopazParadise
    //蜘蛛巢穴
    Spider = 99,//蜘蛛
    SpiderStaff = 100,//法杖
    //月光湖
    Moon = 102,//四个角的月亮
    moonAddPower = 103,//给那个月亮充能
    GoodsType = 104,//商品类型

    //孵化小鸡
    ChicksSun = 105,//太阳
    ChicksDryer = 106,//烘干机
}

export type EffectTuple<EffectType> = [type: EffectType, value: number, customData?: any];

export type CardSeedRecord = {
  seed: number;
  bingoAt: number;
  targetCount: Record<number, number>;
  caught: [callIndex: number, cellIndex: number][];
}

export type CallPool = {
  callSeed: number;
  cards?: CardSeedRecord[];
  cardsGroups?: { [key: string]: CardSeedGroupRecord[] }
  // TODO: metadata
}

export type CardSeedGroupRecord = {
  seed: number[];
  bingoAt: number[];
  bingoCount: number;
  caught: [callIndex: number, cellIndex: number][][];
}
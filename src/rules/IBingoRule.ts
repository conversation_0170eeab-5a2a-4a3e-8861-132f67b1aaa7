import {CellEffect, EffectTuple} from '..';
import {Card, CardBingoTester, ICardFarseer} from '../Card';
import {CardCell} from '../CardCell';
import {IRoundProgressPreset, PlayerRoundProgress} from '../PlayerRoundProgress';

export type RoundInitializer = (preset: IRoundProgressPreset, progress: PlayerRoundProgress) => void;
export type CellEffectHandler = (progress: PlayerRoundProgress, card: Card, cell: CardCell, effect: EffectTuple<CellEffect>) => void;
export type CellDaubedHandler = (progress: PlayerRoundProgress, card: Card, cell: CardCell) => void;

export type Farsee = (playedCard: Card) => ICardFarseer
export type CheckState = (progress: PlayerRoundProgress) => void;

export interface IBingoRule {
    initializeCardEffect: RoundInitializer;
    initializeCellEffect: RoundInitializer;
    bingoTester: CardBingoTester;
    beforeCellMeetEffect: CellEffectHandler;
    afterCellDaub: CellDaubedHandler;
    farsee: Farsee;
    checkState?: CheckState;
}

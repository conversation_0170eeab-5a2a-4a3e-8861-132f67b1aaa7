import _ from 'lodash';
import { Card, CardBingoTester, CardEffect, CellEffect, EffectTuple, NaturalBingoFlag, pickRandomItemsWithMutation, RandomSequence } from '..';
import { handleDaubByPowerup, initializeBoxAndTicketRewardsAndOthers, initializeCollectionRewardsAkaShadowCard } from './common';
import { CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer } from './IBingoRule';

///// Rule /////
export const Constant = Object.freeze({

});

const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);

};


const initializeCellEffect: RoundInitializer = (preset, progress) => {
  initializeBoxAndTicketRewardsAndOthers(preset, progress);

};

const bingoTester: CardBingoTester = card => {
  if (card.findEffectValue(CardEffect.Counter) as number >= (card.findEffectValue(CardEffect.CounterTarget) as number)) return true;
  return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);

};


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  
};

const farsee: Farsee = card => {
  const naturalBingoResult = { naturalBingo: NaturalBingoFlag.Yes, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const ruleName: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

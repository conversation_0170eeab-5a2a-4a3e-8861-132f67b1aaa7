import _ from 'lodash';
import { NaturalBingoFlag } from '..';
import { Card, CardBingoTester } from '../Card';
import { CardCell } from '../CardCell';
import { CellEffect } from '../effects';
import { handleDaubByPowerup, initializeBoxAndTicketRewardsAndOthers, initializeCenterDaub, initializeCollectionRewardsAkaShadowCard } from './common';
import { <PERSON><PERSON>aubed<PERSON><PERSON><PERSON>, CellEffectHandler, Farsee, IBingoRule, RoundInitializer } from './IBingoRule';

///// Rule Bamboo Bamboo /////
const initializeCardEffect = initializeCollectionRewardsAkaShadowCard;

const cornerIndexes = [0, 4, 20, 24];
const initializeCellEffect: RoundInitializer = (host, progress) => {
  _.forEach(progress.cards, card => {
    // Panda at center
    progress.pushEffectToCell(card, card.cellAtIndex(12), CellEffect.Panda);
    // Bamboos at corner
    _.forEach(cornerIndexes, cellIndex => {
      progress.pushEffectToCell(card, card.cellAtIndex(cellIndex), CellEffect.Bamboo);
    });
  });

  initializeCenterDaub(host, progress);
  initializeBoxAndTicketRewardsAndOthers(host, progress);
};

const REQUIRED_EATEN_BAMBOO_COUNT = 2;
const bingoTester: CardBingoTester = card => {
  const eatenBambooCount = _.filter(cornerIndexes, cellIndex => card.cellAtIndex(cellIndex).hasEffect(CellEffect.EatenBamboo)).length;
  return eatenBambooCount >= REQUIRED_EATEN_BAMBOO_COUNT;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);

  // Handle panda eat bamboos
  if (effect[0] === CellEffect.Panda && cell.hasEffect(CellEffect.Bamboo)) {
    progress.pushEffectToCell(card, cell, CellEffect.EatenBamboo);
  }
};

// Simple tree searching
const isConnected = (card: Card, from: CardCell, to: CardCell, path: number[] = []): boolean => {
  if (from.index === to.index) return true;

  if (!path) path = [];
  // if (_.indexOf(path, from.index) !== -1) return false;
  path.push(from.index);

  // const validNeighbor: CardCell[] = [];
  const l = card.cellAtLocation(from.x - 1, from.y);
  const r = card.cellAtLocation(from.x + 1, from.y);
  const t = card.cellAtLocation(from.x, from.y - 1);
  const b = card.cellAtLocation(from.x, from.y + 1);
  const validNeighbors = _.filter([l, r, t, b] as CardCell[], cell => cell && cell.isDaubed && _.indexOf(path, cell.index) === -1);

  let meet = false;
  for (let i = 0; i < validNeighbors.length; i += 1) {
    if (isConnected(card, validNeighbors[i], to, path)) meet = true;
  }

  return meet;
};

enum Direction {
  Up,
  Right,
  Down,
  Left,
}

function determineDirectionPriority(from: CardCell, to: CardCell): Direction[] {
  const directionPriority = [Direction.Up, Direction.Right, Direction.Down, Direction.Left];
  if (to.x - from.x > 0) {
    directionPriority[0] = Direction.Right;
    directionPriority[3] = Direction.Left;
    if (to.y - from.y > 0) {
      directionPriority[1] = Direction.Down;
      directionPriority[2] = Direction.Up;
    } else {
      directionPriority[1] = Direction.Up;
      directionPriority[2] = Direction.Down;
    }
  } else if (to.x - from.x < 0) {
    directionPriority[0] = Direction.Left;
    directionPriority[3] = Direction.Right;
    if (to.y - from.y > 0) {
      directionPriority[1] = Direction.Down;
      directionPriority[2] = Direction.Up;
    } else {
      directionPriority[1] = Direction.Up;
      directionPriority[2] = Direction.Down;
    }
  } else {
    directionPriority[1] = Direction.Right;
    directionPriority[2] = Direction.Left;
    if (to.y - from.y > 0) {
      directionPriority[0] = Direction.Down;
      directionPriority[3] = Direction.Up;
    } else if (to.y - from.y < 0) {
      directionPriority[0] = Direction.Up;
      directionPriority[3] = Direction.Down;
    } else {
      throw new Error('Invalid operation.');
    }
  }
  return directionPriority;
}

const findDaubCandidates = (card: Card, from: CardCell, to: CardCell): { onceTarget: number | null, twiceTarget: [number, number] | null } => {
  const result: { onceTarget: number | null, twiceTarget: [number, number] | null } = {
    onceTarget: null,
    twiceTarget: null,
  };

  const routine = (card: Card, from: CardCell, to: CardCell, path: number[] = [], requiredDaubIndex: number[] = []) => {
    if (result.onceTarget !== null && result.twiceTarget !== null) return;

    path.push(from.index);
    if (!from.isDaubed) requiredDaubIndex.push(from.index);
    if (requiredDaubIndex.length > 2) return;

    if (from.index === to.index) {
      // assert(requiredDaubIndex.length > 0 && requiredDaubIndex.length <= 2);
      if (requiredDaubIndex.length === 1) result.onceTarget = _.get(requiredDaubIndex, 0, -1);
      else result.twiceTarget = [_.get(requiredDaubIndex, 0, -1), _.get(requiredDaubIndex, 1, -1)];
      return;
    }

    const directionPriority = determineDirectionPriority(from, to);
    let i = 0;
    while (i < directionPriority.length) {
      const direction = directionPriority[i];
      i += 1;
      let nextMove: CardCell | undefined;
      if (direction === Direction.Up) nextMove = card.cellAtLocation(from.x, from.y - 1);
      if (direction === Direction.Right) nextMove = card.cellAtLocation(from.x + 1, from.y);
      if (direction === Direction.Down) nextMove = card.cellAtLocation(from.x, from.y + 1);
      if (direction === Direction.Left) nextMove = card.cellAtLocation(from.x - 1, from.y);

      if (!nextMove) continue;
      if (_.indexOf(path, nextMove.index) !== -1) continue;
      routine(card, nextMove, to, path, requiredDaubIndex);
    }
  };

  routine(card, from, to);

  return result;
};

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const pandaCell = _.find(card.cells, cell => cell.hasEffect(CellEffect.Panda));
  if (!pandaCell) throw new Error('Invalid card data.');

  _.forEach(cornerIndexes, bambooIndex => {
    const bambooCell = card.cellAtIndex(bambooIndex);
    if (bambooCell.hasEffect(CellEffect.EatenBamboo)) return;

    // Panda move to bamboo cell
    if (isConnected(card, pandaCell, bambooCell)) {
      progress.pullAllEffectFromCell(card, pandaCell, CellEffect.Panda);
      progress.pushEffectToCell(card, bambooCell, CellEffect.Panda);
    }
  });
};


const bambooFarsee: Farsee = card => {
  const naturalBingoResult = { naturalBingo: NaturalBingoFlag.Yes, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };
  if (card.canBingo) return naturalBingoResult;
  const poorResult = { naturalBingo: NaturalBingoFlag.No, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };

  let onceTarget: number | null = null;
  let twiceTarget: [number, number] | null = null;

  const eatenBambooCount = _.filter(cornerIndexes, cellIndex => card.cellAtIndex(cellIndex).hasEffect(CellEffect.EatenBamboo)).length;
  if (eatenBambooCount >= REQUIRED_EATEN_BAMBOO_COUNT) return naturalBingoResult;
  if (eatenBambooCount === 0) return poorResult; // Only find one case

  const pandaCell = _.find(card.cells, cell => cell.hasEffect(CellEffect.Panda));
  // assert(pandaCell);
  if (!pandaCell) return poorResult;
  const nextBambooCell = _.find(card.cells, cell => cell.hasEffect(CellEffect.Bamboo) && !cell.hasEffect(CellEffect.EatenBamboo));
  // assert(nextBambooCell);
  if (!nextBambooCell) return poorResult;

  const requirements = findDaubCandidates(card, pandaCell, nextBambooCell);
  onceTarget = requirements.onceTarget;
  twiceTarget = requirements.twiceTarget;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};


// const bambooPredictor: NaturalBingoPredictor = (progress, host) => {
//   const card = progress.cards[0];

//   const record: CardSeedRecord = {
//     seed: card.seed,
//     bingoAt: -1,
//     daubCount: 0,
//     cellIndexesLeadToBingo: [],
//     requiredDaubCountToBingo: RequiredDaubCount.MoreThanTwo,
//   };

//   let canNaturalBingo = false;
//   _.forEach(host.fullCallList, (callNumber, callIndex) => {
//     const startTimeThisCall = (callIndex + 1) * host.callInterval;
//     CommandFactory.receiveCall(startTimeThisCall).execute(progress);
//     const theCall = _.last(progress.getReceivedCallList());
//     const targetCell = _.find(card.cells, cell => cell.value === theCall);
//     if (!targetCell) return;

//     const isValidDaub = CommandFactory.tryDaub(startTimeThisCall, 0, targetCell.index).execute(progress);
//     if (isValidDaub) {
//       record.daubCount += 1;
//     }
//     canNaturalBingo = card.canBingo;
//     if (canNaturalBingo) {
//       record.bingoAt = callIndex;
//       record.requiredDaubCountToBingo = RequiredDaubCount.Zero;
//       return false;
//     }
//   });

//   if (canNaturalBingo) return record;

//   const eatenBambooCount = _.filter(cornerIndexes, cellIndex => card.cellAtIndex(cellIndex).hasEffect(CellEffect.EatenBamboo)).length;
//   assert(eatenBambooCount < REQUIRED_EATEN_BAMBOO_COUNT);
//   if (eatenBambooCount === 0) return record; // Only find one case

//   const pandaCell = _.find(card.cells, cell => cell.hasEffect(CellEffect.Panda));
//   assert(pandaCell);
//   const nextBambooCell = _.find(card.cells, cell => cell.hasEffect(CellEffect.Bamboo) && cell.hasNoEffect(CellEffect.EatenBamboo));
//   assert(nextBambooCell);

//   const requirements = findDaubCandidates(card, pandaCell, nextBambooCell);
//   const cellIndexesLeadToBingo: number[][] = [];
//   if (requirements[RequiredDaubCount.Two] !== null) {
//     record.requiredDaubCountToBingo = RequiredDaubCount.Two;
//     cellIndexesLeadToBingo.push(requirements[RequiredDaubCount.Two] as number[]);
//   }
//   if (requirements[RequiredDaubCount.One] !== null) {
//     record.requiredDaubCountToBingo = RequiredDaubCount.One;
//     cellIndexesLeadToBingo.push(requirements[RequiredDaubCount.One] as number[]);
//   }
//   if (cellIndexesLeadToBingo.length <= 0) return record;

//   record.cellIndexesLeadToBingo = _.sortBy(cellIndexesLeadToBingo, indexes => indexes.length);

//   return record;
// };

export const bamboo: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee: bambooFarsee,
};

import {
    Card, CardCell,
    CellEffect,
    NaturalBingoFlag,
    randomInt
} from '..';
import {CardBingoTester} from '../Card';
import {
    getCounterTarget,
    handleDaubByPowerup,
    initializeBoxAndTicketRewardsAndOthers,
    initializeCardSeed,
    initializeCollectionRewardsAkaShadowCard,
    initializeCounter,
    readCounter,
    updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

const COLLECT_TARGET = 5;//收集酒瓶个数
const WINE_BOTTLE_COUNT = 7;//酒瓶个数 （酒瓶有3中颜色）
enum bottleState { A, B}//酒瓶状态 A 保留在原地，B掉落

///// Rule supplies winter /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
    initializeCollectionRewardsAkaShadowCard(preset, progress);
    progress.cards.forEach(card => {
        initializeCardSeed(card, card.seed);
        initializeCounter(card, COLLECT_TARGET);
    });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
    initializeBoxAndTicketRewardsAndOthers(host, progress);
    progress.cards.forEach(card => {
        const noDaubCells = card.getNoDaubCells();
        const cells = card.pickRandomCellsWithMutation(noDaubCells, WINE_BOTTLE_COUNT);
        let idx = 0;
        for (let i = 0; i < WINE_BOTTLE_COUNT; i++) {
            const colorType = randomInt(0, 2, card);//enum ColorType { A, B, C}//酒瓶颜色
            cells[idx++].pushEffect(CellEffect.WinBottle, 0, {count: 1, colorType});
        }
    });
};


const bingoTester: CardBingoTester = card => {
    if (readCounter(card) >= getCounterTarget(card)) return true;
    return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
    handleDaubByPowerup(progress, card, cell, effect);
};

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
    checkBottleState(card);
};
const checkBottleState = (card: Card) => {
    const aubCells = card.getDaubCells();
    const WineY = COLLECT_TARGET - readCounter(card);//酒杯酒的y轴位置
    for (const itemCell of aubCells) {
        const effect = itemCell.findEffect(CellEffect.WinBottle);
        if (effect && effect[1] === bottleState.A) {
            const isPass = checkBottlePass(card, itemCell, WineY);
            if (isPass) {
                effect[1] = bottleState.B;
                updateCounter(card, readCounter(card) + effect[2].count);
                checkBottleState(card);//又从第一个开始判断，是否可以掉下去
                break;
            }
        }
    }
};
//判断酒进度和cell之前是有是空的,或者酒瓶已经被淹
const checkBottlePass = (card: Card, daubedCell: CardCell, WineY: number): boolean => {
    if (daubedCell.y >= WineY || daubedCell.y === CardCell.cellStepCount - 1) {//酒瓶已经被淹
        return true;
    }
    for (let y = daubedCell.y + 1; y <= WineY; y++) {//WineY 有==5的时候
        const cell = card.cellAtLocation(daubedCell.x, y);
        if (cell && !cell.isDaubed) {
            return false;
        }
    }
    return true;
};
const farsee: Farsee = card => {
    const naturalBingoResult = {
        naturalBingo: NaturalBingoFlag.Yes,
        daubOnceTarget: -1,
        daubTwiceTarget: [-1, -1] as [number, number]
    };

    if (card.canBingo) return naturalBingoResult;

    const onceTarget: number | null = null;
    const twiceTarget: [number, number] | null = null;

    return {
        naturalBingo: NaturalBingoFlag.No,
        daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
        daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
    };
};
export const bartender: IBingoRule = {
    initializeCardEffect,
    initializeCellEffect,
    bingoTester,
    beforeCellMeetEffect,
    afterCellDaub,
    farsee
};

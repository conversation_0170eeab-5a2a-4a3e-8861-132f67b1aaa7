import {RandomSequence} from '../RandomSequence';
import {
    getCounterTarget,
    handleDaubByPowerup,
    initializeBoxAndTicketRewardsAndOthers, initializeCardSeed,
    initializeCollectionRewardsAkaShadowCard, initializeCounter, readCounter, updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';
import _ from 'lodash';
import {pickRandomItemsWithMutation} from '../Utils';
import {CardEffect, CellEffect} from '../effects';
import {Card, CardBingoTester, NaturalBingoFlag} from '../Card';
import {CardCell} from "../CardCell";
///// Rule Beauty House /////
const NEED_COLLECT_GOODS_COUNT = 4;
const ALL_GOODS_COUNT = 8;
const GOODS_ADD_TARGET = 1;//加目标1
const GOODS_Type = {
    lipstick: 1,//口红
    perfume: 2,//香水
    makeupMirror: 3,//化妆镜
    smallBrush: 4//小刷子
};
const initializeCardEffect: RoundInitializer = (preset, progress) => {
    initializeCollectionRewardsAkaShadowCard(preset, progress);
    progress.cards.forEach(card => {
        initializeCardSeed(card, card.seed);
        initializeCounter(card, NEED_COLLECT_GOODS_COUNT);
        const rs = new RandomSequence(preset.cardPresets[card.index].seed);
        initializeHouseInfo(card, rs);
    });
};
//选取4个商品
//卡片初始在下方生成4个空购物袋和需要的商品（用虚影或是灰图显示）（随机从可能出现的商品中选择，每种最多出现2次）
const initializeHouseInfo = (card: Card, randomSequence: RandomSequence) => {
    const goodsArray = Object.keys(GOODS_Type) as (keyof typeof GOODS_Type)[];
    const selectedGoods: { [key: string]: number } = {};
    const resultMaps: { [key: number]: number } = {};
    const result: number[] = [];

    while (result.length < 4) {
        const randomGood = goodsArray[Math.floor(randomSequence.nextFloatInRange(0, 1) * goodsArray.length)];
        const goodValue = GOODS_Type[randomGood]; // 获取对应的整数值

        // 检查该商品是否已选择2次
        if (!selectedGoods[randomGood]) {
            selectedGoods[randomGood] = 0;
        }

        if (selectedGoods[randomGood] < 2) {
            result.push(goodValue); // 将整数值推入结果数组
            if (!resultMaps[goodValue]) {
                resultMaps[goodValue] = 1;
            } else {
                resultMaps[goodValue]++;
            }
            selectedGoods[randomGood]++;
        }
    }
    result.sort((a, b) => a - b);
    card.pushEffect(CardEffect.Goods, 0, {goodsList: result, resultMaps});
};
const initializeCellEffect: RoundInitializer = (preset, progress) => {
    initializeBoxAndTicketRewardsAndOthers(preset, progress);

    // Initialize eggs and rabbits
    _.forEach(progress.cards, card => {
        const rs = new RandomSequence(preset.cardPresets[card.index].seed);
        const availableCells = card.getNoDaubCells();
        //8个商品
        const goodsTypeArray = [1, 1, 2, 2, 3, 3, 4, 4];
        const goodsCells = pickRandomItemsWithMutation(availableCells, ALL_GOODS_COUNT, rs);
        let addGoodIndex = 0;
        _.forEach(goodsCells, cell => {
            cell.pushEffect(CellEffect.GoodsType, goodsTypeArray[addGoodIndex]);
            addGoodIndex++;
        });
    });
};

const bingoTester: CardBingoTester = card => {
    if (readCounter(card) >= getCounterTarget(card)) {
        return true;
    }
    return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
    handleDaubByPowerup(progress, card, cell, effect);
};

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
    // Rabbit jump to another cell and daub it
    const daubEffect = cell.findEffect(CellEffect.GoodsType);
    if (daubEffect) {
        const cardEffect = card.findEffect(CardEffect.Goods);
        //如果涂抹的商品可以加入购物车
        if (cardEffect && cardEffect[2].goodsList.indexOf(daubEffect[1]) != -1 && cardEffect[2].resultMaps[daubEffect[1]] > 0) {
            updateCounter(card, readCounter(card) + GOODS_ADD_TARGET);
            cardEffect[2].resultMaps[daubEffect[1]]--;
            card.updateEffectValue(CardEffect.Goods, 0, cardEffect[2]);
        } else {//如果是不需要的商品，会被扔掉，并帮助涂抹一个数字
            const availableCells = card.getNoDaubCells();
            const rs = new RandomSequence(_.toSafeInteger(card.seed + cell.index));
            if (availableCells.length > 0) {// Simple offset via cell index
                const targetCell = pickRandomItemsWithMutation(availableCells, 1, rs)[0];
                progress.forceDaub(card, targetCell, false, false);
            }
        }
    }
};
const farsee: Farsee = card => {
    const naturalBingoResult = {
        naturalBingo: NaturalBingoFlag.Yes,
        daubOnceTarget: -1,
        daubTwiceTarget: [-1, -1] as [number, number]
    };

    if (card.canBingo) return naturalBingoResult;

    const onceTarget: number | null = null;
    const twiceTarget: [number, number] | null = null;

    return {
        naturalBingo: NaturalBingoFlag.No,
        daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
        daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
    };
};

export const beautyHouse: IBingoRule = {
    initializeCardEffect,
    initializeCellEffect,
    bingoTester,
    beforeCellMeetEffect,
    afterCellDaub,
    farsee,
};

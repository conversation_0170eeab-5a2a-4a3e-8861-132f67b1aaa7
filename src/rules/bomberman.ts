import { <PERSON>Dau<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, IBingoRule, RoundInitializer } from './IBingoRule';
import { initializeCollectionRewardsAkaShadowCard, initializeBoxAndTicketRewardsAndOthers, CellShape, handleDaubByPowerup } from './common';
import _ from 'lodash';
import { RandomSequence } from '../RandomSequence';
import { pickRandomItemsWithMutation } from '../Utils';
import { CellEffect } from '../effects';
import { CardBingoTester, NaturalBingoFlag } from '../Card';

///// Rule Bomberman /////
const initializeCardEffect = initializeCollectionRewardsAkaShadowCard;

const BOMB_COUNT = 4;
const initializeCellEffect: RoundInitializer = (preset, progress) => {
  initializeBoxAndTicketRewardsAndOthers(preset, progress);

  // Initialize bombs
  _.forEach(progress.cards, card => {
    const rs = new RandomSequence(preset.cardPresets[card.index].seed);

    const availableCells = card.getNoDaubCells();
    const bombCells = pickRandomItemsWithMutation(availableCells, BOMB_COUNT, rs);
    _.forEach(bombCells, cell => {
      cell.pushEffect(CellEffect.Bomb);
    });
  });
};

const verticalPatterns: CellShape[] = [];
verticalPatterns.push(_.range(0, 5));
verticalPatterns.push(_.range(5, 10));
verticalPatterns.push(_.range(10, 15));
verticalPatterns.push(_.range(15, 20));
verticalPatterns.push(_.range(20, 25));

const bingoTester: CardBingoTester = card => {
  const daubedCellIndexes = _(card.cells).filter(cell => cell.isDaubed).map(cell => cell.index).value();
  const clearedColumnCount = _.filter(verticalPatterns, pattern => (
    _.intersectionBy(pattern, daubedCellIndexes).length === pattern.length
  )).length;

  if (clearedColumnCount >= 2) return true;
  return false;
};

const beforeCellMeetEffect = handleDaubByPowerup;

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  // Handle daubs on bomb
  if (cell.hasEffect(CellEffect.Bomb)) {
    const l = card.cellAtLocation(cell.x - 1, cell.y);
    const r = card.cellAtLocation(cell.x + 1, cell.y);
    const t = card.cellAtLocation(cell.x, cell.y - 1);
    const b = card.cellAtLocation(cell.x, cell.y + 1);

    if (l && !l.isDaubed) progress.forceDaub(card, l);
    if (r && !r.isDaubed) progress.forceDaub(card, r);
    if (t && !t.isDaubed) progress.forceDaub(card, t);
    if (b && !b.isDaubed) progress.forceDaub(card, b);
  }
};

const bombermanFarsee: Farsee = (card) => {
  const naturalBingoResult = { naturalBingo: NaturalBingoFlag.Yes, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };
  if (card.canBingo) return naturalBingoResult;

  let onceTarget: number | null = null;
  let twiceTarget: [number, number] | null = null;
  // Find required cells lead to bingo
  const daubedCellIndexes = _(card.cells).filter(cell => cell.isDaubed).map(cell => cell.index).value();
  const clearedColumnCount = _.filter(verticalPatterns, pattern => (
    _.intersectionBy(pattern, daubedCellIndexes).length === pattern.length
  )).length;
  if(clearedColumnCount >= 2) return naturalBingoResult;

  if (clearedColumnCount === 1) {
    _.forEach(verticalPatterns, pattern => {

      const needDaubIndexes = _.difference(pattern, daubedCellIndexes);
      if (needDaubIndexes.length === 1 && onceTarget === null) {
        onceTarget = needDaubIndexes[0];
      } else if (needDaubIndexes.length === 2 && twiceTarget === null) {
        twiceTarget = [needDaubIndexes[0], needDaubIndexes[1]];
      }

      if (onceTarget !== null && twiceTarget !== null) return false;
    });
  } else { // clearedColumnCount === 0
    const diff1Columns = _(verticalPatterns)
      .map(pattern => _.difference(pattern, daubedCellIndexes))
      .filter(diff => diff.length === 1)
      .value();

    if (diff1Columns.length >= 2) {
      twiceTarget = [diff1Columns[0][0], diff1Columns[1][0]];
    }
  }

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const bomberman: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee: bombermanFarsee,
};

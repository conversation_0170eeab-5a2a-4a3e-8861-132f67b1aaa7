import { handleDaubByPowerup, initializeBoxAndTicketRewardsAndOthers, initializeCollectionRewardsAkaShadowCard } from './common';
import { CellDaubedHandler, Farsee, IBingoRule } from './IBingoRule';
import _ from 'lodash';
import { CardBingoTester } from '../Card';
import { CellEffect } from '../effects';
import { CardCell, NaturalBingoFlag } from '..';

enum CustomTag {
  PreventBurstAffect = 1,
}

///// Rule Bubble Burst /////
const initializeCardEffect = initializeCollectionRewardsAkaShadowCard;

const initializeCellEffect = initializeBoxAndTicketRewardsAndOthers;

const allCellIndexes = _.range(25);
const bingoTester: CardBingoTester = (card) => {
  const daubedIndex = _(card.cells).filter(cell => cell.isDaubed).map(cell => cell.index).value();
  const isBingo = _.intersection(allCellIndexes, daubedIndex).length === allCellIndexes.length;
  return isBingo;
};

const beforeCellMeetEffect = handleDaubByPowerup;

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  if (cell.hasEffect(CellEffect.Tag, CustomTag.PreventBurstAffect)) {
    cell.pullAllEffect(CellEffect.Tag, CustomTag.PreventBurstAffect);
    return;
  }

  const detectedHash: Record<number, boolean> = {};
  const burstCells: CardCell[] = [];
  const affectedCells: CardCell[] = [];

  const origins: CardCell[] = [cell];

  while (origins.length > 0) {
    const origin = origins.pop() as CardCell;

    const l = card.cellAtLocation(origin.x - 1, origin.y);
    const r = card.cellAtLocation(origin.x + 1, origin.y);
    const t = card.cellAtLocation(origin.x, origin.y - 1);
    const b = card.cellAtLocation(origin.x, origin.y + 1);
    const tl = card.cellAtLocation(origin.x - 1, origin.y - 1);
    const tr = card.cellAtLocation(origin.x + 1, origin.y - 1);
    const bl = card.cellAtLocation(origin.x - 1, origin.y + 1);
    const br = card.cellAtLocation(origin.x + 1, origin.y + 1);

    _.forEach([l, r, t, b, tl, tr, bl, br], neighbor => {
      if (!neighbor || neighbor.isDaubed || detectedHash[neighbor.index]) return;
      detectedHash[neighbor.index] = true;
      
      if (neighbor.hasEffect(CellEffect.BubbleStatusC)) {
        // Status C => Burst a.k.a Daub
        burstCells.push(neighbor);
        origins.push(neighbor);

      } else {
        // Status promotion
        affectedCells.push(neighbor);
      }
    });
  }

  _.forEach(affectedCells, affected => {
    if (affected.hasEffect(CellEffect.BubbleStatusB)) {
      // Status B => Status C
      progress.pullAllEffectFromCell(card, affected, CellEffect.BubbleStatusB);
      progress.pushEffectToCell(card, affected, CellEffect.BubbleStatusC);
    } else {
      // Status A => Status B
      progress.pushEffectToCell(card, affected, CellEffect.BubbleStatusB);
    }
  });

  _.forEach(burstCells, burst => {
    cell.pullAllEffect(CellEffect.BubbleStatusC);
    // Only affect once
    progress.pushEffectToCell(card, burst, CellEffect.Tag, CustomTag.PreventBurstAffect);
    progress.forceDaub(card, burst);
  });
};

const bubbleFarsee: Farsee = card => {
  const naturalBingoResult = { naturalBingo: NaturalBingoFlag.Yes, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };
  if (card.canBingo) return naturalBingoResult;

  let onceTarget: number | null = null;
  let twiceTarget: [number, number] | null = null;

  // Find required cells lead to bingo
  const cellIndexesLeadToBingo: number[][] = [];
  const daubedCellIndexes = _(card.cells).filter(cell => cell.isDaubed).map(cell => cell.index).value();

  const bingoRequiredIndexes = _.difference(allCellIndexes, daubedCellIndexes);
  if (bingoRequiredIndexes.length === 0) return naturalBingoResult;

  if (bingoRequiredIndexes.length === 2 && twiceTarget === null) {
    twiceTarget = [bingoRequiredIndexes[0], bingoRequiredIndexes[1]];
    cellIndexesLeadToBingo.push(bingoRequiredIndexes);
  } else if (bingoRequiredIndexes.length === 1 && onceTarget === null) {
    onceTarget = bingoRequiredIndexes[0];
  }

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const bubbleBurst: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee: bubbleFarsee,
};

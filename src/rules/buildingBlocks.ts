import _ from 'lodash';
import { Card, CardBingoTester, CardEffect, CellEffect, EffectTuple, NaturalBingoFlag, pickRandomItemsWithMutation, RandomSequence } from '..';
import { handleDaubByPowerup, initializeBoxAndTicketRewardsAndOthers, initializeCollectionRewardsAkaShadowCard, initializeCounter, readCounter, updateCounter } from './common';
import { CellDaubed<PERSON>andler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer } from './IBingoRule';

///// Rule /////
export const Constant = Object.freeze({
  BlockGoal: 12,
  TotalBlockCount: 18,
});

const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);

  _.forEach(progress.cards, card => {
    initializeCounter(card, Constant.BlockGoal);
  });

};


const initializeCellEffect: RoundInitializer = (preset, progress) => {
  initializeBoxAndTicketRewardsAndOthers(preset, progress);

  _.forEach(progress.cards, card => {
    const rs = new RandomSequence(card.seed);
    const blockCells = pickRandomItemsWithMutation(card.getNoDaubCells(), Constant.TotalBlockCount, rs);

    _.forEach(blockCells, cell => {
      cell.pushEffect(CellEffect.Tag);
    });
  });
};

const bingoTester: CardBingoTester = card => {
  const collectedBlocks = _.filter(card.getCollectedEffects(), ([t]) => t === CellEffect.Tag);
  return collectedBlocks.length >= Constant.BlockGoal;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  if (!cell.hasEffect(CellEffect.Tag)) return;

  updateCounter(card, readCounter(card) + 1);
};

const farsee: Farsee = card => {
  const naturalBingoResult = { naturalBingo: NaturalBingoFlag.Yes, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const buildingBlocks: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

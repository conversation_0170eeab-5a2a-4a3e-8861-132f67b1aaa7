import _ from 'lodash';
import {Card, CellEffect, NaturalBingoFlag} from '..';
import {CardBingoTester} from '../Card';
import {CardCell} from '../CardCell';
import {
  getCounterTarget,
  handleDaubByPowerup,
  initializeBoxAndTicketRewardsAndOthers, initializeCenterDaub,
  initializeCollectionRewardsAkaShadowCard,
  initializeCounter,
  readCounter,
  updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

const topY = 0;
const bottomY = 4;

///// Rule Candy Crush /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);

  _.forEach(progress.cards, card => {
    initializeCounter(card, 1);
  });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
  initializeCenterDaub(host, progress);
  initializeBoxAndTicketRewardsAndOthers(host, progress);
};


const bingoTester: CardBingoTester = card => {
  if (readCounter(card) >= getCounterTarget(card)) return true;
  return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const moveCell = findNextCell(card);
  if (moveCell == null) {
    return;
  }
  const heroCell = _.find(card.cells, c => c.hasEffect(CellEffect.Boy));
  const finalCell: CardCell = moveHero(heroCell, moveCell);
  if (finalCell.y === topY) {
    updateCounter(card, 1);
  }
};

const findNextCell = (card: Card): CardCell | null => {
  const startCells: CardCell[] = card.getCardsYEdgeCells(bottomY);
  const checkedDict: Record<number, boolean> = {};
  const searchQueue: CardCell[] = startCells.filter(c => c.isDaubed);
  if (searchQueue.length <= 0) {
    return null;
  }
  let deepCells: CardCell = searchQueue[0];
  let index = 0;
  while (index < searchQueue.length) {
    const start = searchQueue[index];
    const uncheckedAdjacentCells = _.filter(getNextLocation(card, start), c => !checkedDict[c.index]);
    _.forEach(uncheckedAdjacentCells, ac => {
      checkedDict[ac.index] = true;
      if (!ac.isDaubed) return;
      if (deepCells.y > ac.y) {
        deepCells = ac;
      }
      searchQueue.push(ac);
    });
    index += 1;
  }
  return deepCells;
};

const getNextLocation = (card: Card, cell: CardCell): CardCell[] => {
  return _.compact([
    card.cellAtLocation(cell.x - 1, cell.y),
    card.cellAtLocation(cell.x + 1, cell.y),
    card.cellAtLocation(cell.x, cell.y - 1),
  ]);
};


const moveHero = (from: CardCell | undefined, to: CardCell) => {
  let move = true;
  if (from !== undefined) {
    if (from.y <= to.y) {
      move = false;
    }
    else {
      from.pullAllEffect(CellEffect.Boy);
    }
  }
  if (move) {
    to.pushEffect(CellEffect.Boy);
    return to;
  }
  return from as CardCell;
};


const farsee: Farsee = card => {
  const naturalBingoResult = {
    naturalBingo: NaturalBingoFlag.Yes,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const candyCrush: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

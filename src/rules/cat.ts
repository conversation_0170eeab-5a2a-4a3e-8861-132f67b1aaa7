import {CardBingoTester, Card, NaturalBingoFlag} from "../Card";
import {CardCell} from "../CardCell";
import {CellEffect} from "../effects";
import {RandomSequence} from "../RandomSequence";
import {
  initializeCollectionRewardsAkaShadowCard,
  initializeCounter,
  initializeBoxAndTicketRewardsAndOthers,
  readCounter,
  getCounterTarget,
  handleDaubByPowerup,
  updateCounter
} from "./common";
import {RoundInitializer, CellEffectHandler, CellDaubedHandler, Farsee, IBingoRule} from "./IBingoRule";

const GENERATE_PROB = 0.50;//生成铃铛的几率
enum CatState { Bell, Coin, YuanBao, FortuneCat }//招财猫状态

///// Rule 招财猫 /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);

  progress.cards.forEach(card => {
    initializeCounter(card, 8);
  });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
  initializeBoxAndTicketRewardsAndOthers(host, progress);
};


const bingoTester: CardBingoTester = card => {
  if (readCounter(card) >= getCounterTarget(card)) return true;
  return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const rs = new RandomSequence(card.seed + cell.index);
  const prob = rs.nextSingle();
  //如果满足几率生成一个铃铛
  if (prob < GENERATE_PROB) {
    updateCounter(card, readCounter(card) + 1);
    findPartner(card, cell, CatState.Bell);
  }
};

const findPartner = (card: Card, cell: CardCell, state: number, composePath: { before: number, after: number, index: number }[] = []) => {
  const targetCells = card.cells.filter(cell => cell.isDaubed && cell.hasEffect(CellEffect.Cat, state));
  if (targetCells.length > 0) {
    targetCells[0].pullAllEffect(CellEffect.Cat);
    if (state + 1 < CatState.FortuneCat) {
      composePath.push({before: state, after: state + 1, index: targetCells[targetCells.length - 1].index});
      findPartner(card, cell, state + 1, composePath);
    } else {
      composePath.push({before: state, after: CatState.FortuneCat, index: targetCells[targetCells.length - 1].index});
      cell.pushEffect(CellEffect.Cat, CatState.FortuneCat, composePath);
    }
  } else {
    cell.pushEffect(CellEffect.Cat, state, composePath);
  }
};

const farsee: Farsee = card => {
  const naturalBingoResult = {
    naturalBingo: NaturalBingoFlag.Yes,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const cat: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

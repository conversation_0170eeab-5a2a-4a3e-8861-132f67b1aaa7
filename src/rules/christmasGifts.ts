import _ from 'lodash';
import {CardEffect, CellEffect, NaturalBingoFlag, pickRandomItemsWithMutation, RandomSequence} from '..';
import {CardBingoTester} from '../Card';
import {
    getCounterTarget,
    handleDaubByPowerup,
    initializeBoxAndTicketRewardsAndOthers,
    initializeCollectionRewardsAkaShadowCard,
    initializeCounter,
    readCounter, updateCounter
} from './common';
import {CellDaubed<PERSON><PERSON>ler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

const bottomY = 4;
const GARLAND_COUNT = 4;

///// Rule 圣诞礼物 /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
    initializeCollectionRewardsAkaShadowCard(preset, progress);

    _.forEach(progress.cards, card => {
        initializeCounter(card, 1);
        //礼物初始位置
        card.pushEffect(CardEffect.ChristmasGifts, 0, [-1, -1, -1, -1, -1]);
    });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
    initializeBoxAndTicketRewardsAndOthers(host, progress);
    _.forEach(progress.cards, card => {
        const rs = new RandomSequence(card.seed);
        const cells = pickRandomItemsWithMutation(card.getNoDaubCells(), GARLAND_COUNT, rs);
        //将花环放入棋盘
        for (const cell of cells) {
            cell.pushEffect(CellEffect.Garland);
        }
    });
};


const bingoTester: CardBingoTester = card => {
    if (readCounter(card) >= getCounterTarget(card)) return true;
    return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
    handleDaubByPowerup(progress, card, cell, effect);
};


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
    const christmasGifts = card.findEffect(CardEffect.ChristmasGifts);
    if (christmasGifts) {
        const pos = christmasGifts[2][cell.x];
        let deep = pos;
        for (let i = pos + 1; i < bottomY + 1; i++) {
            if (!card.cellAtLocation(cell.x, i)?.isDaubed) {
                break;
            }
            deep = i;
        }
        if (pos < deep) {
            christmasGifts[2][cell.x] = deep;
        }
        if (deep === bottomY) {
            updateCounter(card, 1);
        }
    }
    if (cell.findEffect(CellEffect.Garland)) {
        const corners = card.getAdjacentCornerCells(cell);
        for (const cornerCell of corners) {
            progress.forceDaub(card, cornerCell);
        }
    }
};


const farsee: Farsee = card => {
    const naturalBingoResult = {
        naturalBingo: NaturalBingoFlag.Yes,
        daubOnceTarget: -1,
        daubTwiceTarget: [-1, -1] as [number, number]
    };

    if (card.canBingo) return naturalBingoResult;

    const onceTarget: number | null = null;
    const twiceTarget: [number, number] | null = null;

    return {
        naturalBingo: NaturalBingoFlag.No,
        daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
        daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
    };
};

export const christmasGifts: IBingoRule = {
    initializeCardEffect,
    initializeCellEffect,
    bingoTester,
    beforeCellMeetEffect,
    afterCellDaub,
    farsee,
};

import _ from 'lodash';
import { CardBingoTester, CardCell, CellEffect, NaturalBingoFlag, pickRandomItemsWithMutation, RandomSequence } from '..';
import { handleDaubByPowerup, initializeBoxAndTicketRewardsAndOthers, initializeCollectionRewardsAkaShadowCard } from './common';
import { CellDaubedHandler, Farsee, IBingoRule, RoundInitializer } from './IBingoRule';

///// Rule Clover /////
const initializeCardEffect = initializeCollectionRewardsAkaShadowCard;

const REQUIRED_COIN_COUNT = 30;
const COIN_1_COUNT = 7;
const COIN_5_COUNT = 5;
const COIN_10_COUNT = 2;
const CLOVER_COUNT = 3;
const CLOVER_GENERATION_COUNT = 3;

const initializeCellEffect: RoundInitializer = (preset, progress) => {
  initializeBoxAndTicketRewardsAndOthers(preset, progress);

  // Initialize clovers and coins
  _.forEach(progress.cards, card => {
    const rs = new RandomSequence(card.seed);

    const availableCells = card.getNoDaubCells();

    const cloverCells = pickRandomItemsWithMutation(availableCells, CLOVER_COUNT, rs);
    const coin10Cells = pickRandomItemsWithMutation(availableCells, COIN_10_COUNT, rs);
    const coin5Cells = pickRandomItemsWithMutation(availableCells, COIN_5_COUNT, rs);
    const coin1Cells = pickRandomItemsWithMutation(availableCells, COIN_1_COUNT, rs);

    _.forEach(cloverCells, c => c.pushEffect(CellEffect.Clover));
    _.forEach(coin10Cells, c => c.pushEffect(CellEffect.CloverCoin, 10));
    _.forEach(coin5Cells, c => c.pushEffect(CellEffect.CloverCoin, 5));
    _.forEach(coin1Cells, c => c.pushEffect(CellEffect.CloverCoin, 1));
  });
};

const bingoTester: CardBingoTester = card => {
  const collectedCoinCount = _(card.getCollectedEffects())
    .filter(([t]) => t === CellEffect.CloverCoin)
    .reduce((count, [, newCount]) => count + newCount, 0);

  if (collectedCoinCount >= REQUIRED_COIN_COUNT) return true;
  return false;
};

const beforeCellMeetEffect = handleDaubByPowerup;

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  // Handle clover daubs
  if (cell.hasEffect(CellEffect.Clover)) {
    const rs = new RandomSequence(_.toSafeInteger(card.seed - cell.index));

    const targetCells: CardCell[] = [];
    const noCoinNoCloverCells = card.getNoExclusiveEffectCells([CellEffect.CloverCoin, CellEffect.Clover]);

    while (targetCells.length < CLOVER_GENERATION_COUNT) {
      if (noCoinNoCloverCells.length < 1) break;

      const picked = pickRandomItemsWithMutation(noCoinNoCloverCells, 1, rs)[0];
      if (picked.isDaubed) continue;

      targetCells.push(picked);
    }

    _.forEach(targetCells, c => c.pushEffect(CellEffect.CloverCoin, 10));
  }
};

const cloverFarsee: Farsee = card => {
  const naturalBingoResult = { naturalBingo: NaturalBingoFlag.Yes, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };
  if (card.canBingo) return naturalBingoResult;

  const collectedCoinCount = _(card.getCollectedEffects())
    .filter(([t]) => t === CellEffect.CloverCoin)
    .reduce((count, [, newCount]) => count + newCount, 0);

  let onceTarget: number | null = null;
  let twiceTarget: [number, number] | null = null;

  const noDaubCells = card.getNoDaubCells();
  const noDaubCloverCells = _.intersection(noDaubCells, _.filter(card.cells, c => c.hasEffect(CellEffect.Clover)));
  const noDaubCoin10Cells = _.intersection(noDaubCells, _.filter(card.cells, c => c.hasEffect(CellEffect.CloverCoin, 10)));

  if (REQUIRED_COIN_COUNT - collectedCoinCount > 20) {
    // Cannot bingo with two coin 10
    if (noDaubCloverCells.length < 1) return { naturalBingo: NaturalBingoFlag.No, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };

    onceTarget = noDaubCloverCells[0].index;

    if (noDaubCoin10Cells.length > 0) twiceTarget = [onceTarget, noDaubCoin10Cells[0].index];
    else if (noDaubCloverCells.length > 1) twiceTarget = [onceTarget, noDaubCloverCells[1].index];

    return {
      naturalBingo: NaturalBingoFlag.No,
      daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
      daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
    };
  } else if (REQUIRED_COIN_COUNT - collectedCoinCount > 10) {
    // Can bingo with two coin 10
    if (noDaubCloverCells.length > 0) onceTarget = noDaubCloverCells[0].index;

    if (noDaubCoin10Cells.length >= 2) twiceTarget = [noDaubCoin10Cells[0].index, noDaubCoin10Cells[1].index];
    else if (noDaubCoin10Cells.length > 0 && noDaubCloverCells.length > 0) twiceTarget = [noDaubCoin10Cells[0].index, noDaubCloverCells[0].index];
    else if (noDaubCloverCells.length >= 2) twiceTarget = [noDaubCloverCells[0].index, noDaubCoin10Cells[1].index];

    return {
      naturalBingo: NaturalBingoFlag.No,
      daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
      daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
    };
  } else {
    const noDaubCoin5Cells = _.intersection(noDaubCells, _.filter(card.cells, c => c.hasEffect(CellEffect.CloverCoin, 5)));
    // Can bingo with one coin 10
    if (noDaubCoin10Cells.length > 0) {
      onceTarget = noDaubCoin10Cells[0].index;
      twiceTarget = [onceTarget, _.last(noDaubCells)?.index || 0];
    }
    else if (noDaubCoin5Cells.length >= 2) {
      if (noDaubCloverCells.length > 0) onceTarget = noDaubCloverCells[0].index;

      twiceTarget = [noDaubCoin5Cells[0].index, noDaubCoin5Cells[1].index];
    } else if (noDaubCoin5Cells.length > 0 && noDaubCloverCells.length > 0) {
      onceTarget = noDaubCloverCells[0].index;
      twiceTarget = [onceTarget, noDaubCoin5Cells[0].index];
    } else if (noDaubCloverCells.length > 0) {
      onceTarget = noDaubCloverCells[0].index;
    }

    return {
      naturalBingo: NaturalBingoFlag.No,
      daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
      daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
    };
  }
};

export const clover: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee: cloverFarsee
};

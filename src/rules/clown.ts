import _ from 'lodash';
import {Card<PERSON>ell, CellEffect, NaturalBingoFlag, newPickRandomItems, randomInt} from '..';
import {CardBingoTester} from '../Card';
import {
  handleDaubByPowerup,
  initializeBoxAndTicketRewardsAndOthers,
  initializeCardSeed,
  initializeCollectionRewardsAkaShadowCard,
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

const CANNON_COUNT = 2;
const DRUM_COUNT = 4;
const DRUM_DAUB_MAX = 3;

///// Rule Clown /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);
  progress.cards.forEach(card => {
    initializeCardSeed(card, card.seed);
  });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
  initializeBoxAndTicketRewardsAndOthers(host, progress);
  _.forEach(progress.cards, card => {
    //放炮
    const indexes: number[] = newPickRandomItems([0, 1, 2, 3, 4], CANNON_COUNT, card);
    (card.cellAtLocation(indexes[0], 0) as CardCell).pushEffect(CellEffect.ClownCannon, 0);
    (card.cellAtLocation(indexes[1], 4) as CardCell).pushEffect(CellEffect.ClownCannon, 0);
    //放鼓
    const cells = newPickRandomItems(card.getNoDaubCells(), DRUM_COUNT + 2, card);
    let count = 0;
    for (const cell of cells) {
      if (cell.hasEffect(CellEffect.ClownCannon)) {
        continue;
      }
      const adjacentCells = card.getAdjacentCells(cell, 1);
      //打鼓要涂抹的格子
      const drumCells = newPickRandomItems(adjacentCells, randomInt(1, DRUM_DAUB_MAX, card), card);
      cell.pushEffect(CellEffect.ClownDrum, 0, drumCells.map(v => v.index));
      count++;
      if (count >= 4) {
        break;
      }
    }
  });
};

const bingoTester: CardBingoTester = card => {
  return !card.cells.find(v => v.y === 1 && !v.isDaubed) || !card.cells.find(v => v.y === 3 && !v.isDaubed);
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => handleDaubByPowerup(progress, card, cell, effect);

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  if (cell.findEffectValue(CellEffect.ClownCannon) === 0) {
    for (let i = 0; i < 5; i++) {
      progress.forceDaub(card, card.cellAtLocation(cell.x, i) as CardCell, false, false);
    }
    cell.updateEffectValue(CellEffect.ClownCannon, 1);
  }
  const drum = cell.findEffect(CellEffect.ClownDrum);
  if (drum && drum[1] === 0) {
    let hasDaubed = false;
    for (const index of drum[2]) {
      const c = card.cellAtIndex(index);
      if (!c.isDaubed) {
        progress.forceDaub(card, c, false, false);
        hasDaubed = true;
      }
    }
    if (!hasDaubed) {
      cell.pullAllEffect(CellEffect.ClownDrum);
    }
  }
};


const farsee: Farsee = card => {
  const naturalBingoResult = {
    naturalBingo: NaturalBingoFlag.Yes,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};
export const clown: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

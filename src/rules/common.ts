//@ts-nocheck
import _ from 'lodash';
import {
  CellEffectHandler,
  IBingoRule,
  RoundInitializer,
  CellDaubedHandler,
  Farsee
} from './IBingoRule';
import {
  randomOneItemByWeights,
  pickRandomItems,
  pickRandomItemsWithMutation,
  getProgressSeedDeterminedByCardSeeds
} from '../Utils';
import {CardEffect, CellEffect, ProgressEffect} from '../effects';
import {CardBingoTester} from '../Card';
import {RandomSequence, RandomSequenceState} from '../RandomSequence';
import {Card, judgeManyTimes, NaturalBingoFlag, PlayerRoundProgress} from '..';
import {push, randomSetNum, logger} from '../CoreUtils2';

export const initializeCollectionRewardsAkaShadowCard: RoundInitializer = (preset, progress) => {
  const progressSeed = getProgressSeedDeterminedByCardSeeds(preset);
  const rs = new RandomSequence(progressSeed);

  const collectionRewardCountWeights =
      preset.collectionRewardInitializationMap[preset.cardCountOptionIndex][preset.betOptionIndex];
  const specifiedCount = randomOneItemByWeights(collectionRewardCountWeights, rs).shadow_cards;

  const cardIndexesToApply = pickRandomItems(_.range(progress.cardCount), specifiedCount, rs);
  _.forEach(cardIndexesToApply, (cardIndex) => {
    progress.cards[cardIndex].pushEffect(CardEffect.CollectionReward);
  });
};

export const initializeCenterDaub: RoundInitializer = (preset, progress) => {
  _.forEach(progress.cards, card => {
    const centerCell = card.cellAtIndex(12);
    centerCell.pushEffect(CellEffect.MarkAsDaubByInitialization);
    card.markCellAsDaubed(centerCell.index);
  });
};

/**
 * Initial cell items generation
 */
export const initializeBoxAndTicketRewardsAndOthers: RoundInitializer = (preset, progress) => {
      const progressSeed = getProgressSeedDeterminedByCardSeeds(preset);
      const rs = new RandomSequence(progressSeed);

      _.forEach(preset.cellEffectInitializationMaps, m => {
        const thisEffectWeights = m.map[preset.cardCountOptionIndex][preset.betOptionIndex];

        _.forEach(progress.cards, card => {
          const availableCells = _.intersection(card.getNoExclusiveEffectCells(), card.getNoDaubCells());
          const effectCount = _.min([randomOneItemByWeights(thisEffectWeights, rs).amount, availableCells.length]) as number;
          const pickedCells = pickRandomItemsWithMutation(availableCells, effectCount, rs);
          _.forEach(pickedCells, cell => {
            if (_.isArray(m.effect)) {
              // Effect type weights
              const effectType = randomOneItemByWeights(m.effect, rs).effect;
              cell.pushEffect(effectType);
            } else {
              // Effect type
              cell.pushEffect(m.effect);
            }
          });
        });
      });

      // New direct initializations
      // TODO: exclusive effect
      // Current strategy: self exclusive

      let limitEffects = preset.limitEffects || [];

      if (_.size(limitEffects) > 0) {

        let limitEffectsObj = {};
        let limitCardIndexA = [];//保存的需要动态分配effect的index

        _.each(progress.cards, (card, cardIndex) => {
          const cellEffectInitializations = preset.cardPresets[cardIndex].cellEffectInitializations;
          if (!_.isArray(cellEffectInitializations)) {
            return true;
          }
          _.each(limitEffects, effect => {
            // let effectObj =  _.findIndex(cellEffectInitializations, obj => {
            //   return obj.effect && _.includes(limitEffects, `${obj.effect[0]}_${obj.effect[1]}`);
            // }) >= 0;
            let tmpCount = 0;
            _.each(cellEffectInitializations, obj => {
              if (obj.effect && `${obj.effect[0]}_${obj.effect[1]}` === effect) {
                tmpCount += obj.count;
                //重置个数，方便统一控制
                obj.count = 0;
              }
            });
            if (tmpCount <= 0) {
              return true;
            }
            limitCardIndexA.push(cardIndex);
            let ori = _.get(limitEffectsObj, effect);
            if (!ori) {
              ori = {numsA: [], indexA: []};
            }
            push(ori.numsA, tmpCount);
            push(ori.indexA, cardIndex);
            _.set(limitEffectsObj, effect, ori);

          });
        });
        logger.debug('checklimitEffects', {cardPresets: preset.cardPresets});

        if (_.size(limitEffectsObj) > 0) {
          _.each(limitEffectsObj, (obj, effect) => {
            let maxNum = _.max(obj.numsA);
            let maxSize = _.size(obj.indexA);
            let myRandom = randomSetNum(maxNum, maxSize);
            logger.debug('checkRandomSetEnd', {myRandom, obj});
            let tmpIndex = 0;
            for (let myIndex of obj.indexA) {
              let desObj = _.find(preset.cardPresets[myIndex].cellEffectInitializations, obj => {
                return obj.effect && `${obj.effect[0]}_${obj.effect[1]}` === effect;
              });
              if (desObj && myRandom[tmpIndex]) {
                desObj.count = myRandom[tmpIndex];
              }
              tmpIndex++;
            }
          });
          logger.debug("updateDynamicEffectsEnd", {limitEffectsObj});
        }
      }

      _.forEach(progress.cards, (card, cardIndex) => {

        const cellEffectInitializations = preset.cardPresets[cardIndex].cellEffectInitializations;

        logger.debug('checkCard', {cardIndex, cellEffectInitializations});

        if (!_.isArray(cellEffectInitializations)) return;

        //约束最大effects数量

        _.forEach(cellEffectInitializations, init => {

          let tmpEffectsNum = 0;
          logger.debug('doWithInitNow', {init});

          const exclusiveCells = _.intersection(card.getNoExclusiveEffectCells(), card.getNoExclusiveEffectCells([init.effect[0]]));

          if (_.isNumber(init.daubRate)) {
            // Daub rate control
            const hits = judgeManyTimes(init.daubRate, init.count, rs);
            const cellsWillBeDaubed = _.intersection(exclusiveCells, card.getNoDaubCellsInCallList(progress.roundHost.fullCallList));
            const cellsWontBeDaubed = _.intersection(exclusiveCells, card.getNoDaubCellsNotInCallList(progress.roundHost.fullCallList));

            _.forEach(hits, hit => {
              if (hit && cellsWillBeDaubed.length > 0) {
                const pickedCell = pickRandomItemsWithMutation(cellsWillBeDaubed, 1, rs)[0];
                pickedCell.pushEffect(...init.effect);
                tmpEffectsNum++;
              } else if (!hit && cellsWontBeDaubed.length > 0) {
                const pickedCell = pickRandomItemsWithMutation(cellsWontBeDaubed, 1, rs)[0];
                pickedCell.pushEffect(...init.effect);
                tmpEffectsNum++;
              }
            });
          } else {
            // Random put
            const availableCells = _.intersection(exclusiveCells, card.getNoDaubCells());
            const putCount = _.min([availableCells.length, init.count]) as number;
            const pickedCells = pickRandomItemsWithMutation(availableCells, putCount, rs);
            _.forEach(pickedCells, cell => {
              tmpEffectsNum++;
              return cell.pushEffect(...init.effect);
            });
          }
          if (tmpEffectsNum > 0) {
            logger.debug('endDealEffect', {
              init, pushEffectsNum: tmpEffectsNum,
              cardIndex
            });
          }

        });//end deal init.
      });

    }
;

export const handleDaubByPowerup: CellEffectHandler = (progress, card, cell, effect) => {
  if (effect[0] === CellEffect.MarkAsDaubByPowerup) {
    progress.forceDaub(card, cell);
  } else if (effect[0] === CellEffect.MarkAsDaubByRocket) {
    progress.forceDaub(card, cell);
  }
};

///// Common Bingo Rule /////
const initializeCardEffect = initializeCollectionRewardsAkaShadowCard;

const initializeCellEffect: RoundInitializer = (host, progress) => {
  initializeCenterDaub(host, progress);
  initializeBoxAndTicketRewardsAndOthers(host, progress);
};

export type CellShape = number[];
const commonPatterns: CellShape[] = [];
// Vertical
commonPatterns.push(_.range(0, 5));
commonPatterns.push(_.range(5, 10));
commonPatterns.push(_.range(10, 15));
commonPatterns.push(_.range(15, 20));
commonPatterns.push(_.range(20, 25));
// Horizontal
commonPatterns.push(_.range(0, 25, 5));
commonPatterns.push(_.range(1, 25, 5));
commonPatterns.push(_.range(2, 25, 5));
commonPatterns.push(_.range(3, 25, 5));
commonPatterns.push(_.range(4, 25, 5));
// Diagonal
commonPatterns.push([0, 6, 12, 18, 24]);
commonPatterns.push([4, 8, 12, 16, 20]);
// Corner
commonPatterns.push([0, 4, 20, 24]);

const commonBingoTester: CardBingoTester = (card) => {
  const daubedCellIndexes = _(card.cells).filter(cell => cell.isDaubed).map(cell => cell.index).value();
  let canBingo = false;
  _.forEach(commonPatterns, shape => { // Search for bingo patterns
    const inShapeCount = _.intersection(shape, daubedCellIndexes).length;
    if (inShapeCount === shape.length) {
      canBingo = true;
      return false;
    }
  });

  return canBingo;
};

const beforeCellMeetEffect = handleDaubByPowerup;

const handleDaubed = _.noop;

const commonFarsee: Farsee = (card) => {
  const naturalBingoResult = {
    naturalBingo: NaturalBingoFlag.Yes,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };

  if (card.canBingo) return naturalBingoResult;

  let onceTarget: number | null = null;
  let twiceTarget: [number, number] | null = null;
  // Find required cells lead to bingo
  const daubedCellIndexes = _(card.cells).filter(cell => cell.isDaubed).map(cell => cell.index).value();
  _.forEach(commonPatterns, pattern => {
    const thisPatternBingoRequiredIndexes = _.difference(pattern, daubedCellIndexes);

    if (thisPatternBingoRequiredIndexes.length === 0) return naturalBingoResult; // Logically cannot be here, just for robustness.

    if (twiceTarget === null && thisPatternBingoRequiredIndexes.length === 2) {
      twiceTarget = [thisPatternBingoRequiredIndexes[0], thisPatternBingoRequiredIndexes[1]];
    }

    if (onceTarget === null && thisPatternBingoRequiredIndexes.length === 1) {
      onceTarget = thisPatternBingoRequiredIndexes[0];
    }

    if (onceTarget !== null && twiceTarget !== null) return false;
  });

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

// Rule utils
export function initializeCounter(card: Card, goal: number): void {
  card.pushEffect(CardEffect.CounterTarget, goal);
  card.pushEffect(CardEffect.Counter, 0);
}

export function getCounterTarget(card: Card): number {
  return card.findEffectValue(CardEffect.CounterTarget) as number;
}

export function readCounter(card: Card): number {
  return card.findEffectValue(CardEffect.Counter) as number;
}

export function updateCounter(card: Card, value: number): void {
  card.updateEffectValue(CardEffect.Counter, value);
}


export function initializeCardSeed(card: Card, seed: number): void {
  const rs = new RandomSequence(seed, true);
  card.pushEffect(CardEffect.CurrentSeed, 0, rs.getState());
}

export function initializeProgressSeed(progress: PlayerRoundProgress): void {
  const seed = [0, 0, 0, 0];
  progress.cards.forEach((card, i) => {
    seed[i] = card.seed;
  });
  const rs = new RandomSequence(seed as RandomSequenceState);
  for (let k = 0; k < 64; k += 1) {
    rs.nextInt32();
  }
  //初始化progress种子
  progress.pushEffect(ProgressEffect.CurrentSeed, 0, rs.getState());
}

export function cardCurrentSeed(card: Card): number {
  return card.findEffectValue(CardEffect.CurrentSeed) as number;
}

export function updateCardCurrentSeed(card: Card, seed: number): void {
  card.updateEffectValue(CardEffect.CurrentSeed, seed);
}

export const common: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester: commonBingoTester,
  beforeCellMeetEffect,
  afterCellDaub: handleDaubed,
  farsee: commonFarsee,
};

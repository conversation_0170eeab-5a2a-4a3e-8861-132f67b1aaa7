import { CardBingoTester } from '../Card';
import { handleDaubByPowerup, initializeBoxAndTicketRewardsAndOthers, initializeCollectionRewardsAkaShadowCard } from './common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CellEffectHandler, Farsee, IBingoRule } from './IBingoRule';
import _ from 'lodash';
import { CellEffect } from '../effects';
import { RandomSequence } from '../RandomSequence';
import { pickRandomItemsWithMutation } from '../Utils';
import { Card, NaturalBingoFlag } from '..';

///// Rule Cupid /////
const initializeCardEffect = initializeCollectionRewardsAkaShadowCard;

const initializeCellEffect = initializeBoxAndTicketRewardsAndOthers;


const paths = [
  [0, 6, 12, 18, 24],
  [4, 8, 12, 16, 20],
];
const circuitPaths = [
  [2, 6, 10, 16, 22, 18, 14, 8],
  [1, 5, 11, 17, 23, 19, 13, 7],
  [3, 7, 11, 15, 21, 17, 13, 9],
];
const REQUIRED_HEART_COUNT = 9;

function getConnectedHeartCellIndexes(card: Card): number[] {
  const connectedHeartIndexes: number[] = [];

  _.forEach(paths, path => {
    _.forEach(path, (cellIndex, pathIndex) => {
      if (!card.cellAtIndex(cellIndex).isDaubed) return;

      const prevCellIndex = path[pathIndex - 1];
      const nextCellIndex = path[pathIndex + 1];

      if (prevCellIndex && card.cellAtIndex(prevCellIndex).isDaubed) {
        connectedHeartIndexes.push(cellIndex);
        return;
      }

      if (nextCellIndex && card.cellAtIndex(nextCellIndex).isDaubed) {
        connectedHeartIndexes.push(cellIndex);
        return;
      }
    });
  });

  _.forEach(circuitPaths, circuit => {
    _.forEach(circuit, (cellIndex, pathIndex) => {
      if (!card.cellAtIndex(cellIndex).isDaubed) return;

      const prevCellIndex = circuit[pathIndex === 0 ? circuit.length - 1 : pathIndex - 1];
      const nextCellIndex = circuit[pathIndex === circuit.length - 1 ? 0 : pathIndex + 1];

      if (card.cellAtIndex(prevCellIndex).isDaubed) {
        connectedHeartIndexes.push(cellIndex);
        return;
      }

      if (card.cellAtIndex(nextCellIndex).isDaubed) {
        connectedHeartIndexes.push(cellIndex);
        return;
      }
    });
  });

  return _.uniq(connectedHeartIndexes);
}

const bingoTester: CardBingoTester = card => {
  const connectedHeartIndexes = getConnectedHeartCellIndexes(card);
  const heartCount = connectedHeartIndexes.length;
  if (heartCount >= REQUIRED_HEART_COUNT) return true;
  return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);

  // Handle daubs by cupid
  if (effect[0] === CellEffect.MarkAsDaubByCupid) {
    progress.forceDaub(card, cell);
  }
};

const CUPID_DAUB_STEP = 4;
const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  // Cupid daub
  const daubCountNotByCupid = _.filter(card.cells, cell => cell.isDaubed && !cell.hasEffect(CellEffect.MarkAsDaubByCupid)).length;
  if (daubCountNotByCupid > 0 && daubCountNotByCupid % CUPID_DAUB_STEP === 0) {
    const rs = new RandomSequence(_.toSafeInteger(card.seed + daubCountNotByCupid)); // offset by daubCountNotByCupid
    const noDaubCells = card.getNoDaubCells();
    const targetCell = pickRandomItemsWithMutation(noDaubCells, 1, rs)[0];
    progress.pushEffectToCell(card, targetCell, CellEffect.MarkAsDaubByCupid);
  }
};

function getCellIndexOfPosition(x: number, y: number): number | null {
  if (x >= 0 && x <= 5 && y >= 0 && y <= 5) return x * 5 + y;
  return null;
}

function getNearbyCellIndexes(cellIndex: number): number[] {
  const x = _.toInteger(cellIndex / 5);
  const y = cellIndex % 5;

  const indexes: number[] = [];
  const topLeft = getCellIndexOfPosition(x - 1, y - 1);
  const topRight = getCellIndexOfPosition(x + 1, y - 1);
  const bottomLeft = getCellIndexOfPosition(x - 1, y + 1);
  const bottomRight = getCellIndexOfPosition(x + 1, y + 1);

  if (topLeft !== null) indexes.push(topLeft);
  if (topRight !== null) indexes.push(topRight);
  if (bottomLeft !== null) indexes.push(bottomLeft);
  if (bottomRight !== null) indexes.push(bottomRight);

  return indexes;
}

const cupidFarsee: Farsee = card => {
  const naturalBingoResult = { naturalBingo: NaturalBingoFlag.Yes, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };
  if (card.canBingo) return naturalBingoResult;

  let onceTarget: number | null = null;
  let twiceTarget: [number, number] | null = null;

  // Find required cells lead to bingo
  const connectedHeartIndexes = getConnectedHeartCellIndexes(card);
  const heartCount = connectedHeartIndexes.length;
  const daubedCellIndexes = _(card.cells).filter(cell => cell.isDaubed).map(cell => cell.index).value();
  const lonelyCellIndexes = _.difference(daubedCellIndexes, connectedHeartIndexes);
  if (lonelyCellIndexes.length >= 2) {
    if (heartCount + 2 > REQUIRED_HEART_COUNT) {
      const nearbyIndexes = getNearbyCellIndexes(lonelyCellIndexes[0]);
      // assert(nearbyIndexes.length > 0);
      onceTarget = _.get(nearbyIndexes, 0, -1);
    }
    if (heartCount + 4 > REQUIRED_HEART_COUNT) {
      const leadToBingoIndexes: number[] = [];
      _.forEach(_.range(2), nthLonely => {
        const nearbyIndexes = getNearbyCellIndexes(lonelyCellIndexes[nthLonely]);
        // assert(nearbyIndexes.length > 0);
        leadToBingoIndexes.push(_.get(nearbyIndexes, 0, -1));
      });

      twiceTarget = [leadToBingoIndexes[0], leadToBingoIndexes[1]];
    }
  } else if (lonelyCellIndexes.length === 1) {
    if (heartCount + 2 > REQUIRED_HEART_COUNT) {
      const nearbyIndexes = getNearbyCellIndexes(lonelyCellIndexes[0]);
      // assert(nearbyIndexes.length > 0);
      onceTarget = _.get(nearbyIndexes, 0, -1);
    }
  }

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const cupid: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee: cupidFarsee,
};

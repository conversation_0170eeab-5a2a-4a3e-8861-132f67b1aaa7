import {
  Card<PERSON>ell,
  CellEffect, EffectTuple,
  NaturalBingoFlag, newPickRandomItems,
  PlayerRoundProgress,
  ProgressEffect, randomFloat, randomInt, RandomSequence, RandomSequenceState,
  weightRandom
} from '..';
import {CardBingoTester} from '../Card';
import {
  handleDaubByPowerup,
  initializeBoxAndTicketRewardsAndOthers,
  initializeCenterDaub,
  initializeCollectionRewardsAkaShadowCard, initializeProgressSeed
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

const TargetRange = [[1, 2], [2, 3], [3, 4], [4, 5]]; //不同卡数 不同充能点数范围
const SkillDaubCount = [[3, 7, 10, 15], [2, 4, 6, 10], [2, 4, 6, 10]]; //三个技能的对应每个卡的释放个数
const SkillUseWeights = [20, 30, 30];

export interface DiscoForeverTarget {
  target: number;
  index: number;
  count: number;
  firstIndex: number;
}

///// Rule disco forever /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);
  initializeProgressSeed(progress);
  initDiscoTarget(progress);
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
  initializeCenterDaub(host, progress);
  initializeBoxAndTicketRewardsAndOthers(host, progress);
};


const bingoTester: CardBingoTester = card => {
  for (const cell of card.cells) {
    if (!cell.isDaubed) {
      return false;
    }
  }
  return true;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const effect = findTarget(progress);
  const target = effect[2] as DiscoForeverTarget;
  if (target.firstIndex == -1) {
    target.firstIndex = cell.index;
  }
  let cross = false;
  if (cell.findEffectValue(CellEffect.DiscoLengthways) === 1 || cell.findEffectValue(CellEffect.DiscoCrossShaped) === 1) {
    for (let i = 0; i < 5; i++) {
      const c = card.cellAtLocation(cell.x, i) as CardCell;
      progress.forceDaub(card, c, false, false);
    }
    cell.updateEffectValue(CellEffect.DiscoLengthways, 2);
    cross = true;
  }
  if (cell.findEffectValue(CellEffect.DiscoTransverse) === 1 || cell.findEffectValue(CellEffect.DiscoCrossShaped) === 1) {
    for (let i = 0; i < 5; i++) {
      const c = card.cellAtLocation(i, cell.y) as CardCell;
      progress.forceDaub(card, c, false, false);
    }
    cell.updateEffectValue(CellEffect.DiscoTransverse, 2);
    cross = true;
  }
  if (cross) {
    cell.updateEffectValue(CellEffect.DiscoCrossShaped, 2);
  }
  if (cell.x === target.index - 1) {
    effect[1]++;
  }
  if (target.firstIndex === cell.index) {
    checkTarget(progress);
    const effect = findTarget(progress);
    effect[2].firstIndex = -1;
  }
};

const findTarget = (progress: PlayerRoundProgress): EffectTuple<ProgressEffect> => {
  const effect = progress.findEffect(ProgressEffect.DiscoTarget);
  if (!effect) {
    throw new Error('something wrong');
  }
  return effect;
};

const checkTarget = (progress: PlayerRoundProgress) => {
  const effect = findTarget(progress);
  if (effect[1] >= effect[2].target) {
    initDiscoTarget(progress);
    useSkill(progress);
    checkTarget(progress);
  }
};

const useSkill = (progress: PlayerRoundProgress) => {
  const skillEffect = [CellEffect.DiscoLengthways, CellEffect.DiscoCrossShaped, CellEffect.DiscoTransverse];
  const noDaubCell = [];
  for (let k = 0; k < progress.cards.length; k++) {
    const card = progress.cards[k];
    for (const cell of card.cells) {
      const effectType = cell.fetchAllEffectType();
      if (cell.isDaubed || effectType.some(v => skillEffect.includes(v))) {
        continue;
      }
      noDaubCell.push({index: k, x: cell.x, y: cell.y});
    }
  }
  const cardCount = progress.cards.length;
  const val = weightRandom(SkillUseWeights, progress) + 1;
  const count = SkillDaubCount[val - 1][cardCount - 1];
  const daubCells = newPickRandomItems(noDaubCell, Math.min(count, noDaubCell.length), progress);
  for (const obj of daubCells) {
    const card = progress.cards[obj.index];
    const cell = card.cellAtLocation(obj.x, obj.y) as CardCell;
    switch (val) {
      case 1: { //随机涂抹
        cell.pushEffect(CellEffect.DiscoRandomDaub, 1);
        progress.forceDaub(card, cell, false, false);
        break;
      }
      case 2: { //直线涂抹
        const effect = randomFloat(0, 1, progress) < 0.5 ? CellEffect.DiscoTransverse : CellEffect.DiscoLengthways;
        cell.pushEffect(effect, 1);
        break;
      }
      case 3: { //十字涂抹
        cell.pushEffect(CellEffect.DiscoCrossShaped, 1);
        break;
      }
    }
  }
};

const initDiscoTarget = (progress: PlayerRoundProgress) => {
  let discoCount = 0;
  const count = [0, 0, 0, 0, 0];
  const cardCount = progress.cards.length;
  for (const card of progress.cards) {
    if (card.isBingoed) {
      continue;
    }
    card.cells.forEach((c) => {
      if (!c.isDaubed) {
        count[c.x]++;
      }
    });
  }
  const pick = [];
  //去掉剩余点击个数不满足条件的列
  for (let i = 0; i < 5; i++) {
    if (count[i] >= TargetRange[cardCount - 1][0]) {
      pick.push(i);
    }
  }
  const effect = progress.findEffect(ProgressEffect.DiscoTarget);
  let firstIndex = -1;
  if (effect !== undefined) {
    discoCount = effect[2].count + 1;
    firstIndex = effect[2].firstIndex;
  }

  let targetIndex, targetVal;
  if (pick.length > 0) {
    const i = randomInt(0, pick.length - 1, progress);
    targetIndex = pick[i] + 1;
    targetVal = randomInt(TargetRange[cardCount - 1][0], Math.min(TargetRange[cardCount - 1][1], count[targetIndex - 1]), progress);
  } else {
    targetIndex = randomInt(1, 5, progress);
    targetVal = randomInt(TargetRange[cardCount - 1][0], TargetRange[cardCount - 1][1], progress);
  }
  if (discoCount > 0) {
    progress.updateEffectValue(ProgressEffect.DiscoTarget, 0, {
      target: targetVal,
      index: targetIndex,
      count: discoCount,
      firstIndex: firstIndex
    });
  } else {
    progress.pushEffect(ProgressEffect.DiscoTarget, 0, {
      target: targetVal,
      index: targetIndex,
      count: 0,
      firstIndex: firstIndex
    });
  }
};


const farsee: Farsee = card => {
  const naturalBingoResult = {
    naturalBingo: NaturalBingoFlag.Yes,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const discoForever: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

import { RandomSequence } from '../RandomSequence';
import { handleDaubByPowerup, initializeBoxAndTicketRewardsAndOthers, initializeCollectionRewardsAkaShadowCard } from './common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CellEffectHandler, Farsee, IBingoRule, RoundInitializer } from './IBingoRule';
import _ from 'lodash';
import { pickRandomItemsWithMutation } from '../Utils';
import { CellEffect } from '../effects';
import { CardBingoTester, NaturalBingoFlag } from '../Card';

///// Rule Easter Egg /////
const initializeCardEffect = initializeCollectionRewardsAkaShadowCard;

const EGG_COUNT = 5;
const RABBIT_COUNT = 2;
const initializeCellEffect: RoundInitializer = (preset, progress) => {
  initializeBoxAndTicketRewardsAndOthers(preset, progress);

  // Initialize eggs and rabbits
  _.forEach(progress.cards, card => {
    const rs = new RandomSequence(preset.cardPresets[card.index].seed);

    const availableCells = card.getNoDaubCells();

    // Be careful about the element mutations
    const eggCells = pickRandomItemsWithMutation(availableCells, EGG_COUNT + RABBIT_COUNT, rs);
    const rabbitCells = pickRandomItemsWithMutation(eggCells, RABBIT_COUNT, rs);

    _.forEach(eggCells, cell => {
      cell.pushEffect(CellEffect.EasterEgg);
    });
    _.forEach(rabbitCells, cell => {
      cell.pushEffect(CellEffect.Rabbit);
    });
  });
};

const REQUIRED_EGG_COUNT = 3;
const bingoTester: CardBingoTester = card => {
  const eggCount = _.filter(card.getCollectedEffects(), ([t]) => t === CellEffect.EasterEgg).length;

  if (eggCount >= REQUIRED_EGG_COUNT) return true;
  return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);

  // Handle rabbit daubs
  if (effect[0] === CellEffect.MarkAsDaubByRabbit) {
    progress.forceDaub(card, cell);
  }
};

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  // Rabbit jump to another cell and daub it
  if (cell.hasEffect(CellEffect.Rabbit)) {
    const rs = new RandomSequence(_.toSafeInteger(card.seed + cell.index)); // Simple offset via cell index
    const availableCells = card.getNoDaubCells();
    const targetCell = pickRandomItemsWithMutation(availableCells, 1, rs)[0];

    progress.pushEffectToCell(card, targetCell, CellEffect.MarkAsDaubByRabbit);
  }
};

const cupidFarsee: Farsee = card => {
  const naturalBingoResult = { naturalBingo: NaturalBingoFlag.Yes, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };
  if (card.canBingo) return naturalBingoResult;

  // Cannot find target
  const poorResult = { naturalBingo: NaturalBingoFlag.No, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };

  let onceTarget: number | null = null;
  let twiceTarget: [number, number] | null = null;

  const collectedEggCount = _.filter(card.getCollectedEffects(), ([t]) => t === CellEffect.EasterEgg).length;
  // assert(collectedEggCount <= REQUIRED_EGG_COUNT);
  if (collectedEggCount < REQUIRED_EGG_COUNT - 2) return poorResult;

  // Find required cells lead to bingo
  const noDaubEggCellIndexes = _(card.getNoDaubCells()).filter(cell => cell.hasEffect(CellEffect.EasterEgg)).map(c => c.index);
  // assert(noDaubEggCells.length >= 2);
  if (REQUIRED_EGG_COUNT - collectedEggCount === 1) {
    onceTarget = _.get(noDaubEggCellIndexes, 0, -1);

  } else if (REQUIRED_EGG_COUNT - collectedEggCount === 2) {
    twiceTarget = [_.get(noDaubEggCellIndexes, 0, -1), _.get(noDaubEggCellIndexes, 1, -1)];
  }

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

// export const easterEggPredictor: NaturalBingoPredictor = (progress, host) => {
//   const card = progress.cards[0];

//   const record: CardSeedRecord = {
//     seed: card.seed,
//     bingoAt: -1,
//     daubCount: 0,
//     cellIndexesLeadToBingo: [],
//     requiredDaubCountToBingo: RequiredDaubCount.MoreThanTwo,
//   };

//   let canNaturalBingo = false;
//   _.forEach(host.fullCallList, (callNumber, callIndex) => {
//     const startTimeThisCall = (callIndex + 1) * host.callInterval;
//     CommandFactory.receiveCall(startTimeThisCall).execute(progress);
//     const theCall = _.last(progress.getReceivedCallList());
//     const targetCell = _.find(card.cells, cell => cell.value === theCall);
//     if (!targetCell) return;

//     const isValidDaub = CommandFactory.tryDaub(startTimeThisCall, 0, targetCell.index).execute(progress);
//     if (isValidDaub) {
//       record.daubCount += 1;
//     }
//     canNaturalBingo = card.canBingo;
//     if (canNaturalBingo) {
//       record.bingoAt = callIndex;
//       record.requiredDaubCountToBingo = RequiredDaubCount.Zero;
//       return false;
//     }
//   });

//   if (canNaturalBingo) return record;

//   const collectedEggCount = _.filter(card.collectedCellEffect, effect => effect === CellEffect.EasterEgg).length;
//   assert(collectedEggCount <= REQUIRED_EGG_COUNT);
//   if (collectedEggCount < REQUIRED_EGG_COUNT - 2) return record; // cannot powerup bingo

//   // Find required cells lead to bingo
//   const noDaubEggCells = _.filter(card.getNoDaubCells()).filter(cell => cell.hasEffect(CellEffect.EasterEgg));
//   assert(noDaubEggCells.length >= 2);
//   if (REQUIRED_EGG_COUNT - collectedEggCount === 1) {
//     record.requiredDaubCountToBingo = RequiredDaubCount.One;
//     record.cellIndexesLeadToBingo.push([noDaubEggCells[0].index], [noDaubEggCells[0].index, noDaubEggCells[1].index]);
//   } else if (REQUIRED_EGG_COUNT - collectedEggCount === 2) {
//     record.requiredDaubCountToBingo = RequiredDaubCount.Two;
//     record.cellIndexesLeadToBingo.push([noDaubEggCells[0].index, noDaubEggCells[1].index]);
//   }

//   return record;
// };


export const easterEgg: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee: cupidFarsee,
};

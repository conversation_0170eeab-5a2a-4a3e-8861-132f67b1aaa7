import _ from 'lodash';
import { Card, CardBingoTester, CardEffect, CellEffect, EffectTuple, NaturalBingoFlag, pickRandomItemsWithMutation, RandomSequence } from '..';
import { handleDaubByPowerup, initializeBoxAndTicketRewardsAndOthers, initializeCollectionRewardsAkaShadowCard } from './common';
import { CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer } from './IBingoRule';

///// Fast Food /////
export const Constant = Object.freeze({
  FoodCount: 8,
  BingoScoreTarget: 200,
  CountMapToOrderScore: { 1: 15, 2: 30, 3: 70 } as Record<number, number>
});

export enum FoodTypeCardEffect {
  A = 1000,
  B = 1001,
  C = 1002,
  D = 1003,
}

const perOrderRs = (cardSeed: number, orderNth: number) => new RandomSequence(cardSeed - orderNth);
const randomFoodType = (rs: RandomSequence) => 1000 + rs.nextIntegerInRange(3);
const getNewOrder = (cardSeed: number, orderNth: number): EffectTuple<CardEffect>[] => {
  const rs = perOrderRs(cardSeed, orderNth);
  const order: EffectTuple<CardEffect>[] = [[CardEffect.FastFoodOrderHeader, orderNth]];
  const targetCount = rs.nextIntegerInRange(3, 1);
  _.forEach(_.range(targetCount), () => {
    const foodType = randomFoodType(rs);
    order.push([foodType, 1]);
  });

  return order;
};

const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);

  _.forEach(progress.cards, card => {
    card.pushEffect(CardEffect.CounterTarget, Constant.BingoScoreTarget);
    card.pushEffect(CardEffect.Counter, 0);
    _.forEach([0, 1, 2], orderNth => {
      card.effects.push(...getNewOrder(card.seed, orderNth));
    });
  });
};

const initializeCellEffect: RoundInitializer = (preset, progress) => {
  _.forEach(progress.cards, card => {
    const rs = new RandomSequence(card.seed);
    const foodCells = pickRandomItemsWithMutation(card.getNoDaubCells(), Constant.FoodCount, rs);
    _.forEach(foodCells, cell => {
      cell.pushEffect(CellEffect.FastFood, randomFoodType(rs));
    });
  });

  initializeBoxAndTicketRewardsAndOthers(preset, progress);
};

const bingoTester: CardBingoTester = card => {
  if (card.findEffectValue(CardEffect.Counter) as number >= (card.findEffectValue(CardEffect.CounterTarget) as number)) return true;
  return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};

const updateOrders = (card: Card) => {
  const counter = card.findEffect(CardEffect.Counter) as EffectTuple<CardEffect>;
  if (!counter) throw new Error('Assertion Error.');

  let allFoodsCollected = true;
  let orderFoodCount = 0;
  let currentMaxOrderNth = 0;
  let orderCountNeedToGenerate = 0;

  const indexesToRemoveInDescOrder: number[] = []; // Thanks to lodash, it's not necessary to be descending order

  _.forEachRight(card.effects, ([type, value], index) => {
    const isHeader = type === CardEffect.FastFoodOrderHeader;
    const isFood = type <= FoodTypeCardEffect.D && type >= FoodTypeCardEffect.A;

    if (isFood) {
      orderFoodCount += 1;
      allFoodsCollected = allFoodsCollected && (value <= 0);
    } else if (isHeader) {
      if (value > currentMaxOrderNth) currentMaxOrderNth = value;

      if (allFoodsCollected) {
        // Order complete
        indexesToRemoveInDescOrder.push(..._.range(index + orderFoodCount, index - 1));
        orderCountNeedToGenerate += 1;
        const score = Constant.CountMapToOrderScore[orderFoodCount];
        if (!score) throw new Error('Assertion Error.');
        counter[1] += score;
      }

      // Reset
      allFoodsCollected = true;
      orderFoodCount = 0;

    } else return;
  });

  // Remove completed orders
  _.pullAt(card.effects, indexesToRemoveInDescOrder);

  _.forEach(_.range(orderCountNeedToGenerate), n => {
    const orderNth = currentMaxOrderNth + n + 1;
    card.effects.push(...getNewOrder(card.seed, orderNth));
  });
};

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const cellFoodType = cell.findEffectValue(CellEffect.FastFood);
  if (cellFoodType === undefined) return;

  let thisOrderUpdated = true;
  _.forEach(card.effects, (effect) => {
    const [t, v] = effect;
    if (t === CardEffect.FastFoodOrderHeader) {
      thisOrderUpdated = false;
      return;
    }

    if (!thisOrderUpdated && t === cellFoodType && v > 0) {
      effect[1] -= 1;
      thisOrderUpdated = true;
    }
  });

  updateOrders(card);
};

const fastFoodFarsee: Farsee = card => {
  const naturalBingoResult = { naturalBingo: NaturalBingoFlag.Yes, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const fastFood: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee: fastFoodFarsee
};

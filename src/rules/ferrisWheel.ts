import _ from 'lodash';
import {
  Card,
  CardCell,
  CardEffect,
  CellEffect,
  NaturalBingoFlag,
  newPickRandomItems,
  pickRandomItemsWithMutation,
  randomInt,
  RandomSequence
} from '..';
import {CardBingoTester} from '../Card';
import {
  handleDaubByPowerup,
  initializeBoxAndTicketRewardsAndOthers,
  initializeCardSeed,
  initializeCollectionRewardsAkaShadowCard
} from './common';
import {<PERSON><PERSON>aubed<PERSON><PERSON><PERSON>, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

const HAMMER_COUNT = 4;

///// Rule 摩天轮 /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);
  _.forEach(progress.cards, card => {
    initializeCardSeed(card, card.seed);
    //小女孩初始位置
    card.pushEffect(CardEffect.Protagonist, 0, [-1, -1]);
  });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
  initializeBoxAndTicketRewardsAndOthers(host, progress);
  _.forEach(progress.cards, card => {
    const rs = new RandomSequence(card.seed);
    const cells = pickRandomItemsWithMutation(card.getNoDaubCells(), HAMMER_COUNT, rs);
    let total = 0;
    const daubCount: number[] = [];
    for (let i = 0; i < HAMMER_COUNT; i++) {
      const count = randomInt(2, 4, card);
      total += count;
      daubCount.push(count);
    }
    //要涂抹的格子
    const daubCells = newPickRandomItems(card.getNoDaubCells(), total, card).map(c => c.index);
    //将锤子放入棋盘
    cells.forEach((c, i) => {
      c.pushEffect(CellEffect.Hammer, 0, daubCells.splice(0, daubCount[i]));
    });
  });
};


const bingoTester: CardBingoTester = card => {
  const playerTarget = card.findEffectValue(CardEffect.Protagonist);
  return playerTarget === 1;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const hammer = cell.findEffect(CellEffect.Hammer);
  if (hammer) {
    const daubIndexes: number[] = hammer[2];
    let useHammer = false;
    for (const index of daubIndexes) {
      const c = card.cellAtIndex(index);
      if (c.isDaubed) {
        continue;
      }
      useHammer = true;
      progress.forceDaub(card, card.cellAtIndex(index) as CardCell, false, false);
    }
    if (!useHammer) {
      cell.pullAllEffect(CellEffect.Hammer);
    }
  }
  const player = card.findEffect(CardEffect.Protagonist);
  if (!player) {
    throw new Error('something wrong');
  }
  const [x, y] = player[2];
  const moveCell = findNextCell(card, card.cellAtLocation(x, y));
  if (moveCell == null) {
    return;
  }
  if (x === moveCell.x && y === moveCell.y) {
    return;
  }
  const val = moveCell.y === 0 ? 1 : 0;
  card.updateEffectValue(CardEffect.Protagonist, val, [moveCell.x, moveCell.y]);
};

const findNextCell = (card: Card, originCell?: CardCell): CardCell | null => {
  const startCells: CardCell[] = card.getCardsYEdgeCells(4);
  const checkedDict: Record<number, boolean> = {};
  const searchQueue: CardCell[] = startCells.filter(c => c.isDaubed);
  if (searchQueue.length <= 0) {
    return null;
  }
  let deepCells: CardCell = originCell || searchQueue[0];
  let index = 0;
  while (index < searchQueue.length) {
    const start = searchQueue[index];
    const uncheckedAdjacentCells = _.filter(getNextLocation(card, start), c => !checkedDict[c.index]);
    _.forEach(uncheckedAdjacentCells, ac => {
      if (deepCells && deepCells.y === 0) {
        return;
      }
      checkedDict[ac.index] = true;
      if (!ac.isDaubed) return;
      if (deepCells.y > ac.y) {
        deepCells = ac;
      }
      searchQueue.push(ac);
    });
    index += 1;
  }
  return deepCells;
};

const getNextLocation = (card: Card, cell: CardCell): CardCell[] => {
  return _.compact([
    card.cellAtLocation(cell.x - 1, cell.y),
    card.cellAtLocation(cell.x + 1, cell.y),
    card.cellAtLocation(cell.x, cell.y - 1),
    cell,
  ]);
};


const farsee: Farsee = card => {
  const naturalBingoResult = {
    naturalBingo: NaturalBingoFlag.Yes,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const ferrisWheel: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

import _ from 'lodash';
import { Card, CardBingoTester, CardCell, CellEffect, NaturalBingoFlag, pickRandomItemsWithMutation, PlayerRoundProgress, RandomSequence } from '..';
import { handleDaubByPowerup, initializeBoxAndTicketRewardsAndOthers, initializeCollectionRewardsAkaShadowCard } from './common';
import { CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer } from './IBingoRule';

///// Flower Fairy /////
const INITIAL_HONEY_POT_COUNT = 12;
const HONEY_POT_TARGET = 2;

const initializeCardEffect = initializeCollectionRewardsAkaShadowCard;

const initializeCellEffect: RoundInitializer = (preset, progress) => {
  initializeBoxAndTicketRewardsAndOthers(preset, progress);

  _.forEach(progress.cards, card => {
    // Central daub and fairy
    const centerCell = card.cellAtIndex(12);
    centerCell.pushEffect(CellEffect.MarkAsDaubByInitialization);
    progress.forceDaub(card, centerCell);

    centerCell.pushEffect(CellEffect.Fairy, 0);

    // Initialize honey pots
    const rs = new RandomSequence(card.seed);
    const candidates = card.getNoDaubCells();
    const honeyPotCells = pickRandomItemsWithMutation(candidates, _.clamp(INITIAL_HONEY_POT_COUNT, candidates.length), rs);
    _.forEach(honeyPotCells, cell => { cell.pushEffect(CellEffect.HoneyPot, 0); });
  });
};

const bingoTester: CardBingoTester = card => {
  for (let i = 0; i < card.cells.length; i += 1) {
    if (!card.cells[i].isDaubed) return false;
  }
  return true;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);

  // Don't use callback to daub
  // if (effect[0] === CellEffect.MarkAsDaubByFairy) {
  //   progress.forceDaub(card, cell);
  // }
};

/**
 * @returns Collected honey pot count in this operation.
 */
function fairyDaubMany(targets: CardCell[], progress: PlayerRoundProgress, card: Card): number {
  let collectedHoneyPotCount = 0;
  _.forEach(targets, t => {
    t.pushEffect(CellEffect.MarkAsDaubByFairy, targets.length);
    if (t.isDaubed) return; // Prevent daubed cells in 2x2 case
    progress.forceDaub(card, t, true);
    if (t.hasEffect(CellEffect.HoneyPot)) collectedHoneyPotCount += 1;
  });

  return collectedHoneyPotCount;
}

function afterHoneyPotCollected(count: number, progress: PlayerRoundProgress, card: Card, rs: RandomSequence) {
  const centerCell = card.cellAtIndex(12);
  const fairy = _.find(centerCell.effects, ([t]) => t === CellEffect.Fairy);
  if (!fairy) return;

  fairy[1] = fairy[1] + count;

  while (fairy[1] >= HONEY_POT_TARGET) {
    fairy[1] = fairy[1] - HONEY_POT_TARGET;

    const skillChance = rs.nextSingle();

    if (skillChance < 0.33) {
      // #1 Daub 2/3 cells
      const daubCount = pickRandomItemsWithMutation([2, 3], 1, rs)[0];
      const candidates = card.getNoDaubCells();
      const targets = pickRandomItemsWithMutation(candidates, _.clamp(daubCount, candidates.length), rs);
      const collectedHoneyPotCount = fairyDaubMany(targets, progress, card);
      afterHoneyPotCollected(collectedHoneyPotCount, progress, card, rs);
    } else if (skillChance < 0.66) {
      // #2 Daub 2x2 cells
      const noDaubCells = card.getNoDaubCells();
      if (noDaubCells.length === 0) return;

      const origin = pickRandomItemsWithMutation(noDaubCells, 1, rs)[0];
      const targets = [origin];
      const xNeighbor = origin.x < 4 ? origin.x + 1 : origin.x - 1;
      const yNeighbor = origin.y < 4 ? origin.y + 1 : origin.y - 1;

      targets.push(
        card.cellAtLocation(xNeighbor, origin.y) as CardCell,
        card.cellAtLocation(origin.x, yNeighbor) as CardCell,
        card.cellAtLocation(xNeighbor, yNeighbor) as CardCell,
      );

      const collectedHoneyPotCount = fairyDaubMany(targets, progress, card);
      afterHoneyPotCollected(collectedHoneyPotCount, progress, card, rs);
    } else {
      // #3 Generate 2/3 honey pots
      const generateCount = pickRandomItemsWithMutation([2, 3], 1, rs)[0];

      const candidates = _.intersection(card.getNoDaubCells(), card.getNoExclusiveEffectCells([CellEffect.HoneyPot]));
      const targets = pickRandomItemsWithMutation(candidates, _.clamp(generateCount, candidates.length), rs);

      _.forEach(targets, t => progress.pushEffectToCell(card, t, CellEffect.HoneyPot, generateCount));
    }
  }
}

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  if (!cell.hasEffect(CellEffect.HoneyPot)) return;

  const rs = new RandomSequence(card.seed + cell.index);
  afterHoneyPotCollected(1, progress, card, rs);
};

const cloverFarsee: Farsee = card => {
  const naturalBingoResult = { naturalBingo: NaturalBingoFlag.Yes, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const flowerFairy: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee: cloverFarsee
};

import _ from 'lodash';
import {
  Card,
  CardCell,
  CardEffect,
  CellEffect,
  NaturalBingoFlag,
  pickRandomItemsWithMutation,
  RandomSequence
} from '..';
import {CardBingoTester} from '../Card';
import {
  handleDaubByPowerup,
  initializeBoxAndTicketRewardsAndOthers,
  initializeCardSeed,
  initializeCollectionRewardsAkaShadowCard
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

const DRINKS_COUNT = 4;
const DOOR_X = [1, 2, 3];

///// Rule 足球 /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);
  _.forEach(progress.cards, card => {
    initializeCardSeed(card, card.seed);
    //运动员初始位置
    card.pushEffect(CardEffect.FootballPlayer, 0, [-1, -1]);
  });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
  initializeBoxAndTicketRewardsAndOthers(host, progress);
  _.forEach(progress.cards, card => {
    const rs = new RandomSequence(card.seed);
    const cells = pickRandomItemsWithMutation(card.getNoDaubCells(), DRINKS_COUNT, rs);
    //将饮料放入棋盘
    for (const cell of cells) {
      cell.pushEffect(CellEffect.FootballDrink, 1);
    }
  });
};


const bingoTester: CardBingoTester = card => {
  const playerTarget = card.findEffectValue(CardEffect.FootballPlayer);
  return playerTarget === 1;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  if (cell.findEffectValue(CellEffect.FootballDrink)) {
    cell.updateEffectValue(CellEffect.FootballDrink, 0);
    const noDaubCells = card.getNoDaubCells();
    if (noDaubCells.length > 0) {
      const cells = card.pickRandomCellsWithMutation(noDaubCells, 1);
      progress.forceDaub(card, cells[0], false, false);
    }
  }
  const player = card.findEffect(CardEffect.FootballPlayer);
  if (!player) {
    throw new Error('something wrong');
  }
  const [x, y] = player[2];
  const moveCell = findNextCell(card, card.cellAtLocation(x, y));
  if (moveCell == null) {
    return;
  }
  if (x === moveCell.x && y === moveCell.y) {
    return;
  }
  const val = moveCell.y === 0 && DOOR_X.includes(moveCell.x) ? 1 : 0;
  card.updateEffectValue(CardEffect.FootballPlayer, val, [moveCell.x, moveCell.y]);
};

const findNextCell = (card: Card, originCell?: CardCell): CardCell | null => {
  const startCells: CardCell[] = card.getCardsYEdgeCells(4);
  const checkedDict: Record<number, boolean> = {};
  const searchQueue: CardCell[] = startCells.filter(c => c.isDaubed);
  if (searchQueue.length <= 0) {
    return null;
  }
  let deepCells: CardCell = originCell || searchQueue[0];
  let index = 0;
  const checkCenter = function (x: number) {
    return x > 0 && x < 4;
  };
  while (index < searchQueue.length) {
    const start = searchQueue[index];
    const uncheckedAdjacentCells = _.filter(getNextLocation(card, start), c => !checkedDict[c.index]);
    _.forEach(uncheckedAdjacentCells, ac => {
      if (deepCells && deepCells.y === 0 && DOOR_X.includes(deepCells.x)) {
        return;
      }
      checkedDict[ac.index] = true;
      if (!ac.isDaubed) return;
      if (deepCells.y > ac.y || (deepCells.y === ac.y && !checkCenter(deepCells.x) && checkCenter(ac.x))) {
        deepCells = ac;
      }
      searchQueue.push(ac);
    });
    index += 1;
  }
  return deepCells;
};

const getNextLocation = (card: Card, cell: CardCell): CardCell[] => {
  return _.compact([
    card.cellAtLocation(cell.x - 1, cell.y),
    card.cellAtLocation(cell.x + 1, cell.y),
    card.cellAtLocation(cell.x, cell.y - 1),
    cell,
  ]);
};


const farsee: Farsee = card => {
  const naturalBingoResult = {
    naturalBingo: NaturalBingoFlag.Yes,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const football: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

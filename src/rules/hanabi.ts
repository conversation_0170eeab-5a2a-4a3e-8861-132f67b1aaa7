import _ from 'lodash';
import { Card, CardBingoTester, CardCell, CellEffect, EffectTuple, IWeight, NaturalBingoFlag, pickRandomItemsWithMutation, PlayerRoundProgress, randomOneItemByWeights, RandomSequence } from '..';
import { handleDaubByPowerup, initializeBoxAndTicketRewardsAndOthers, initializeCollectionRewardsAkaShadowCard } from './common';
import { CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer } from './IBingoRule';

///// Hanabi /////
export const getChargeCountDictionary: () => Record<number, number> = () => ({
  [CellEffect.DaubOneRow]: 3,
  [CellEffect.DaubOneColumn]: 3,
  [CellEffect.Daub3x3]: 4,
  [CellEffect.RandomDaubOne]: 2,
  [CellEffect.RandomDaubTwo]: 3,
});

export const getEffectOrder: () => Record<number, number> = () => ({
  [CellEffect.DaubOneRow]: 0,
  [CellEffect.DaubOneColumn]: 1,
  [CellEffect.Daub3x3]: 2,
  [CellEffect.RandomDaubOne]: 3,
  [CellEffect.RandomDaubTwo]: 4,
});

const TotalEffectCount = 12;

const getEffectWeights: () => ({ type: CellEffect } & IWeight)[] = () => [
  { type: CellEffect.DaubOneRow, weight: 1 },
  { type: CellEffect.DaubOneColumn, weight: 1 },
  { type: CellEffect.Daub3x3, weight: 1 },
  { type: CellEffect.RandomDaubOne, weight: 1 },
  { type: CellEffect.RandomDaubTwo, weight: 1 },
];

const initializeCardEffect = initializeCollectionRewardsAkaShadowCard;

const initializeCellEffect: RoundInitializer = (preset, progress) => {
  initializeBoxAndTicketRewardsAndOthers(preset, progress);

  const weights = getEffectWeights();
  _.forEach(progress.cards, card => {
    const rs = new RandomSequence(card.seed);
    const cellsToPutHanabi = pickRandomItemsWithMutation(card.getNoDaubCells(), TotalEffectCount, rs);
    _.forEach(cellsToPutHanabi, cell => {
      const { type: effect } = randomOneItemByWeights(weights, rs);
      cell.pushEffect(effect);
    });
  });
};

const bingoTester: CardBingoTester = card => {
  for (let i = 0; i < card.cells.length; i += 1) {
    if (!card.cells[i].isDaubed) return false;
  }
  return true;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};

const getHanabiDaub: () => Record<number, (effectCell: CardCell, card: Card, progress: PlayerRoundProgress) => void> = () => ({
  [CellEffect.DaubOneRow]: (cell, card, progress) => {
    const targetCells = [
      card.cellAtLocation(0, cell.y),
      card.cellAtLocation(1, cell.y),
      card.cellAtLocation(2, cell.y),
      card.cellAtLocation(3, cell.y),
      card.cellAtLocation(4, cell.y),
    ];
    _.forEach(targetCells, c => {
      c?.pushEffect(CellEffect.Tag, cell.index);
      progress.forceDaub(card, c as CardCell, true);
    });
  },
  [CellEffect.DaubOneColumn]: (cell, card, progress) => {
    const targetCells = [
      card.cellAtLocation(cell.x, 0),
      card.cellAtLocation(cell.x, 1),
      card.cellAtLocation(cell.x, 2),
      card.cellAtLocation(cell.x, 3),
      card.cellAtLocation(cell.x, 4),
    ];
    _.forEach(targetCells, c => {
      c?.pushEffect(CellEffect.Tag, cell.index);
      progress.forceDaub(card, c as CardCell, true);
    });
  },
  [CellEffect.Daub3x3]: (cell, card, progress) => {
    const targetCells = [
      card.cellAtLocation(cell.x - 1, cell.y - 1),
      card.cellAtLocation(cell.x, cell.y - 1),
      card.cellAtLocation(cell.x + 1, cell.y - 1),
      card.cellAtLocation(cell.x - 1, cell.y),
      card.cellAtLocation(cell.x, cell.y),
      card.cellAtLocation(cell.x + 1, cell.y),
      card.cellAtLocation(cell.x - 1, cell.y + 1),
      card.cellAtLocation(cell.x, cell.y + 1),
      card.cellAtLocation(cell.x + 1, cell.y + 1),
    ];
    _(targetCells).filter(c => c !== undefined).forEach(c => {
      c?.pushEffect(CellEffect.Tag, cell.index);
      progress.forceDaub(card, c as CardCell, true);
    });
  },
  [CellEffect.RandomDaubOne]: (cell, card, progress) => {
    const candidates = card.getNoDaubCells();
    const rs = new RandomSequence(card.seed + cell.index);
    const targetCells = pickRandomItemsWithMutation(candidates, _.clamp(1, candidates.length), rs);
    _.forEach(targetCells, c => {
      c?.pushEffect(CellEffect.Tag, cell.index);
      progress.forceDaub(card, c, true);
    });
  },
  [CellEffect.RandomDaubTwo]: (cell, card, progress) => {
    const candidates = card.getNoDaubCells();
    const rs = new RandomSequence(card.seed + cell.index);
    const targetCells = pickRandomItemsWithMutation(candidates, _.clamp(2, candidates.length), rs);
    _.forEach(targetCells, c => {
      c?.pushEffect(CellEffect.Tag, cell.index);
      progress.forceDaub(card, c, true);
    });
  }
});

const afterCellDaub: CellDaubedHandler = (progress, card, daubedCell) => {
  // Update all charge counter
  const chargeCountDictionary = getChargeCountDictionary();
  const effectOrder = getEffectOrder();
  const hanabiDaub = getHanabiDaub();

  const availableCounters: [cellIndex: number, effect: EffectTuple<CellEffect>][] = [];
  _.forEach(card.cells, cell => {
    if (!cell.isDaubed || cell.index === daubedCell.index) return;

    const targetTuple = _.find(cell.effects, ([t, v]) => chargeCountDictionary[t] && v < chargeCountDictionary[t]);
    if (!targetTuple) return;
    availableCounters.push([cell.index, targetTuple as EffectTuple<CellEffect>]);
  });

  _.forEach(availableCounters, ([, c]) => c[1] = c[1] + 1);

  const finishedCountersAfterThisDaub = _.filter(availableCounters, ([, [t, v]]) => v >= chargeCountDictionary[t]);
  const orderedCounters = _.orderBy(finishedCountersAfterThisDaub, ([, [t]]) => effectOrder[t]);

  _.forEach(orderedCounters, ([cellIndex, [effectType]]) => {
    hanabiDaub[effectType](card.cellAtIndex(cellIndex), card, progress);
  });
};

const farsee: Farsee = card => {
  const naturalBingoResult = { naturalBingo: NaturalBingoFlag.Yes, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const hanabi: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee: farsee,
};

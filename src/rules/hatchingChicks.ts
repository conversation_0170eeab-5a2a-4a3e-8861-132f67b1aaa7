import _ from 'lodash';
import {
  Card<PERSON>ell,
  CardEffect,
  CellEffect,
  NaturalBingoFlag,
  randomFloat,
} from '..';
import {CardBingoTester} from '../Card';
import {
  getCounterTarget,
  handleDaubByPowerup,
  initializeBoxAndTicketRewardsAndOthers,
  initializeCardSeed,
  initializeCollectionRewardsAkaShadowCard, initializeCounter, readCounter, updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

const SUN_PROB = 0.1; //太阳概率
const DRYER_PROB = 0.2; //烘干机概率
const MAX_DRYER_COUNT = 6; //最大烘干机数量
const MAX_SUN_COUNT = 2; //最大太阳数量
const HATCHING_STATE = 2; //孵化状态
const EGG_TARGET = 10; //最终目标

///// Rule 孵化小鸡 /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);
  _.forEach(progress.cards, card => {
    initializeCardSeed(card, card.seed);
    initializeCounter(card, EGG_TARGET);
    //鸡蛋初始位置
    card.pushEffect(CardEffect.ChicksEgg, 0, [0, 0, 0, 0, 0]);
  });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
  initializeBoxAndTicketRewardsAndOthers(host, progress);
};


const bingoTester: CardBingoTester = card => {
  return readCounter(card) >= getCounterTarget(card);
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const r = randomFloat(0, 1, card);
  let hatchCount = 0;
  const currentEggState: number[] = card.findCustomData(CardEffect.ChicksEgg) as number[];
  if (r < SUN_PROB) {
    const sunCount = _.filter(card.cells, cell => cell.hasEffect(CellEffect.ChicksSun)).length;
    if (sunCount < MAX_SUN_COUNT) {
      cell.pushEffect(CellEffect.ChicksSun);
      _.forEach(currentEggState, (v, i) => {
        currentEggState[i]++;
        if (currentEggState[i] >= HATCHING_STATE) {
          currentEggState[i] = 0;
          hatchCount++;
        }
      });
    }
  } else if (r < SUN_PROB + DRYER_PROB) {
    const dryerCount = _.filter(card.cells, cell => cell.hasEffect(CellEffect.ChicksDryer)).length;
    if (dryerCount < MAX_DRYER_COUNT) {
      cell.pushEffect(CellEffect.ChicksDryer);
      //本列以及相邻的两列增加进度
      const offset = [-1, 0, 1];
      for (const v of offset) {
        const index = cell.x + v;
        if (index >= 0 && index < CardCell.cellStepCount) {
          currentEggState[index]++;
          if (currentEggState[index] >= HATCHING_STATE) {
            currentEggState[index] = 0;
            hatchCount++;
          }
        }
      }
    }
  }
  if (hatchCount > 0) {
    updateCounter(card, readCounter(card) + hatchCount);
  }
};

const farsee: Farsee = card => {
  const naturalBingoResult = {
    naturalBingo: NaturalBingoFlag.Yes,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const hatchingChicks: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

import {
    CellEffect,
    NaturalBingoFlag,
    randomInt,
    CardCell,
    Card, CardEffect
} from '..';
import {CardBingoTester} from '../Card';
import {
    getCounterTarget,
    handleDaubByPowerup,
    initializeBoxAndTicketRewardsAndOthers,
    initializeCardSeed,
    initializeCollectionRewardsAkaShadowCard,
    initializeCounter,
    readCounter,
    updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';
import _ from "lodash";

const COLLECT_TARGET = 60;//收集蜂蜜
const HONEY_NUM = 3;//蜂蜜收集的最大数量
const HONEY_ADD_TARGET = 10;//一个蜂蜜加目标10
const NO_BEE_DESTINATION = -1;//没有目的
const EXTRA_BEE_DESTINATION = -2;//卡片外

///// Rule honey harvest /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
    initializeCollectionRewardsAkaShadowCard(preset, progress);
    progress.cards.forEach(card => {
        initializeCardSeed(card, card.seed);
        initializeCounter(card, COLLECT_TARGET);
        initializeHoney(card, HONEY_ADD_TARGET);
    });
};
const initializeHoney = (card: Card, num: number) => {
    card.pushEffect(CardEffect.Honey, num);
};
const initializeCellEffect: RoundInitializer = (host, progress) => {
    initializeBoxAndTicketRewardsAndOthers(host, progress);
    progress.cards.forEach(card => {
        const noDaubCells = card.getNoDaubCells();
        _.forEach(noDaubCells, cell => {
            const beeNum = randomInt(1, 2, card);
            cell.pushEffect(CellEffect.bee, 0, {
                beeNum,//格子蜜蜂数量
                beeDestination: [NO_BEE_DESTINATION, NO_BEE_DESTINATION],//-1表示没有蜜蜂，-2表示蜜蜂飞在card外面(为涂抹的格子都满了// )
            });
            cell.pushEffect(CellEffect.honey, 0, {
                honeyNum: 0 //格子收集蜂蜜数量
            });
        });
    });
};


const bingoTester: CardBingoTester = card => {
    if (readCounter(card) >= getCounterTarget(card)) return true;
    return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
    handleDaubByPowerup(progress, card, cell, effect);
};

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
    const beeEffect = cell.findEffect(CellEffect.bee);
    const honeyEffect = cell.findEffect(CellEffect.honey);
    const beeNum = beeEffect ? beeEffect[2].beeNum : 0;
    const noDaubCells = card.getNoDaubCells();
    for (let i = 0; i < beeNum; i++) {
        //遍历还可以加蜜蜂(蜂蜜)的格子
        const canFlyCardCell = findMatchEffectHoney(noDaubCells);
        const flyIndex = canFlyCardCell.length > 0 ? randomInt(0, canFlyCardCell.length - 1, card) : -1;
        if (beeEffect) {
            if (canFlyCardCell.length <= 0) {
                beeEffect[2].beeDestination[i] = EXTRA_BEE_DESTINATION;
                break;
            } else {
                beeEffect[2].beeDestination[i] = canFlyCardCell[flyIndex].index;
            }
        }
        const flyCell = canFlyCardCell[flyIndex].findEffect(CellEffect.honey);
        if (flyCell) {
            flyCell[2].honeyNum++;
        }
    }
    if (honeyEffect && honeyEffect[2].honeyNum) {
        for (let i = 0; i < honeyEffect[2].honeyNum; i++) {
            updateCounter(card, readCounter(card) + HONEY_ADD_TARGET);
        }
    }
};
const findMatchEffectHoney = function (noDaubCells: CardCell[]) {
    const canFlyCardCell: CardCell[] = [];
    _.forEach(noDaubCells, item => {
        const beeEffect = item.findEffect(CellEffect.honey);
        if (beeEffect && beeEffect[2].honeyNum < HONEY_NUM) {
            canFlyCardCell.push(item);
        }
    });
    return canFlyCardCell;
};
const farsee: Farsee = card => {
    const naturalBingoResult = {
        naturalBingo: NaturalBingoFlag.Yes,
        daubOnceTarget: -1,
        daubTwiceTarget: [-1, -1] as [number, number]
    };

    if (card.canBingo) return naturalBingoResult;

    const onceTarget: number | null = null;
    const twiceTarget: [number, number] | null = null;

    return {
        naturalBingo: NaturalBingoFlag.No,
        daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
        daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
    };
};

export const honeyHarvest: IBingoRule = {
    initializeCardEffect,
    initializeCellEffect,
    bingoTester,
    beforeCellMeetEffect,
    afterCellDaub,
    farsee
};

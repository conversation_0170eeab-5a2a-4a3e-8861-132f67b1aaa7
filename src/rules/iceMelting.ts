import _ from 'lodash';
import {
    Card,
    CardCell,
    CellEffect,
    NaturalBingoFlag,
} from '..';
import {CardBingoTester} from '../Card';
import {
    getCounterTarget,
    handleDaubByPowerup,
    initializeBoxAndTicketRewardsAndOthers, initializeCardSeed,
    initializeCollectionRewardsAkaShadowCard,
    initializeCounter,
    readCounter, updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

const ICE_TARGET = 25;//消除冰块的总个数
enum IceState { A, B, C, D}//冰块状态 a b c d:被消融

///// Rule 冰雪消融 /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
    initializeCollectionRewardsAkaShadowCard(preset, progress);
    _.forEach(progress.cards, card => {
        initializeCardSeed(card, card.seed);
        initializeCounter(card, ICE_TARGET);
    });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
    initializeBoxAndTicketRewardsAndOthers(host, progress);

    _.forEach(progress.cards, card => {
        const noDaubCells = card.getNoDaubCells();
        _.forEach(noDaubCells, cell => {
            //isWaterMelting:是否被水滴消融
            cell.pushEffect(CellEffect.Ice, IceState.A, {
                isWaterMelting: false,
                waterIndex: [-1, -1, -1, -1],//水滴去向index
            });
        });
    });
};


const bingoTester: CardBingoTester = card => {
    const isDaubed = card.cells.filter(v => !v.isDaubed).length;
    return !isDaubed;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
    handleDaubByPowerup(progress, card, cell, effect);
};
//寻找上下左右符合要求的cell
const findCell = (card: Card, x: number, y: number, addX: number, addY: number): CardCell | undefined => {
    if (x < 0 || x > 4 || y < 0 || y > 4 || x + addX < 0 || x + addX > 4 || y + addY < 0 || y + addY > 4)
        return undefined;
    const cell = card.cellAtLocation(x + addX, y + addY);
    if (cell) {
        const effect = cell.findEffect(CellEffect.Ice);
        if (effect && effect[1] != IceState.D) {
            return cell;
        } else {
            return findCell(card, x + addX, y + addY, addX, addY);
        }
    } else {
        return findCell(card, x + addX, y + addY, addX, addY);
    }
};

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
    const noDaubCells = card.getNoDaubCells();
    if (!noDaubCells || noDaubCells.length <= 0) {
        return;
    }
    const effect = cell.findEffect(CellEffect.Ice);
    if (effect && (effect[1] != IceState.D || (effect[1] === IceState.D && effect[2].isWaterMelting))) {
        cell.updateEffectValue(CellEffect.Ice, IceState.D);
        const cellsOld = [findCell(card, cell.x, cell.y, -1, 0),//左
            findCell(card, cell.x, cell.y, 1, 0),//右
            findCell(card, cell.x, cell.y, 0, 1),//上
            findCell(card, cell.x, cell.y, 0, -1)//下];
        ];
        for (let i = 0; i < cellsOld.length; i++) {
            if (cellsOld[i]) {
                effect[2].waterIndex[i] = cellsOld[i]?.index;
            }
        }
        const cells = _.compact(cellsOld);
        _.forEach(cells, (cellItem) => {
            if (cellItem) {
                const effect = cellItem.findEffect(CellEffect.Ice);
                if (effect && effect[1] < IceState.D) {
                    effect[1] += 1;
                    if (effect[1] === IceState.D) {
                        effect[2].isWaterMelting = true;
                    }
                    cellItem.updateEffectValue(CellEffect.Ice, effect[1], effect[2]);
                    if (effect && effect[1] === IceState.D) {
                        progress.forceDaub(card, cellItem, false, false);
                    }
                } else {
                    //console.warn("IceState.C", JSON.stringify(cells), JSON.stringify(cell));
                    //throw new Error('something wrong');
                }

            }
        });
    }
};

const farsee: Farsee = card => {
    const naturalBingoResult = {
        naturalBingo: NaturalBingoFlag.Yes,
        daubOnceTarget: -1,
        daubTwiceTarget: [-1, -1] as [number, number]
    };

    if (card.canBingo) return naturalBingoResult;

    const onceTarget: number | null = null;
    const twiceTarget: [number, number] | null = null;

    return {
        naturalBingo: NaturalBingoFlag.No,
        daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
        daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
    };
};
export const iceMelting: IBingoRule = {
    initializeCardEffect,
    initializeCellEffect,
    bingoTester,
    beforeCellMeetEffect,
    afterCellDaub,
    farsee
};
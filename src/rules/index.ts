import {IBingoRule} from './IBingoRule';
import {common} from './common';
import {bomberman} from './bomberman';
import {easterEgg} from './easterEgg';
import {bubbleBurst} from './bubbleBurst';
import {cupid} from './cupid';
import {bamboo} from './bamboo';
import {clover} from './clover';
import {flowerFairy} from './flowerFairy';
import {hanabi} from './hanabi';
import {fastFood} from './fastFood';
import {tileMatching} from './tileMatching';
import {penguin} from './penguin';
import {buildingBlocks} from './buildingBlocks';
import {royalFlush} from './royalFlush';
import {kraken} from './kraken';
import {piggy} from './piggy';
import {race} from './race';
import {shark} from './shark';
import {candyCrush} from './candyCrush';
import {watermelon} from './watermelon';
import {zuma} from './zuma';
import {cat} from './cat';
import {pumpkin} from './pumpkin';
import {rubber} from './rubber';
import {treasureRaider} from './treasureRaider';
import {christmasGifts} from './christmasGifts';
import {discoForever} from './discoForever';
import {luckyClover} from './luckyClover';
import {valentineDay} from './valentineDay';
import {football} from './football';
import {ski} from './ski';
import {clown} from './clown';
import {ferrisWheel} from './ferrisWheel';
import {newYear} from './newYear';
import {suppliesWinter} from "./suppliesWinter";
import {iceMelting} from "./iceMelting";
import {bartender} from "./bartender";
import {honeyHarvest} from "./honeyHarvest";
import {collectStar} from "./starCollect";
import {treasurePot} from "./treasurePot";
import {spaceWarfare} from "./spaceWarfare";
import {spiderLair} from "./spiderLair";
import {moonlightLake} from "./moonlightLake";
import {beautyHouse} from "./beautyHouse";
import {hatchingChicks} from './hatchingChicks';
export function getRuleById(ruleId: number): IBingoRule {
    switch (ruleId) {
        case 101:
            return common;

        case 201:
            return bomberman;

        case 202:
            return easterEgg;

        case 203:
            return bubbleBurst;

        case 204:
            return cupid;

        case 205:
            return bamboo;

        case 206:
            return clover;

        case 207:
            return flowerFairy;

        case 208:
            return hanabi;

        case 209:
            return fastFood;

        case 210:
            return tileMatching;

        case 211:
            return penguin;

        case 212:
            return buildingBlocks;

        case 213:
            return royalFlush;

        case 214:
            return kraken;

        case 215:
            return piggy;

        case 216:
            return race;

        case 217:
            return shark;

        case 218:
            return candyCrush;

        case 219:
            return watermelon;

        case 220:
            return cat;

        case 221:
            return rubber;

        case 222:
            return treasureRaider;

        case 223:
            return discoForever;

        case 224:
            return pumpkin;

        case 225:
            return zuma;

        case 226:
            return luckyClover;

        case 227:
            return christmasGifts;

        case 228:
            return newYear;

        case 231:
            return valentineDay;

        case 500:
            return football;

        case 501:
            return ski;

        case 502:
            return clown;

        case 503:
            return ferrisWheel;

        case 229:
            return suppliesWinter;

        case 230:
            return iceMelting;

        case 504:
            return bartender;

        case 232:
            return honeyHarvest;

        case 233:
            return collectStar;

        case 234:
            return treasurePot;

        case 235:
            return spaceWarfare;
        case 505:
            return spiderLair;
        case 506:
            return moonlightLake;
        case 507:
            return beautyHouse;
        case 508:
            return hatchingChicks;
        default:
            throw new Error('Invalid rule id:' + ruleId);
    }
}

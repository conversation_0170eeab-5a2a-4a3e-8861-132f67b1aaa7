import _ from 'lodash';
import { Card, CardBingoTester, CardEffect, CellEffect, EffectTuple, NaturalBingoFlag, pickRandomItemsWithMutation, RandomSequence } from '..';
import { handleDaubByPowerup, initializeBoxAndTicketRewardsAndOthers, initializeCollectionRewardsAkaShadowCard, initializeCounter, readCounter, updateCounter } from './common';
import { CellDaubed<PERSON>andler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer } from './IBingoRule';

///// Kraken /////
export const Constant = Object.freeze({
  MaxHp: 20,
  CannonCellIndexes: [6, 8, 16, 18]
});

const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);

  _.forEach(progress.cards, card => {
    initializeCounter(card, Constant.MaxHp);
  });
};


const initializeCellEffect: RoundInitializer = (preset, progress) => {
  _.forEach(progress.cards, card => {
    _.forEach(Constant.CannonCellIndexes, i => {
      const cannonCell = card.cellAtIndex(i);
      cannonCell.pushEffect(CellEffect.MarkAsDaubByInitialization);
      card.markCellAsDaubed(cannonCell.index);
    });
  });

  initializeBoxAndTicketRewardsAndOthers(preset, progress);
};

const bingoTester: CardBingoTester = card => {
  if (card.findEffectValue(CardEffect.Counter) as number >= (card.findEffectValue(CardEffect.CounterTarget) as number)) return true;
  return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const adjacentCellIndexes = _.map(card.getEdgeAndVertexAdjacentCells(cell), c => c.index);
  const fireCount = _.intersection(Constant.CannonCellIndexes, adjacentCellIndexes).length;
  updateCounter(card, readCounter(card) + fireCount);
};

const farsee: Farsee = card => {
  const naturalBingoResult = { naturalBingo: NaturalBingoFlag.Yes, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const kraken: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

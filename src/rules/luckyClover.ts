import {CellEffect, NaturalBingoFlag, pickRandomItemsWithMutation, RandomSequence} from '..';
import {CardBingoTester} from '../Card';
import {
  getCounterTarget,
  handleDaubByPowerup,
  initializeBoxAndTicketRewardsAndOthers,
  initializeCardSeed,
  initializeCollectionRewardsAkaShadowCard,
  initializeCounter,
  readCounter,
  updateCounter
} from './common';
import {<PERSON>DaubedH<PERSON>ler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

const COLLECT_TARGET = 15;//收集总个数
const SINGE_CLOVER_COUNT = 4;//单四叶草个数
const DOUBLE_CLOVER_COUNT = 3;//两个四叶草个数
const THIRD_CLOVER_COUNT = 2;//三个四叶草个数
const HAT_CLOVER_COUNT = 1;//帽子个数
const GOLD_SET_RATE = [0.3, 0.35, 1, 1]; //不同卡数金叶子放置几率

///// Rule lucky clover /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);
  progress.cards.forEach(card => {
    initializeCardSeed(card, card.seed);
    initializeCounter(card, COLLECT_TARGET);
  });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
  initializeBoxAndTicketRewardsAndOthers(host, progress);
  const goldSetRate = GOLD_SET_RATE[progress.cards.length - 1];
  progress.cards.forEach(card => {
    const total = SINGE_CLOVER_COUNT + DOUBLE_CLOVER_COUNT + THIRD_CLOVER_COUNT + HAT_CLOVER_COUNT;
    const noDaubCells = card.getNoDaubCells();
    const cells = card.pickRandomCellsWithMutation(noDaubCells, total);
    let idx = 0;
    for (let i = 0; i < SINGE_CLOVER_COUNT; i++) {
      cells[idx++].pushEffect(CellEffect.SingleLuckyClover, 0, {count: 1});
    }
    for (let i = 0; i < DOUBLE_CLOVER_COUNT; i++) {
      cells[idx++].pushEffect(CellEffect.DoubleLuckyClover, 0, {count: 2});
    }
    for (let i = 0; i < THIRD_CLOVER_COUNT; i++) {
      cells[idx++].pushEffect(CellEffect.ThirdLuckyClover, 0, {count: 3});
    }
    for (let i = 0; i < HAT_CLOVER_COUNT; i++) {
      cells[idx++].pushEffect(CellEffect.LuckyCloverHat, 0, {count: 7});
    }
    //金四叶草位置 不影响bingo结果
    if (Math.random() < goldSetRate) {
      const goldCell = pickRandomItemsWithMutation(noDaubCells, 1)[0];
      goldCell.pushEffect(CellEffect.GoldLuckyClover, 0);
    }
  });
};


const bingoTester: CardBingoTester = card => {
  if (readCounter(card) >= getCounterTarget(card)) return true;
  return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const targetEffect = [CellEffect.SingleLuckyClover, CellEffect.DoubleLuckyClover, CellEffect.ThirdLuckyClover, CellEffect.LuckyCloverHat];
  let addCount = 0;
  cell.effects.forEach(([type, , custom]) => {
    if (targetEffect.includes(type)) {
      addCount += custom.count;
      cell.updateEffectValue(type, 1);
      if (type === CellEffect.LuckyCloverHat) {
        const nextHatCell = card.pickRandomCellsWithMutation(card.getNoDaubCells(), 1)[0];
        nextHatCell.pushEffect(CellEffect.LuckyCloverHat, 0, custom);
      }
    }
  });
  if (addCount > 0) {
    updateCounter(card, readCounter(card) + addCount);
  }
};

const farsee: Farsee = card => {
  const naturalBingoResult = {
    naturalBingo: NaturalBingoFlag.Yes,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const luckyClover: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee
};

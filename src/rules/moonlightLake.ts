import {
    Card,
    CellEffect, getRandomArray,
    NaturalBingoFlag,
} from '..';
import {CardBingoTester} from '../Card';
import {
    handleDaubByPowerup,
    initializeBoxAndTicketRewardsAndOthers,
    initializeCardSeed,
    initializeCollectionRewardsAkaShadowCard,
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';
import _ from "lodash";

import {logger} from '../CoreUtils2';

const POWER_NUM = 6;//
const moonLocation = [[0, 0], [4, 0], [0, 4], [4, 4]];
///// moonlightLake /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
    initializeCollectionRewardsAkaShadowCard(preset, progress);
    progress.cards.forEach(card => {
        initializeCardSeed(card, card.seed);
    });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
    initializeBoxAndTicketRewardsAndOthers(host, progress);
    progress.cards.forEach(card => {
        for (let i = 0; i < 4; i++) {
            const cell = card.cellAtLocation(moonLocation[i][0], moonLocation[i][1]);//左上角
            if (cell) {
                cell.pushEffect(CellEffect.Moon, 0, {type: i});//标记是月亮位置 type位置类型 value 表示充能情况
                cell.pushEffect(CellEffect.MarkAsDaubByInitialization);
                cell.isDaubed = true;
            }
        }
        const noDaubCells = card.getNoDaubCells();
        _.forEach(noDaubCells, cell => {
            const moonGroups = getMoonGroups(cell.x, cell.y);
            cell.pushEffect(CellEffect.moonAddPower, 1, {type: moonGroups});//如果涂抹将给那个月亮充能 value 表示充能数
        });
    });
};


const bingoTester: CardBingoTester = card => {
    return !card.cells.find(v => !v.isDaubed);
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
    handleDaubByPowerup(progress, card, cell, effect);
};
const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
    const addPowerEffect = cell.findEffect(CellEffect.moonAddPower);
    if (addPowerEffect) {
        const groups = addPowerEffect[2].type;
        _.forEach(groups, groupIndex => {
            const moonCell = card.cellAtLocation(moonLocation[groupIndex][0], moonLocation[groupIndex][1]);
            const moonEffect = moonCell?.findEffect(CellEffect.Moon);
            if (moonEffect && moonEffect[1] < POWER_NUM) {
                moonEffect[1] += addPowerEffect[1];
                if (moonEffect[1] >= POWER_NUM) {
                    for (let i = 0; i < 3; i++) {//随机涂抹3个
                        const noDaubCells = card.getNoDaubCells();
                        if (noDaubCells.length > 0) {
                            const cellIndexList: number[] = [];
                            _.forEach(noDaubCells, noDaubCell => {
                                cellIndexList.push(noDaubCell.index);
                            });
                            const randomIndexes = getRandomArray(cellIndexList, 1, card);//随机几个格子
                            if (card.index === 2) {
                                logger.warn("moonlightLakeCp2", {randomIndexes, cellIndexList});
                            }
                            progress.forceDaub(card, card.cells[randomIndexes[0]], false, false);
                        }
                    }
                }
            }
        });
    }
};
const farsee: Farsee = card => {
    const naturalBingoResult = {
        naturalBingo: NaturalBingoFlag.Yes,
        daubOnceTarget: -1,
        daubTwiceTarget: [-1, -1] as [number, number]
    };

    if (card.canBingo) return naturalBingoResult;

    const onceTarget: number | null = null;
    const twiceTarget: [number, number] | null = null;

    return {
        naturalBingo: NaturalBingoFlag.No,
        daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
        daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
    };
};
const getMoonGroups = (x: number, y: number) => {
    if (x == 2 && y == 2) {
        return [0, 1, 2, 3];
    } else if (x >= 0 && x < 3 && y >= 0 && y < 3) {
        if (x == 2) {
            return [0, 1];
        } else if (y == 2) {
            return [0, 2];
        }
        return [0];
    } else if (x >= 3 && x < 5 && y >= 0 && y < 3) {
        if (y == 2) {
            return [1, 3];
        }
        return [1];
    } else if (x >= 0 && x < 3 && y >= 3 && y < 5) {
        if (x == 2) {
            return [2, 3];
        }
        return [2];
    } else if (x >= 3 && x < 5 && y >= 3 && y < 5) {
        return [3];
    }
};
export const moonlightLake: IBingoRule = {
    initializeCardEffect,
    initializeCellEffect,
    bingoTester,
    beforeCellMeetEffect,
    afterCellDaub,
    farsee
};

import {Card, <PERSON><PERSON>ell, CellEffect, NaturalBingoFlag, randomFloat, weightRandom} from '..';
import {CardBingoTester} from '../Card';
import {
  handleDaubByPowerup,
  initializeBoxAndTicketRewardsAndOthers,
  initializeCardSeed,
  initializeCollectionRewardsAkaShadowCard
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

const BINGO_TARGET = 25;

enum LanternState {
  Close,
  Open
}

///// Rule New Year /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);
  progress.cards.forEach(card => {
    initializeCardSeed(card, card.seed);
  });
};
const Lines = [
  [0, 5, 10, 15, 20],
  [1, 6, 11, 16, 21],
  [2, 7, 12, 17, 22],
  [3, 8, 13, 18, 23],
  [4, 9, 14, 19, 24],
  [0, 1, 2, 3, 4],
  [5, 6, 7, 8, 9],
  [10, 11, 12, 13, 14],
  [15, 16, 17, 18, 19],
  [20, 21, 22, 23, 24],
  [2, 6, 10],
  [3, 7, 11, 15],
  [4, 8, 12, 16, 20],
  [9, 13, 17, 21],
  [14, 18, 22],
  [10, 16, 22],
  [5, 11, 17, 23],
  [0, 6, 12, 18, 24],
  [1, 7, 13, 19],
  [2, 8, 14],
];


const initializeCellEffect: RoundInitializer = (host, progress) => {
  initializeBoxAndTicketRewardsAndOthers(host, progress);
  progress.cards.forEach(card => {
    for (const cell of card.cells) {
      let custom = null;
      let state = LanternState.Close;
      const weight = new Array(25).fill(1);
      if (cell.index === 7 || cell.index === 17) {
        state = LanternState.Open;
        weight[cell.index] = 0;
      }
      if (randomFloat(0, 1, card) < 0.15) {
        const index = weightRandom(weight, card);
        weight[index] = 0;
        custom = {index: index};
      }
      cell.pushEffect(CellEffect.Lantern, state, custom);
    }
  });
};

const bingoTester: CardBingoTester = card => card.cells.filter(v => v.findEffectValue(CellEffect.Lantern) === LanternState.Open).length >= BINGO_TARGET;

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => handleDaubByPowerup(progress, card, cell, effect);


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const effect = cell.findEffect(CellEffect.Lantern);
  if (!effect) {
    return;
  }
  if (effect[2]) {
    const index = effect[2].index;
    progress.forceDaub(card, card.cellAtIndex(index));
  }
  checkLight(card, cell);
};

const checkLight = (card: Card, cell: CardCell) => {
  cell.updateEffectValue(CellEffect.Lantern, LanternState.Open);
  for (const line of Lines) {
    const pos = line.indexOf(cell.index);
    if (pos !== -1) {
      let isLight = false;
      for (let i = 0; i < pos; i++) {
        if (card.cellAtIndex(line[i]).findEffectValue(CellEffect.Lantern) === LanternState.Open) {
          isLight = true;
        } else if (isLight) {
          card.cellAtIndex(line[i]).updateEffectValue(CellEffect.Lantern, LanternState.Open);
        }
      }
      isLight = false;
      for (let i = line.length - 1; i > pos; i--) {
        if (card.cellAtIndex(line[i]).findEffectValue(CellEffect.Lantern) === LanternState.Open) {
          isLight = true;
        } else if (isLight) {
          card.cellAtIndex(line[i]).updateEffectValue(CellEffect.Lantern, LanternState.Open);
        }
      }
    }
  }
};


const farsee: Farsee = card => {
  const naturalBingoResult = {
    naturalBingo: NaturalBingoFlag.Yes,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};
export const newYear: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

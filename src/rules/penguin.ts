import _ from 'lodash';
import { CardBingoTester, CardCell, CellEffect, findSameCellRegion, NaturalBingoFlag, pickRandomItemsWithMutation, RandomSequence } from '..';
import { getCounterTarget, handleDaubByPowerup, initializeBoxAndTicketRewardsAndOthers, initializeCenterDaub, initializeCollectionRewardsAkaShadowCard, initializeCounter, readCounter, updateCounter } from './common';
import { CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer } from './IBingoRule';

///// Rule /////
export const Constant = Object.freeze({
  FishGoal: 8,
  TotalFishCount: 13,
});

export enum FishState { Alive = 1000, Hunted = 1001 }

const movePenguin = (from: CardCell, to: CardCell) => {
  from.pullAllEffect(CellEffect.Penguin);
  to.pushEffect(CellEffect.Penguin);
};

const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);

  _.forEach(progress.cards, card => {
    initializeCounter(card, Constant.FishGoal);
  });
};


const initializeCellEffect: RoundInitializer = (preset, progress) => {
  initializeCenterDaub(preset, progress);
  initializeBoxAndTicketRewardsAndOthers(preset, progress);

  _.forEach(progress.cards, card => {
    const centerCell = card.cellAtIndex(12);
    centerCell.pushEffect(CellEffect.Penguin);

    const rs = new RandomSequence(card.seed);
    const fishCells = pickRandomItemsWithMutation(card.getNoDaubCells(), Constant.TotalFishCount, rs);
    _.forEach(fishCells, c => {
      c.pushEffect(CellEffect.Tag, FishState.Alive);
    });
  });
};

const bingoTester: CardBingoTester = card => {
  if (readCounter(card) >= getCounterTarget(card)) return true;
  return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const penguinCell = _.find(card.cells, c => c.hasEffect(CellEffect.Penguin));
  if (!penguinCell) throw new Error('No available penguin cell.');
  const penguinAvailableRegion = findSameCellRegion(penguinCell, card, c => c.isDaubed);

  const fishAliveCells = _(penguinAvailableRegion)
    .filter(c => c.hasEffect(CellEffect.Tag, FishState.Alive))
    .sortBy(c => c.x + c.y * 10) // row first ordering
    .value();

  const huntedFishCount = fishAliveCells.length;
  updateCounter(card, readCounter(card) + huntedFishCount);
  _.forEach(fishAliveCells, c => c.updateEffectValue(CellEffect.Tag, FishState.Hunted));

  const lastFish = _.last(fishAliveCells);
  if (!lastFish) return;

  movePenguin(penguinCell, lastFish);
};

const farsee: Farsee = card => {
  const naturalBingoResult = { naturalBingo: NaturalBingoFlag.Yes, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const penguin: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

// @ts-nocheck
import _ from 'lodash';
import {
    handleDaubByPowerup,
    initializeBoxAndTicketRewardsAndOthers,
    initializeCollectionRewardsAkaShadowCard,
    initializeCounter,
} from './common';
import {IBingoRule} from './IBingoRule';
import {logger, no, yes} from '../CoreUtils2';
import {CardEffect, CellEffect} from '../effects';
import {RandomSequence} from '../RandomSequence';
import {pickRandomItemsWithMutation, randomOneItemByWeights} from '../Utils';
import {NaturalBingoFlag} from '../Card';

//业务参考文档：bingo_doc_ch/策划案/文档/04.特殊关卡/特殊岛关卡/10.小猪接金币.xlsx

const gold_bingo_num = 5;//可以bingo的金币数
const gold_max_num = 7;//最大可放置的金币数量
const firework_max_num = 6;//最大焰火数量，焰火随机消除2个格子

function randomDaubTwo(progress, card, daubedCell) {

    //所有空格子，用于校验烟花是否炸到了空格子上
    let daubedIndexes = _.transform(_.filter(card.cells, cell => {
        return cell.isDaubed;
    }), function (result, v, index) {
        result.push(v.index);
    }, []);

    if (!daubedCell.hasEffect(CellEffect.PiggyFirework, no)) {
        return;
    }
    daubedCell.updateEffectValue(CellEffect.PiggyFirework, yes);
    const daubCellIndex = daubedCell.index;
    const cardIndex = card.index;

    //region 烟花最多炸2个格子，先取数表现，再回调其它的格子
    let noDaubCells = card.getNoDaubCells();
    if (_.size(noDaubCells) <= 0) {
        return;
    }

    const candidates = card.getNoDaubCells();
    const rs = new RandomSequence(card.seed + daubedCell.index);
    const targetCells = pickRandomItemsWithMutation(candidates, _.clamp(2, candidates.length), rs);

    if (_.size(targetCells) <= 0) {
        return;
    }

    //直接涂抹，不回调
    _.each(targetCells, targetCell => {
        progress.forceDaub(card, targetCell, true);
    });

    let targetIndexes = _.transform(targetCells, function (result, v, index) {
        result.push(v.index);
    }, []);
    logger.debug('fireworkDaubEnd', {
        daubedIndexes,
        cardIndex: card.index, from: daubCellIndex, to: targetIndexes,
    });

    //回调其它格子
    _.each(targetCells, targetCell => {
        afterCellDaub(progress, card, targetCell);
    });

}


///// piggy /////
function initializeCardEffect(preset, progress) {
    initializeCollectionRewardsAkaShadowCard(preset, progress);

    _.each(progress.cards, card => {
        initializeCounter(card, gold_bingo_num);
    });
}

function initializeCellEffect(preset, progress) {
    initializeBoxAndTicketRewardsAndOthers(preset, progress);

    const weights = [
        {type: CellEffect.PiggyFirework, weight: 100},
        // {type: CellEffect.Tag, weight: 75},
    ];

    _.each(progress.cards, card => {
        const rs = new RandomSequence(card.seed);

        let tmpCells = pickRandomItemsWithMutation(card.getNoDaubCells(), gold_max_num, rs);
        let tmpIndexes = [];
        _.each(tmpCells, cell => {
            cell.pushEffect(CellEffect.PiggyGold, no);
            tmpIndexes.push(cell.index);
            logger.debug('pushGoldEnd', {
                cardIndex: card.index,
                cellIndex: cell.index
            });
        });

        //排除掉已有gold的格子
        tmpCells = pickRandomItemsWithMutation(_.filter(card.getNoDaubCells(), cellObj => {
            return !_.includes(tmpIndexes, cellObj.index);
        }), firework_max_num, rs);

        _.each(tmpCells, cell => {
            const {type: effect} = randomOneItemByWeights(weights, rs);
            if (effect === CellEffect.Tag) {
                return true;
            }
            cell.pushEffect(CellEffect.PiggyFirework, no);
            logger.debug('pushFireworkEnd', {
                effect, cardIndex: card.index,
                cellIndex: cell.index
            });
        });
    });
}

function bingoTester(card) {
    let logObj = _.pick(card, ['effects', 'index']);
    logger.debug('checkCardBingo', {...logObj});
    return getCounter(card) >= gold_bingo_num;
}

function beforeCellMeetEffect(progress, card, cell, effect) {
    handleDaubByPowerup(progress, card, cell, effect);
}

function collectGold(progress, card, daubedCell) {
    let myCells = [
        card.cellAtLocation(daubedCell.x, 0),
        card.cellAtLocation(daubedCell.x, 1),
        card.cellAtLocation(daubedCell.x, 2),
        card.cellAtLocation(daubedCell.x, 3),
        card.cellAtLocation(daubedCell.x, 4),
    ];
    _.each(myCells, cell => {
        if (!(cell.hasEffect(CellEffect.PiggyGold, no) && cell.isDaubed)) {
            return true;
        }
        // 如果金币下方没有数字了，金币落下
        let checkY = _.filter([0, 1, 2, 3, 4], y => {
            return y > cell.y;
        });
        let canCollect = true;
        for (let y of checkY) {
            let checkCell = card.cellAtLocation(daubedCell.x, y);
            if (!checkCell.isDaubed) {
                canCollect = false;
            }
        }
        if (!canCollect) {
            return true;
        }

        cell.updateEffectValue(CellEffect.PiggyGold, yes);

        let counterIndex = getCounterIndex(card);
        let oriCount = card.effects[counterIndex][1];
        let nowCount = oriCount + 1;
        card.effects[counterIndex][1] = nowCount;

        logger.debug('collectGoldEnd', {
            from: oriCount, to: nowCount,
            daubCellIndex: daubedCell.index,
            cellIndex: cell.index, cardIndex: card.index, cell, myCells, checkY,
        });
        // card.canBingo = getCounter(card) >= gold_bingo_num;
        logger.debug('checkCardEffects', {
            effects: card.effects,
            canBingo: card.canBingo, cardIndex: card.index, oriCount, nowCount,
        });

    });
}

function afterCellDaub(progress, card, daubedCell) {
    logger.debug('afterCellDaubEnd', {
        cellIndex: daubedCell.index,
        cardIndex: card.index,
        counter: getCounter(card),
    });
    collectGold(progress, card, daubedCell);
    randomDaubTwo(progress, card, daubedCell);
}

function getCounterIndex(card) {
    return _.findIndex(card.effects, a => {
        return a[0] === CardEffect.Counter;
    });
}

function getCounter(card) {
    let counterIndex = getCounterIndex(card);
    return card.effects[counterIndex][1];
}

function farsee(card) {
    const naturalBingoResult = {
        naturalBingo: NaturalBingoFlag.Yes,
        daubOnceTarget: -1,
        daubTwiceTarget: [-1, -1] as [number, number]
    };

    if (card.canBingo) return naturalBingoResult;

    const onceTarget: number | null = null;
    const twiceTarget: [number, number] | null = null;

    return {
        naturalBingo: NaturalBingoFlag.No,
        daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
        daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
    };
}

export const piggy: IBingoRule = {
    initializeCardEffect,
    initializeCellEffect,
    bingoTester,
    beforeCellMeetEffect,
    afterCellDaub,
    farsee,
};

import _ from 'lodash';
import {CellEffect, NaturalBingoFlag, RandomSequence, shuffleArr} from '..';
import {CardBingoTester} from '../Card';
import {
  handleDaubByPowerup,
  initializeBoxAndTicketRewardsAndOthers,
  initializeCollectionRewardsAkaShadowCard
} from './common';
import {CellDaubed<PERSON><PERSON><PERSON>, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

///// 万圣节南瓜灯🎃 /////
const COLUMN_COUNT = 5;
const ROW_COUNT = 5;
const CENTER_INDEX = 12;

const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
  _.forEach(progress.cards, card => {
    const rs = new RandomSequence(card.seed);
    const columns = shuffleArr(_.range(COLUMN_COUNT), rs);
    //将南瓜灯埋在除中心以外的点
    let color = 1;
    for (const i of columns) {
      const max = i == 2 ? ROW_COUNT - 1 : ROW_COUNT;
      const random = rs.nextIntegerInRange(max - 1);
      const index = random + i * ROW_COUNT;
      card.cells[i == 2 && index >= CENTER_INDEX ? index + 1 : index].pushEffect(CellEffect.Pumpkin, color++);
    }
  });
  initializeBoxAndTicketRewardsAndOthers(host, progress);
};


const bingoTester: CardBingoTester = card => {
  card.targetCount = _.filter(card.cells, cell => cell.hasEffect(CellEffect.Pumpkin) && !cell.hasEffect(CellEffect.PumpkinBoom)).length;
  return !(card.getNoDaubCells().length > 0);
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  let val;
  if ((val = cell.findEffectValue(CellEffect.Pumpkin)) != undefined) {
    //拿走南瓜灯上的帽子
    cell.updateEffectValue(CellEffect.Pumpkin, 0);
    //消除对应颜色的列
    for (let i = 0; i < ROW_COUNT; i++) {
      const cellIndex = i + (val - 1) * COLUMN_COUNT;
      if (!card.cells[cellIndex].isDaubed) {
        card.cells[cellIndex].pushEffect(CellEffect.PumpkinBoom, val);
        progress.forceDaub(card, card.cells[cellIndex]);
      }
    }
  }

};

const farsee: Farsee = card => {
  const naturalBingoResult = {
    naturalBingo: NaturalBingoFlag.Yes,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const pumpkin: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

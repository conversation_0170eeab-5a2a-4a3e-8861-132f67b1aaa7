import _ from 'lodash';
import {NaturalBingoFlag} from '..';
import {Card, CardBingoTester} from '../Card';
import {CardCell} from '../CardCell';
import {CellEffect} from '../effects';
import {
  getCounterTarget,
  handleDaubByPowerup,
  initializeBoxAndTicketRewardsAndOthers,
  initializeCenterDaub,
  initializeCollectionRewardsAkaShadowCard, initializeCounter, readCounter, updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';
import {logger} from "../CoreUtils2";

export const Constant = Object.freeze({
  OilDrumGoal: 2
});

export enum OilDrumState { None = 0, Got = 1 }

///// Rule Race /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);

  _.forEach(progress.cards, card => {
    initializeCounter(card, Constant.OilDrumGoal);
  });
};

const cornerIndexes = [0, 2, 4, 20, 22, 24];
const initializeCellEffect: RoundInitializer = (host, progress) => {
  _.forEach(progress.cards, card => {
    // 车手在中间
    progress.pushEffectToCell(card, card.cellAtIndex(12), CellEffect.Driver);
    // 🛢️ 四角 和车手两侧
    _.forEach(cornerIndexes, cellIndex => {
      progress.pushEffectToCell(card, card.cellAtIndex(cellIndex), CellEffect.OilDrum, OilDrumState.None);
    });
  });

  initializeCenterDaub(host, progress);
  initializeBoxAndTicketRewardsAndOthers(host, progress);
};

const bingoTester: CardBingoTester = card => {
  card.targetCount = _.filter(cornerIndexes, cellIndex => card.cellAtIndex(cellIndex).hasEffect(CellEffect.OilDrum) && card.cellAtIndex(cellIndex).isDaubed).length;
  if (readCounter(card) >= getCounterTarget(card)) return true;
  return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};

// Simple tree searching
const isConnected = (card: Card, from: CardCell, to: CardCell, path: number[] = []): boolean => {
  if (from.index === to.index) return true;

  if (!path) path = [];
  path.push(from.index);

  const l = card.cellAtLocation(from.x - 1, from.y);
  const r = card.cellAtLocation(from.x + 1, from.y);
  const t = card.cellAtLocation(from.x, from.y - 1);
  const b = card.cellAtLocation(from.x, from.y + 1);
  const validNeighbors = _.filter([l, r, t, b] as CardCell[], cell => cell && cell.isDaubed && _.indexOf(path, cell.index) === -1);

  let meet = false;
  for (let i = 0; i < validNeighbors.length; i += 1) {
    if (isConnected(card, validNeighbors[i], to, path)) meet = true;
  }

  return meet;
};

enum Direction {
  Up,
  Right,
  Down,
  Left,
}

function determineDirectionPriority(from: CardCell, to: CardCell): Direction[] {
  const directionPriority = [Direction.Up, Direction.Right, Direction.Down, Direction.Left];
  if (to.x - from.x > 0) {
    directionPriority[0] = Direction.Right;
    directionPriority[3] = Direction.Left;
    if (to.y - from.y > 0) {
      directionPriority[1] = Direction.Down;
      directionPriority[2] = Direction.Up;
    } else {
      directionPriority[1] = Direction.Up;
      directionPriority[2] = Direction.Down;
    }
  } else if (to.x - from.x < 0) {
    directionPriority[0] = Direction.Left;
    directionPriority[3] = Direction.Right;
    if (to.y - from.y > 0) {
      directionPriority[1] = Direction.Down;
      directionPriority[2] = Direction.Up;
    } else {
      directionPriority[1] = Direction.Up;
      directionPriority[2] = Direction.Down;
    }
  } else {
    directionPriority[1] = Direction.Right;
    directionPriority[2] = Direction.Left;
    if (to.y - from.y > 0) {
      directionPriority[0] = Direction.Down;
      directionPriority[3] = Direction.Up;
    } else if (to.y - from.y < 0) {
      directionPriority[0] = Direction.Up;
      directionPriority[3] = Direction.Down;
    } else {
      throw new Error('Invalid operation.');
    }
  }
  return directionPriority;
}

const findDaubCandidates = (card: Card, from: CardCell, to: CardCell): { onceTarget: number | null, twiceTarget: [number, number] | null } => {
  const result: { onceTarget: number | null, twiceTarget: [number, number] | null } = {
    onceTarget: null,
    twiceTarget: null,
  };

  const routine = (card: Card, from: CardCell, to: CardCell, path: number[] = [], requiredDaubIndex: number[] = []) => {
    if (result.onceTarget !== null && result.twiceTarget !== null) return;

    path.push(from.index);
    if (!from.isDaubed) requiredDaubIndex.push(from.index);
    if (requiredDaubIndex.length > 2) return;

    if (from.index === to.index) {
      if (requiredDaubIndex.length === 1) result.onceTarget = _.get(requiredDaubIndex, 0, -1);
      else result.twiceTarget = [_.get(requiredDaubIndex, 0, -1), _.get(requiredDaubIndex, 1, -1)];
      return;
    }

    const directionPriority = determineDirectionPriority(from, to);
    let i = 0;
    while (i < directionPriority.length) {
      const direction = directionPriority[i];
      i += 1;
      let nextMove: CardCell | undefined;
      if (direction === Direction.Up) nextMove = card.cellAtLocation(from.x, from.y - 1);
      if (direction === Direction.Right) nextMove = card.cellAtLocation(from.x + 1, from.y);
      if (direction === Direction.Down) nextMove = card.cellAtLocation(from.x, from.y + 1);
      if (direction === Direction.Left) nextMove = card.cellAtLocation(from.x - 1, from.y);

      if (!nextMove) continue;
      if (_.indexOf(path, nextMove.index) !== -1) continue;
      routine(card, nextMove, to, path, requiredDaubIndex);
    }
  };

  routine(card, from, to);

  return result;
};

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const driverCell = _.find(card.cells, cell => cell.hasEffect(CellEffect.Driver));
  if (!driverCell) throw new Error('Invalid card data.');

  _.forEach(cornerIndexes, oilDrumIndex => {
    const oilDrumCell = card.cellAtIndex(oilDrumIndex);
    if (oilDrumCell.hasEffect(CellEffect.OilDrum, OilDrumState.Got)) return;

    if (isConnected(card, driverCell, oilDrumCell)) {
      logger.debug('get_oil_drum', {
        driverCell: driverCell.index,
        oilDrumCell: oilDrumCell.index
      });
      progress.pullAllEffectFromCell(card, driverCell, CellEffect.Driver);
      progress.pushEffectToCell(card, oilDrumCell, CellEffect.Driver);
      updateCounter(card, readCounter(card) + 1);
      oilDrumCell.updateEffectValue(CellEffect.OilDrum, OilDrumState.Got);
    }
  });
};


const farsee: Farsee = card => {
  const naturalBingoResult = {
    naturalBingo: NaturalBingoFlag.Yes,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };
  if (card.canBingo) return naturalBingoResult;
  const poorResult = {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };

  let onceTarget: number | null = null;
  let twiceTarget: [number, number] | null = null;

  const getOilDrumCount = _.filter(cornerIndexes, cellIndex => card.cellAtIndex(cellIndex).hasEffect(CellEffect.OilDrum, OilDrumState.Got)).length;
  if (getOilDrumCount >= Constant.OilDrumGoal) return naturalBingoResult;
  if (getOilDrumCount === 0) return poorResult; // Only find one case

  const driverCell = _.find(card.cells, cell => cell.hasEffect(CellEffect.Driver));
  if (!driverCell) return poorResult;
  const nextOilDrumCell = _.find(card.cells, cell => cell.hasEffect(CellEffect.OilDrum, OilDrumState.None));
  if (!nextOilDrumCell) return poorResult;

  const requirements = findDaubCandidates(card, driverCell, nextOilDrumCell);
  onceTarget = requirements.onceTarget;
  twiceTarget = requirements.twiceTarget;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const race: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

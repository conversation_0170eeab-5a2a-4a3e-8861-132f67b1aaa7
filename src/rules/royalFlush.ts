import _ from 'lodash';
import { Card, CardBingoTester, CardEffect, CellEffect, NaturalBingoFlag, pickRandomItemsWithMutation, RandomSequence } from '..';
import { handleDaubByPowerup, initializeBoxAndTicketRewardsAndOthers, initializeCollectionRewardsAkaShadowCard } from './common';
import { CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer } from './IBingoRule';

///// Rule /////

export enum Poker {
  BlackAce =
  0b000001,
  BlackKing =
  0b000010,
  BlackQueen =
  0b000100,
  BlackJack =
  0b001000,
  Black10 =
  0b010000,
  RedAce =
  0b100001,
  RedKing =
  0b100010,
  RedQueen =
  0b100100,
  RedJack =
  0b101000,
  Red10 =
  0b110000,
  JokerA =
  0b01000000,
  JokerB =
  0b10000000,
  Empty = 0,
  BlackFlush = BlackAce | BlackKing | BlackQueen | BlackJack | Black10,
  RedFlush = RedAce | RedKing | RedQueen | RedJack | Red10,
}

export type JokerClue = {
  cellIndex: number;
  targets: number[];
};

export function hasPoker(state: number, poker: Poker): boolean {
  return (state & poker) === poker;
}

function randomOnePoker(candidates: Poker[], rs: RandomSequence): Poker {
  const randomIndex = rs.nextIntegerInRange(0, candidates.length - 1);
  return candidates[randomIndex];
}

function collectPoker(card: Card, poker: Poker): void {
  const flushStates = _.filter(card.effects, ([t]) => t === CardEffect.FlushState);
  const targetState = flushStates[poker >> 5];

  targetState[1] = targetState[1] | poker;
}

const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);

  _.forEach(progress.cards, c => {
    c.pushEffect(CardEffect.FlushState, Poker.Empty);
    c.pushEffect(CardEffect.FlushState, Poker.Empty);
  });
};

const initializeCellEffect: RoundInitializer = (preset, progress) => {
  initializeBoxAndTicketRewardsAndOthers(preset, progress);

  const normalPokers = [
    Poker.BlackAce,
    Poker.BlackKing,
    Poker.BlackQueen,
    Poker.BlackJack,
    Poker.Black10,
    Poker.RedAce,
    Poker.RedKing,
    Poker.RedQueen,
    Poker.RedJack,
    Poker.Red10,
  ];

  _.forEach(progress.cards, card => {
    const rs = new RandomSequence(card.seed);

    const noDaubCells = card.getNoDaubCells();
    pickRandomItemsWithMutation(noDaubCells, 1, rs)[0].pushEffect(CellEffect.Tag, Poker.JokerA);
    pickRandomItemsWithMutation(noDaubCells, 1, rs)[0].pushEffect(CellEffect.Tag, Poker.JokerB);

    _.forEach(normalPokers, poker => {
      pickRandomItemsWithMutation(noDaubCells, 1, rs)[0].pushEffect(CellEffect.Tag, poker);
    });

    // There are 10% chance to get a rare card
    if (rs.nextDouble() < 0.1) {
      _.forEach(normalPokers, poker => {
        pickRandomItemsWithMutation(noDaubCells, 1, rs)[0].pushEffect(CellEffect.Tag, poker);
      });
    }

    _.forEach(noDaubCells, c => {
      const randomPoker = randomOnePoker(normalPokers, rs);
      c.pushEffect(CellEffect.Tag, randomPoker);
    });
  });
};

const bingoTester: CardBingoTester = card => {
  const states = _.filter(card.effects, ([t]) => t === CardEffect.FlushState);
  if (states[0][1] === Poker.BlackFlush && states[1][1] === Poker.RedFlush) return true;

  return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const pokerTag = cell.findEffect(CellEffect.Tag);
  if (!pokerTag) return;

  const poker = pokerTag[1];

  // daub random 2
  if (poker === Poker.JokerA || poker === Poker.JokerB) {
    const rs = new RandomSequence(card.seed - cell.index);

    const clue: JokerClue = { cellIndex: cell.index, targets: [] };

    const daubCount = 2;
    for (let i = 0; i < daubCount; i += 1) {
      const noDaubCells = card.getNoDaubCells();
      if (noDaubCells.length == 0) break;

      const targetCell = pickRandomItemsWithMutation(noDaubCells, 1, rs)[0];
      progress.forceDaub(card, targetCell);
      clue.targets.push(targetCell.index);
    }

    progress.recordClue(card.index, (): JokerClue => clue);

    return;
  }

  collectPoker(card, pokerTag[1]);
};

const farsee: Farsee = card => {
  const naturalBingoResult = { naturalBingo: NaturalBingoFlag.Yes, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const royalFlush: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

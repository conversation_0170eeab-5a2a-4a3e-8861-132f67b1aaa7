import {
  Card,
  CardEffect,
  CellEffect,
  NaturalBingoFlag,
  PlayerRoundProgress,
  RandomSequence
} from '..';
import {CardBingoTester} from '../Card';
import {
  handleDaubByPowerup,
  initializeBoxAndTicketRewardsAndOthers,
  initializeCollectionRewardsAkaShadowCard, initializeCounter,
  readCounter,
  updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

const MAX_RUBBER_COUNT = 3;//橡皮糖最大个数
const GENERATE_RUBBER_STEP = 4;//橡皮糖生成需要涂抹次数
const RUBBER_DAUB_COUNT = 3;//橡皮糖涂抹个数
const RUBBER_DAUB_INTERVAL = [5, 9];//橡皮糖涂抹间隔


///// Rule 橡皮糖 /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);
  progress.cards.forEach(card => {
    card.pushEffect(CardEffect.RubberCount, 0);
    initializeCounter(card, GENERATE_RUBBER_STEP);
  });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
  initializeBoxAndTicketRewardsAndOthers(host, progress);
};


const bingoTester: CardBingoTester = card => {
  for (const cell of card.cells) {
    if (!cell.isDaubed) {
      return false;
    }
  }
  return true;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  if (cell.hasEffect(CellEffect.Rubber)) {
    checkCardState(progress, card);
  }
  const counter = readCounter(card);
  const rubberCount = card.findEffectValue(CardEffect.RubberCount) as number;
  //进度条满足 且 当前卡里橡皮糖不超过限制 生成新的橡皮糖
  if (counter + 1 >= GENERATE_RUBBER_STEP && rubberCount < MAX_RUBBER_COUNT) {
    //生成橡皮糖
    createNewRubber(progress, card, cell.index);
    card.updateEffectValue(CardEffect.RubberCount, rubberCount + 1);
    updateCounter(card, 0);
  } else if (counter < GENERATE_RUBBER_STEP) {
    updateCounter(card, counter + 1);
  }
};

const createNewRubber = (progress: PlayerRoundProgress, card: Card, index: number) => {
  const rs = new RandomSequence(card.seed + index);
  let daubTime = progress.curRoundTime;
  //获取能被橡皮糖涂抹的位置
  const availableCells = card.cells.filter(cell => !cell.isDaubed && !cell.hasEffect(CellEffect.Rubber));
  for (let i = 0; i < RUBBER_DAUB_COUNT; i++) {
    if (availableCells.length > 0) {
      //随机间隔
      const rd = rs.nextIntegerInRange(RUBBER_DAUB_INTERVAL[1], RUBBER_DAUB_INTERVAL[0]);
      daubTime = (Math.floor(daubTime / 1000) + rd) * 1000;
      const idx = rs.nextIntegerInRange(availableCells.length - 1);
      const nextCell = availableCells[idx];
      nextCell.pushEffect(CellEffect.Rubber, daubTime, {index});
      availableCells.splice(idx, 1);
    }
  }
  //console.log('create', ddd.join('-'));
};

const checkState = (progress: PlayerRoundProgress) => {
  for (const card of progress.cards) {
    checkCardState(progress, card);
  }
};


const checkCardState = (progress: PlayerRoundProgress, card: Card) => {
  const curRoundTime = progress.curRoundTime;
  let isCheck = false;
  const rubberIndexes = [];
  for (const cell of card.cells) {
    const effect = cell.findEffect(CellEffect.Rubber);
    if (effect) {
      const daubTime = effect[1];
      if (daubTime > 0) {
        if (curRoundTime >= daubTime || cell.isDaubed) {
          if (!cell.isDaubed) {
            progress.forceDaub(card, cell);
          }
          cell.updateEffectValue(CellEffect.Rubber, 0);
          isCheck = true;
        } else {
          rubberIndexes.push(effect[2].index);
        }
      }
    }
  }
  if (isCheck) {
    //更新卡上橡皮糖个数
    const rubberCount = new Set([...rubberIndexes]).size;
    card.updateEffectValue(CardEffect.RubberCount, rubberCount);
    const counter = readCounter(card);
    if (counter >= GENERATE_RUBBER_STEP && rubberCount < MAX_RUBBER_COUNT) {
      //生成橡皮糖
      createNewRubber(progress, card, curRoundTime);
    }
  }
};


const farsee: Farsee = card => {
  const naturalBingoResult = {
    naturalBingo: NaturalBingoFlag.Yes,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const rubber: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
  checkState,
};

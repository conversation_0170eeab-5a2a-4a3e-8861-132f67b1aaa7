import _ from 'lodash';
import {CellEffect, findSameCellRegion, IWeight, NaturalBingoFlag, randomOneItemByWeights, RandomSequence} from '..';
import {CardBingoTester} from '../Card';
import {CardCell} from '../CardCell';
import {
  getCounterTarget,
  handleDaubByPowerup,
  initializeBoxAndTicketRewardsAndOthers,
  initializeCenterDaub,
  initializeCollectionRewardsAkaShadowCard,
  initializeCounter,
  readCounter,
  updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

enum FishLevel { None, Small, Medium, Big, Super } //🐟等级 0为吃掉了

const FISH_EXP_GOAL = [6, 12, 20];//达到对应等级所需经验
const FISH_EXP = [0, 2, 5];//吃掉每种🐟获得的经验
const getEffectWeights: () => ({ val: number } & IWeight)[] = () => [
  {val: 0, weight: 10},
  {val: 1, weight: 50},
  {val: 2, weight: 40}
];

///// Rule Shark /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);

  _.forEach(progress.cards, card => {
    initializeCounter(card, _.last(FISH_EXP_GOAL) as number);
  });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
  initializeCenterDaub(host, progress);
  _.forEach(progress.cards, card => {
    // 主角在中间
    progress.pushEffectToCell(card, card.cellAtIndex(12), CellEffect.Shark, FishLevel.Small);
    const weights = getEffectWeights();
    const rs = new RandomSequence(card.seed);
    _.each(card.getNoDaubCells(), cell => {
      const {val} = randomOneItemByWeights(weights, rs);
      if (val > 0) {
        cell.pushEffect(CellEffect.Fish, val);
      }
    });

  });
  initializeBoxAndTicketRewardsAndOthers(host, progress);
};


const bingoTester: CardBingoTester = card => {
  if (readCounter(card) >= getCounterTarget(card)) return true;
  return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const heroCell = _.find(card.cells, c => c.hasEffect(CellEffect.Shark));
  if (!heroCell) throw new Error('No available hero cell.');
  const availableRegion = findSameCellRegion(heroCell, card, c => c.isDaubed);

  const fishCells = _(availableRegion)
      .filter(c => c.hasEffect(CellEffect.Fish))
      .sortBy(c => c.findEffectValue(CellEffect.Fish))
      .value();
  let sharkLevel: number = heroCell.findEffectValue(CellEffect.Shark) as number;
  let curExp = readCounter(card);
  let lastCell = null;
  _.each(fishCells, c => {
    const fishLevel = c.findEffectValue(CellEffect.Fish);
    if (!fishLevel || (fishLevel === FishLevel.Medium && sharkLevel < FishLevel.Big)) {
      return;
    }

    curExp += FISH_EXP[fishLevel];
    if (curExp >= FISH_EXP_GOAL[sharkLevel - 1]) {
      heroCell.updateEffectValue(CellEffect.Shark, ++sharkLevel);
    }
    c.updateEffectValue(CellEffect.Fish, FishLevel.None);
    lastCell = cell;
  });
  if (!lastCell) return;
  updateCounter(card, curExp);
  moveHero(heroCell, lastCell);
};

const moveHero = (from: CardCell, to: CardCell) => {
  const effectVal = from.findEffectValue(CellEffect.Shark);
  from.pullAllEffect(CellEffect.Shark);
  to.pushEffect(CellEffect.Shark, effectVal);

};


const farsee: Farsee = card => {
  const naturalBingoResult = {
    naturalBingo: NaturalBingoFlag.Yes,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const shark: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

import {Card, CardEffect, CellEffect, NaturalBingoFlag} from '..';
import {CardBingoTester} from '../Card';
import {
    handleDaubByPowerup, initializeBoxAndTicketRewardsAndOthers,
    initializeCardSeed,
    initializeCollectionRewardsAkaShadowCard,
    readCounter,
    updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

const BINGO_TARGET = 160;
const ITEM = Object.freeze<{ [key: string]: number }>({
    NONE: 0,
    SKI_BOARD: 1,
    JET_BAG: 2,
    ROCKET: 3,
});
const ITEM_NUM = [0, 5, 4, 3];
const ITEM_SCORE = [0, 10, 30, 100];

///// Rule Zuma /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
    initializeCollectionRewardsAkaShadowCard(preset, progress);

    progress.cards.forEach(card => {
        initializeCardSeed(card, card.seed);
        card.pushEffect(CardEffect.CounterTarget, BINGO_TARGET);
        card.pushEffect(CardEffect.Counter, 1); // 初始为1分
        initializeItems(card);
    });
};


const initializeItems = (card: Card) => {
    const allNum = Object.values(ITEM).reduce((pre, cur) => pre + ITEM_NUM[cur], 0);
    const randomCells = card.pickRandomCellsWithMutation(card.getNoDaubCells(), allNum);
    let idx = 0;
    for (const item of Object.values(ITEM)) {
        const num = ITEM_NUM[item];
        for (let i = 0; i < num; i++) {
            randomCells[idx].pushEffect(CellEffect.SkiItemVector, item);
            idx++;
        }
    }
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
    initializeBoxAndTicketRewardsAndOthers(host, progress);
};

const bingoTester: CardBingoTester = card => readCounter(card) >= BINGO_TARGET;

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => handleDaubByPowerup(progress, card, cell, effect);


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
    const item = cell.findEffectValue(CellEffect.SkiItemVector);
    const curScore = readCounter(card);
    if (item) {
        updateCounter(card, curScore + ITEM_SCORE[item]);
        // 使用负数代表点过的状态
        cell.updateEffectValue(CellEffect.SkiItemVector, -item);
    }
};


const farsee: Farsee = card => {
    const naturalBingoResult = {
        naturalBingo: NaturalBingoFlag.Yes,
        daubOnceTarget: -1,
        daubTwiceTarget: [-1, -1] as [number, number]
    };

    if (card.canBingo) return naturalBingoResult;

    const onceTarget: number | null = null;
    const twiceTarget: [number, number] | null = null;

    return {
        naturalBingo: NaturalBingoFlag.No,
        daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
        daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
    };
};
export const ski: IBingoRule = {
    initializeCardEffect,
    initializeCellEffect,
    bingoTester,
    beforeCellMeetEffect,
    afterCellDaub,
    farsee,
};

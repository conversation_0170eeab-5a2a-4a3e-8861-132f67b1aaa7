import {
    CellEffect,
    NaturalBingoFlag,
} from '..';
import {CardBingoTester} from '../Card';
import {
    getCounterTarget,
    handleDaubByPowerup,
    initializeBoxAndTicketRewardsAndOthers,
    initializeCardSeed,
    initializeCollectionRewardsAkaShadowCard,
    initializeCounter,
    readCounter,
    updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';
import _ from "lodash";

const BATTERY_RATE = 0.2;//出现电池的概率
const DIE_OUT_INSECT_NUM = 25;//消灭虫子的个数
const BATTERY_DIE_OUT_INSECT_NUM = 4;//电池消灭虫子数量

///// space war fare /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
    initializeCollectionRewardsAkaShadowCard(preset, progress);
    progress.cards.forEach(card => {
        initializeCardSeed(card, card.seed);
        initializeCounter(card, DIE_OUT_INSECT_NUM);
    });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
    initializeBoxAndTicketRewardsAndOthers(host, progress);
    progress.cards.forEach(card => {
        const noDaubCells = card.getNoDaubCells();
        const remainCount = Math.ceil(BATTERY_RATE * (noDaubCells.length));
        const cellIndexList: number[] = [];
        _.forEach(noDaubCells, cell => {
            cellIndexList.push(cell.index);
        });
        const randomIndexes = card.getNoDaubCellsRandomIndex(remainCount);//随机几个格子,埋电池

        _.forEach(noDaubCells, cell => {
            if (randomIndexes.indexOf(cell.index) != -1) {
                cell.pushEffect(CellEffect.SpaceBatter, 0, {dieOutInsect: []});
            }
            cell.pushEffect(CellEffect.SpaceInsect, 0);
        });
    });
};


const bingoTester: CardBingoTester = card => {
    if (readCounter(card) >= getCounterTarget(card)) return true;
    return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
    handleDaubByPowerup(progress, card, cell, effect);
};
const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
    const BatterEffect = cell.findEffect(CellEffect.SpaceBatter);
    const InsectEffect = cell.findEffect(CellEffect.SpaceInsect);
    let dieInsectNum = 0;
    if (InsectEffect && InsectEffect[1] != 1) {
        cell.updateEffectValue(CellEffect.SpaceInsect, 1);
        dieInsectNum++;
    }
    if (BatterEffect) {
        const dieCells = card.getNoDaubCellsRandomCellS(BATTERY_DIE_OUT_INSECT_NUM, CellEffect.SpaceInsect, 0);//随机几个格子,消灭虫子
        _.forEach(dieCells, cell => {
            cell.updateEffectValue(CellEffect.SpaceInsect, 1);
            BatterEffect[2].dieOutInsect.push(cell.index);
            dieInsectNum++;
        });
        cell.updateEffectValue(CellEffect.SpaceBatter, 1);
    }
    if (dieInsectNum) {
        updateCounter(card, readCounter(card) + dieInsectNum);
    }
};
const farsee: Farsee = card => {
    const naturalBingoResult = {
        naturalBingo: NaturalBingoFlag.Yes,
        daubOnceTarget: -1,
        daubTwiceTarget: [-1, -1] as [number, number]
    };

    if (card.canBingo) return naturalBingoResult;

    const onceTarget: number | null = null;
    const twiceTarget: [number, number] | null = null;

    return {
        naturalBingo: NaturalBingoFlag.No,
        daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
        daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
    };
};

export const spaceWarfare: IBingoRule = {
    initializeCardEffect,
    initializeCellEffect,
    bingoTester,
    beforeCellMeetEffect,
    afterCellDaub,
    farsee
};

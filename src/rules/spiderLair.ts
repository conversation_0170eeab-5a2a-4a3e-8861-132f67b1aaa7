import {
    Card,
    CellEffect,
    NaturalBingoFlag, randomInt,
} from '..';
import {CardBingoTester} from '../Card';
import {
    getCounterTarget,
    handleDaubByPowerup,
    initializeBoxAndTicketRewardsAndOthers,
    initializeCardSeed,
    initializeCollectionRewardsAkaShadowCard,
    initializeCounter,
    readCounter,
    updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';
import _ from "lodash";

const PICTURE_NUM = 1;//
const pictureObj: { [key: string]: any } = {
    picture1: {A: [[0, 0], [0, 1]], B: [[1, 0], [1, 1], [2, 0], [2, 1]]},
    picture2: {A: [[2, 0], [2, 1]], B: [[0, 0], [0, 1], [1, 0], [1, 1]]},
    picture3: {A: [[0, 0], [1, 0]], B: [[0, 1], [0, 2], [1, 1], [1, 2]]},
    picture4: {A: [[0, 2], [1, 2]], B: [[0, 0], [0, 1], [1, 0], [1, 1]]},
};


///// spiderLair ///// 蜘蛛巢穴
const initializeCardEffect: RoundInitializer = (preset, progress) => {
    initializeCollectionRewardsAkaShadowCard(preset, progress);
    progress.cards.forEach(card => {
        initializeCardSeed(card, card.seed);
        initializeCounter(card, PICTURE_NUM);
    });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
    initializeBoxAndTicketRewardsAndOthers(host, progress);
    progress.cards.forEach(card => {
        const PictureTypeList = getPictureTypes(card);
        const bigXY = getXYOffset(PictureTypeList, 0);
        const centerCell = card.cellAtLocation(2, 2);
        if (centerCell) {
            centerCell.isDaubed = true;
        }
        for (let i = 0; i < PictureTypeList.length; i++) {
            const pictureType = PictureTypeList[i];
            const param = `picture${pictureType}`;
            const pictureA = pictureObj[param]["A"];
            const pictureB = pictureObj[param]["B"];
            _.forEach(pictureA, cellXY => {
                if (i == 1) {//右上角
                    const cell = card.cellAtLocation(bigXY[0] + cellXY[0] + 1, cellXY[1]);
                    cell?.pushEffect(CellEffect.SpiderStaff, 0, {type: i});//标记是法杖图片 type类型相同，表示同一张图片
                } else if (i == 2) {//左下脚
                    const cell = card.cellAtLocation(cellXY[0], bigXY[1] + cellXY[1] + 1);
                    cell?.pushEffect(CellEffect.SpiderStaff, 0, {type: i});//标记是法杖图片
                } else if (i == 3) {//右下脚的
                    const bigXY1 = getXYOffset(PictureTypeList, 1);
                    const bigXY2 = getXYOffset(PictureTypeList, 2);
                    const x = bigXY2[0] + cellXY[0] + 1, y = bigXY1[1] + cellXY[1] + 1;
                    const cell = card.cellAtLocation(bigXY2[0] + cellXY[0] + 1, bigXY1[1] + cellXY[1] + 1);
                    cell?.pushEffect(CellEffect.SpiderStaff, 0, {type: i});//标记是法杖图片
                } else {//左上角
                    const cell = card.cellAtLocation(cellXY[0], cellXY[1]);
                    cell?.pushEffect(CellEffect.SpiderStaff, 0, {type: i});//标记是法杖图片
                }
            });
            _.forEach(pictureB, cellXY => {
                if (i == 1) {//右上角
                    const cell = card.cellAtLocation(bigXY[0] + cellXY[0] + 1, cellXY[1]);
                    cell?.pushEffect(CellEffect.Spider, 0, {type: i});//标记是蜘蛛图片
                } else if (i == 2) {//左下脚
                    const cell = card.cellAtLocation(cellXY[0], bigXY[1] + cellXY[1] + 1);
                    cell?.pushEffect(CellEffect.Spider, 0, {type: i});//标记是蜘蛛图片
                } else if (i == 3) {//右下脚的
                    const bigXY1 = getXYOffset(PictureTypeList, 1);
                    const bigXY2 = getXYOffset(PictureTypeList, 2);
                    const x = bigXY2[0] + cellXY[0] + 1, y = bigXY1[1] + cellXY[1] + 1;
                    const cell = card.cellAtLocation(bigXY2[0] + cellXY[0] + 1, bigXY1[1] + cellXY[1] + 1);
                    cell?.pushEffect(CellEffect.Spider, 0, {type: i});//标记是蜘蛛图片
                } else {//左上角
                    const cell = card.cellAtLocation(cellXY[0], cellXY[1]);
                    cell?.pushEffect(CellEffect.Spider, 0, {type: i});//标记是蜘蛛图片
                }
            });
        }

    });
};


const bingoTester: CardBingoTester = card => {
    if (readCounter(card) >= getCounterTarget(card)) return true;
    return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
    handleDaubByPowerup(progress, card, cell, effect);
};
const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
    const DaubCells = card.getDaubCells();
    let spiderCondition = false;
    let staffNumCondition = false;
    //判断4种格子的，其实时候有满足的条件的
    for (let i = 0; i < 4; i++) {//i 0，1，2，3 代表type
        let spiderNum = 0;//同张图片蜘蛛涂抹个数
        let StaffNum = 0;//同张图片法杖涂抹个数
        _.forEach(DaubCells, cell => {
            const spiderEffect = cell.findEffect(CellEffect.Spider);
            const spiderStaffEffect = cell.findEffect(CellEffect.SpiderStaff);
            if (spiderEffect && spiderEffect[2].type === i) {
                spiderNum++;
            }
            if (spiderStaffEffect && spiderStaffEffect[2].type === i) {
                StaffNum++;
            }
        });
        if (spiderNum >= 4) {
            spiderCondition = true;
        }
        if (StaffNum >= 2) {
            staffNumCondition = true;
        }
    }
    if (spiderCondition && staffNumCondition) {
        updateCounter(card, 1);
    }
};
const farsee: Farsee = card => {
    const naturalBingoResult = {
        naturalBingo: NaturalBingoFlag.Yes,
        daubOnceTarget: -1,
        daubTwiceTarget: [-1, -1] as [number, number]
    };

    if (card.canBingo) return naturalBingoResult;

    const onceTarget: number | null = null;
    const twiceTarget: [number, number] | null = null;

    return {
        naturalBingo: NaturalBingoFlag.No,
        daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
        daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
    };
};
const getPictureTypes = (card: Card) => {
    const recordPictureType = [];
    let randomIndex = randomInt(1, 4, card);
    recordPictureType.push(randomIndex);
    if (randomIndex <= 2) {
        randomIndex = randomInt(3, 4, card);//随机3-4
        recordPictureType.push(randomIndex);
        randomIndex = randomInt(3, 4, card);//随机3-4
        recordPictureType.push(randomIndex);
        randomIndex = randomInt(1, 2, card);//随机1-2
        recordPictureType.push(randomIndex);
    } else {
        randomIndex = randomInt(1, 2, card);//随机1-2
        recordPictureType.push(randomIndex);
        randomIndex = randomInt(1, 2, card);//随机1-2
        recordPictureType.push(randomIndex);
        randomIndex = randomInt(3, 4, card);//随机3-4
        recordPictureType.push(randomIndex);
    }
    return recordPictureType;
};
//获取x y的偏移量
const getXYOffset = (pictureTypeList: number[], offsetIndex: number) => {
    const picture0Type = pictureTypeList[offsetIndex];//第一个位置，后面的依靠第一个位置坐标偏移
    const picture0A = pictureObj[`picture${picture0Type}`]["A"];
    const picture0B = pictureObj[`picture${picture0Type}`]["B"];
    //查找最大x y
    const bigXY = [0, 0];
    _.forEach(picture0A, pictureXY => {
        if (pictureXY[0] > bigXY[0]) {
            bigXY[0] = pictureXY[0];
        }
        if (pictureXY[1] > bigXY[1]) {
            bigXY[1] = pictureXY[1];
        }
    });
    _.forEach(picture0B, pictureXY => {
        if (pictureXY[0] > bigXY[0]) {
            bigXY[0] = pictureXY[0];
        }
        if (pictureXY[1] > bigXY[1]) {
            bigXY[1] = pictureXY[1];
        }
    });
    return bigXY;
};

export const spiderLair: IBingoRule = {
    initializeCardEffect,
    initializeCellEffect,
    bingoTester,
    beforeCellMeetEffect,
    afterCellDaub,
    farsee
};

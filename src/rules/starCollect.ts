import {
    CellEffect,
    NaturalBingoFlag,
    randomInt,
    weightRandom
} from '..';
import {CardBingoTester} from '../Card';
import {
    getCounterTarget,
    handleDaubByPowerup,
    initializeBoxAndTicketRewardsAndOthers,
    initializeCardSeed,
    initializeCollectionRewardsAkaShadowCard,
    initializeCounter,
    readCounter,
    updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';
import _ from "lodash";

const COLLECT_STAR = 50;//收集星星
const STAR_RATE = [13, 30, 20, 15, 10, 7, 5];//出每中星星的概率
const STAR_NUM = [0, 1, 2, 3, 4, 5, 10];//每种星星类型的收集星星的个数 0表示没埋星星
const HAT_STAR_NUM = 15;//帽子收集星星的个数

///// start collect /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
    initializeCollectionRewardsAkaShadowCard(preset, progress);
    progress.cards.forEach(card => {
        initializeCardSeed(card, card.seed);
        initializeCounter(card, COLLECT_STAR);
    });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
    initializeBoxAndTicketRewardsAndOthers(host, progress);
    progress.cards.forEach(card => {
        const noDaubCells = card.getNoDaubCells();
        const hatCellsIndex = randomInt(0, noDaubCells.length - 1, card);
        const cellIndex = noDaubCells[hatCellsIndex].index;
        noDaubCells[hatCellsIndex].pushEffect(CellEffect.CollectStarHat, 0, {"count": HAT_STAR_NUM});
        _.forEach(noDaubCells, cell => {
            const starTypeIndex = weightRandom(STAR_RATE, card);
            if (STAR_NUM[starTypeIndex] && cell.index != cellIndex) {
                cell.pushEffect(CellEffect.CollectStar, 0, {"index": starTypeIndex, "count": STAR_NUM[starTypeIndex]});
            }
        });
    });
};


const bingoTester: CardBingoTester = card => {
    if (readCounter(card) >= getCounterTarget(card)) return true;
    return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
    handleDaubByPowerup(progress, card, cell, effect);
};

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
    const HatEffect = cell.findEffect(CellEffect.CollectStarHat);
    const StarEffect = cell.findEffect(CellEffect.CollectStar);
    let addStarNum = 0;
    if (HatEffect && HatEffect[1] != 1) {
        addStarNum += HatEffect[2].count;
        cell.updateEffectValue(CellEffect.CollectStarHat, 1);
    } else if (StarEffect && StarEffect[1] != 1) {
        addStarNum += StarEffect[2].count;
        cell.updateEffectValue(CellEffect.CollectStar, 1);
    }
    if (addStarNum) {
        updateCounter(card, readCounter(card) + addStarNum);
    }
};
const farsee: Farsee = card => {
    const naturalBingoResult = {
        naturalBingo: NaturalBingoFlag.Yes,
        daubOnceTarget: -1,
        daubTwiceTarget: [-1, -1] as [number, number]
    };

    if (card.canBingo) return naturalBingoResult;

    const onceTarget: number | null = null;
    const twiceTarget: [number, number] | null = null;

    return {
        naturalBingo: NaturalBingoFlag.No,
        daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
        daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
    };
};

export const collectStar: IBingoRule = {
    initializeCardEffect,
    initializeCellEffect,
    bingoTester,
    beforeCellMeetEffect,
    afterCellDaub,
    farsee
};

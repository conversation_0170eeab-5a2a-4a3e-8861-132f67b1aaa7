import {
    Card, CardCell,
    CardEffect,
    CellEffect,
    NaturalBingoFlag,
    randomInt
} from '..';
import {CardBingoTester} from '../Card';
import {
    getCounterTarget,
    handleDaubByPowerup,
    initializeBoxAndTicketRewardsAndOthers,
    initializeCardSeed,
    initializeCollectionRewardsAkaShadowCard,
    initializeCounter,
    readCounter,
    updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

const COLLECT_TARGET = 3;//松鼠收集总个数
const PINE_NUTS_COUNT = 10;//松子个数
const SQUIRREL = 4;//松鼠个数
const COLLECT_PINE_NUTS = 2;//松鼠收集松子个数
// 0，1,2,3,4 分别代表松鼠轴上x的位子 // 存储松鼠的
interface CustomData {
    position: { x: number; y: number },
    indexes: number[]
}

///// Rule supplies winter /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
    initializeCollectionRewardsAkaShadowCard(preset, progress);
    progress.cards.forEach(card => {
        initializeCardSeed(card, card.seed);
        initializeCounter(card, COLLECT_TARGET);
        initializeSquirrel(card, SQUIRREL);
    });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
    initializeBoxAndTicketRewardsAndOthers(host, progress);
    progress.cards.forEach(card => {
        const noDaubCells = card.getNoDaubCells();
        const cells = card.pickRandomCellsWithMutation(noDaubCells, PINE_NUTS_COUNT);
        let idx = 0;
        for (let i = 0; i < PINE_NUTS_COUNT; i++) {
            cells[idx++].pushEffect(CellEffect.PineNuts, 0, {count: 1});
        }
    });
};


const bingoTester: CardBingoTester = card => {
    if (readCounter(card) >= getCounterTarget(card)) return true;
    return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
    handleDaubByPowerup(progress, card, cell, effect);
};

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
    let addCount = 0;
    const effect = cell.findEffect(CellEffect.PineNuts);
    if (effect) {
        cell.updateEffectValue(CellEffect.PineNuts, 1);
        const cardEffect = card.findEffect(CardEffect.Squirrel);
        if (cardEffect) {
            const customData = cardEffect[2];
            const squirrelChangeIndex: number[] = [];
            const squirrelChangeY: number[] = [];
            let allDistance = 0;
            for (let i = 0; i < customData.length; i++) {
                if (customData[i].indexes.length < 2 && (cell.x === customData[i].position.x || cell.x + 1 === customData[i].position.x)) {
                    const distance = Math.abs(customData[i].position.y - cell.y);
                    if (distance <= allDistance || squirrelChangeIndex.length === 0) {
                        if (distance < allDistance && squirrelChangeIndex.length > 0) {
                            squirrelChangeIndex.splice(0, 1);
                            squirrelChangeY.splice(0, 1);
                        }
                        allDistance = distance;
                        squirrelChangeIndex.push(i);
                        squirrelChangeY.push(cell.y);
                    }
                }
            }
            if (squirrelChangeIndex.length >= 1) {
                //如果有条松鼠，随机一个
                let randomIndex = 0;
                if (squirrelChangeIndex.length >= 3) {
                    console.log(squirrelChangeIndex);
                }
                if (squirrelChangeIndex.length >= 2) {
                    randomIndex = randomInt(0, 1, card);
                }
                customData[squirrelChangeIndex[randomIndex]].position.y = squirrelChangeY[randomIndex];
                customData[squirrelChangeIndex[randomIndex]].indexes.push(cell.index);
                card.updateEffectValue(CardEffect.Squirrel, 0, customData);
                if (customData[squirrelChangeIndex[randomIndex]].indexes.length >= COLLECT_PINE_NUTS) {
                    addCount += 1;
                    removeCellPineNuts(card, squirrelChangeIndex[randomIndex], customData[squirrelChangeIndex[randomIndex]].position.x, customData);
                }
            }
        }
    }
    if (addCount > 0) {
        updateCounter(card, readCounter(card) + addCount);
    }
};
//去掉cell周围没有松鼠的松子
const removeCellPineNuts = (card: Card, customIndex: number, x: number, customData: CustomData[]) => {
    let cells: CardCell[] = [];
    //判断掉落松鼠左侧的是否有松鼠
    if (x - 1 === 0) {
        cells = cells.concat(card.getCardsXEdgeCells(x - 1));
    }
    if (x - 1 > 0) {
        if (customData[customIndex - 1].indexes.length >= COLLECT_PINE_NUTS) {
            cells = cells.concat(card.getCardsXEdgeCells(x - 1));
        }
    }
    //判断落松鼠右侧的是否有松鼠
    if (x < CardCell.cellStepCount - 1) {
        if (customData[customIndex + 1].indexes.length >= COLLECT_PINE_NUTS) {
            cells = cells.concat(card.getCardsXEdgeCells(x));
        }
    }
    //因为松鼠的x 是从1开始的
    if (x === CardCell.cellStepCount - 1) {
        cells = cells.concat(card.getCardsXEdgeCells(x));
    }
    for (let i = 0; i < cells.length; i++) {
        cells[i].pullAllEffect(CellEffect.PineNuts);
    }
};
const farsee: Farsee = card => {
    const naturalBingoResult = {
        naturalBingo: NaturalBingoFlag.Yes,
        daubOnceTarget: -1,
        daubTwiceTarget: [-1, -1] as [number, number]
    };

    if (card.canBingo) return naturalBingoResult;

    const onceTarget: number | null = null;
    const twiceTarget: [number, number] | null = null;

    return {
        naturalBingo: NaturalBingoFlag.No,
        daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
        daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
    };
};
const initializeSquirrel = (card: Card, num: number) => {
    const cdList: CustomData[] = [];
    for (let i = 1; i <= num; i++) {
        const y = randomInt(0, 4, card);
        const cb = {
            position: {x: i, y},
            indexes: [],
        };
        cdList.push(cb);
    }
    card.pushEffect(CardEffect.Squirrel, 0, cdList);
};
export const suppliesWinter: IBingoRule = {
    initializeCardEffect,
    initializeCellEffect,
    bingoTester,
    beforeCellMeetEffect,
    afterCellDaub,
    farsee
};

import _ from 'lodash';
import { Card, CardBingoTester, CardCell, CardEffect, CellEffect, NaturalBingoFlag, RandomSequence } from '..';
import { handleDaubByPowerup, initializeBoxAndTicketRewardsAndOthers, initializeCollectionRewardsAkaShadowCard, initializeCounter, readCounter, updateCounter } from './common';
import { CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer } from './IBingoRule';

///// Tile Matching /////
export const Constant = Object.freeze({
  ScoreGoal: 320,
  ScorePerTile: 10,
});

export enum Color {
  A = 1000, B = 1001, C = 1002, D = 1003, E = 1004,
}

export type DaubClue = {
  before: number[];
  sameColors: number[];
  distances: [index: number, distance: number][];
}

const randomColor = (rs: RandomSequence) => rs.nextIntegerInRange(4) + 1000 as Color;

const getColor = (cell: CardCell) => cell.findEffectValue(CellEffect.Tag) as Color;
const updateColor = (cell: CardCell, color: Color) => cell.updateEffectValue(CellEffect.Tag, color);

export const findSameColors = (cell: CardCell, card: Card): CardCell[] => {
  const targetColor = getColor(cell);
  const targets: CardCell[] = [cell];
  const checkedDict: Record<number, boolean> = { [cell.index]: true };
  const searchQueue: CardCell[] = [cell];
  let index = 0;

  while (index < searchQueue.length) {
    const originCell = searchQueue[index];
    const uncheckedAdjacentCells = _.filter(card.getEdgeAdjacentCells(originCell), c => !checkedDict[c.index]);
    _.forEach(uncheckedAdjacentCells, ac => {
      checkedDict[ac.index] = true;

      if (getColor(ac) !== targetColor) return;

      targets.push(ac);
      searchQueue.push(ac);
    });

    index += 1;
  }

  return targets;
};

const flushTiles = (clearedCells: CardCell[], card: Card, rs: RandomSequence): [index: number, distance: number][] => {
  const distances: [number, number][] = [];
  _(clearedCells).groupBy(cell => cell.x).toPairs()
    .map(([, cells]) => _.sortBy(cells, cell => cell.y))
    .forEach(clearedCellsThisColumn => {
      const x = clearedCellsThisColumn[0].x;

      const flushDistance = [0, 0, 0, 0, 0];

      _.forEach(clearedCellsThisColumn, affectedCell => {
        _.times(flushDistance.length - affectedCell.y - 1, i => {
          flushDistance[affectedCell.y + i + 1] += 1;
        });
      });

      const survivors = _(_.range(5)).difference(_.map(clearedCellsThisColumn, c => c.y)).map(y => card.cellAtLocation(x, y)) as _.Collection<CardCell>;

      survivors.forEach(survivor => {
        const distance = flushDistance[survivor.y];
        updateColor(card.cellAtLocation(x, survivor.y - distance) as CardCell, getColor(survivor));

        if (distance > 0) distances.push([survivor.index, distance]);
      });

      const newColorCount = clearedCellsThisColumn.length;
      _.times(newColorCount, i => {
        updateColor(card.cellAtLocation(x, 4 - i) as CardCell, randomColor(rs));
      });
    });

  return distances;
};

const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);

  _.forEach(progress.cards, card => {
    initializeCounter(card, Constant.ScoreGoal);
  });
};

const initializeCellEffect: RoundInitializer = (preset, progress) => {
  initializeBoxAndTicketRewardsAndOthers(preset, progress);

  _.forEach(progress.cards, card => {
    const rs = new RandomSequence(card.seed);
    _.forEach(card.cells, cell => {
      cell.pushEffect(CellEffect.Tag, randomColor(rs));
    });
  });
};

const bingoTester: CardBingoTester = card => {
  if (card.findEffectValue(CardEffect.Counter) as number >= (card.findEffectValue(CardEffect.CounterTarget) as number)) return true;
  return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const sameColors = findSameColors(cell, card);
  const thisDaubScore = sameColors.length * Constant.ScorePerTile;

  const currentScore = readCounter(card);
  updateCounter(card, currentScore + thisDaubScore);

  const rs = new RandomSequence(card.seed + cell.index);
  const distances = flushTiles(sameColors, card, rs);

  progress.recordClue(card.index, (): DaubClue => ({
    before: _.map(card.cells, c => getColor(c)),
    sameColors: _.map(sameColors, c => c.index),
    distances: distances,
  }));
};

const farsee: Farsee = card => {
  const naturalBingoResult = { naturalBingo: NaturalBingoFlag.Yes, daubOnceTarget: -1, daubTwiceTarget: [-1, -1] as [number, number] };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const tileMatching: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

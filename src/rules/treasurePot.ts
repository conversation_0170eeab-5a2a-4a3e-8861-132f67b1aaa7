import {
    Card,
    CardCell,
    CardEffect,
    CellEffect,
    getRandomArray,
    NaturalBingoFlag,
    PlayerRoundProgress,
    randomInt
} from '..';
import {CardBingoTester} from '../Card';
import {
    getCounterTarget,
    handleDaubByPowerup,
    initializeBoxAndTicketRewardsAndOthers,
    initializeCardSeed,
    initializeCollectionRewardsAkaShadowCard,
    initializeCounter,
    readCounter,
    updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';
import _ from "lodash";
import Progress = Mocha.reporters.Progress;

const COLLECT_POT = 3;//收集聚宝盆个数
const POT_NEED_DAUB_CNT = 5;//激活聚宝盆，需要额外涂抹次数
const POT_AUTO_DAUB_CNT = 4;//激活聚宝盆后，自动涂抹次数
const BURY_COINS_RATE = 20;//埋钱币的机率
const POT_ADD_TARGET = 1;//一个聚宝盆加目标1
const PotState = {//value的设定 0: 未展示, 1: 已展示, 2: 已激活
    NoDaub: 0,
    Daub: 1,
    Activated: 2,
};

///// Treasure Pot ///// 聚宝盆
const initializeCardEffect: RoundInitializer = (preset, progress) => {
    initializeCollectionRewardsAkaShadowCard(preset, progress);
    progress.cards.forEach(card => {
        initializeCardSeed(card, card.seed);
        initializeCounter(card, COLLECT_POT);
        initializePotDaubInfo(card);
    });
};
const initializePotDaubInfo = (card: Card) => {
    /**
     * daubList记录即将需要涂抹的
     * nextDaubList 记录下一层需要涂抹的
     * value 记录当前涂抹的index
     */
    const daubList: CardCell[] = [];
    const nextDaubList: CardCell[] = [];
    card.pushEffect(CardEffect.PotDaubInfo, 0, {daubList, nextDaubList, loopNum: 0});
};
const initializeCellEffect: RoundInitializer = (host, progress) => {
    initializeBoxAndTicketRewardsAndOthers(host, progress);
    progress.cards.forEach(card => {
        const noDaubCells = card.getNoDaubCells();
        const potIndex: number[] = [];
        for (let i = 0; i < COLLECT_POT; i++) {
            const listIndex = randomInt(0, noDaubCells.length - 1, card);
            const cellIndex = noDaubCells[listIndex].index;
            if (potIndex.indexOf(cellIndex) != -1) {
                i--;
                continue;
            }
            potIndex.push(cellIndex);
        }
        _.forEach(noDaubCells, cell => {
            if (potIndex.indexOf(cell.index) != -1) {
                /**
                 * value的设定 0: 未展示, 1: 已展示, 2: 已激活
                 * total: 需要多少次涂抹才激活
                 * cnt: 当前已积累几次涂抹
                 * daubIndexList: 激活后会触发涂抹的格子index
                 * operationIndex progress的哪一层，其实应该记录在card
                 */
                cell.pushEffect(CellEffect.TreasurePot, 0, {
                    cnt: POT_NEED_DAUB_CNT,
                    daubIndexList: [],
                    operationIndex: 0
                });
            } else {
                const rate = randomInt(0, 100, card);
                if (rate <= BURY_COINS_RATE) {
                    cell.pushEffect(CellEffect.CoinTool, 0);
                }
            }
            /**
             * addPowerIndexList 需要充能聚宝盆的index
             * value 记录格子自身的所在的层
             */
            cell.pushEffect(CellEffect.TreasurePotAddPower, -1, {addPowerIndexList: [], isAutoDaub: false});
        });
    });
};


const bingoTester: CardBingoTester = card => {
    if (readCounter(card) >= getCounterTarget(card)) return true;
    return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
    handleDaubByPowerup(progress, card, cell, effect);
};

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
    const operationIndex = progress.operationIndex;
    const daubPotEffect = cell.findEffect(CellEffect.TreasurePot);
    const addPowerEffect = cell.findEffect(CellEffect.TreasurePotAddPower);
    if (daubPotEffect && daubPotEffect[1] == PotState.NoDaub) {
        daubPotEffect[1] = PotState.Daub;
        daubPotEffect[2].operationIndex = operationIndex;
    }
    if (addPowerEffect && addPowerEffect[1] === -1) {//把整体的index赋值到 格子上
        addPowerEffect[1] = operationIndex;
    }
    //增加聚宝激活次数
    const potCells = card.getEffectCells(CellEffect.TreasurePot);
    _.forEach(potCells, (potCell) => {
        const potEffect = potCell.findEffect(CellEffect.TreasurePot);
        const potAddPowerEffect = potCell.findEffect(CellEffect.TreasurePotAddPower);
        //如果是回掉涂抹的激活聚宝盆充能条件（不是回掉涂抹的聚宝盆）
        if (potAddPowerEffect && addPowerEffect && potEffect && potEffect[1] === PotState.Daub && (potEffect[2].operationIndex < operationIndex ||
            (potEffect[2].operationIndex === operationIndex && potAddPowerEffect[1] < addPowerEffect[1]))) {
            potEffect[2].cnt -= 1;
            if (addPowerEffect) {
                addPowerEffect[2].addPowerIndexList.push(potCell.index);
            }
            if (potEffect[2].cnt <= 0) {//激活的聚宝盆
                updateCounter(card, readCounter(card) + POT_ADD_TARGET);
                potCell.updateEffectValue(CellEffect.TreasurePot, PotState.Activated, potEffect[2]);
                const noDaubCells = getNoAutoDaubCells(card);
                if (noDaubCells.length > 0) {
                    const cellIndexList: number[] = [];
                    _.forEach(noDaubCells, noDaubCell => {
                        cellIndexList.push(noDaubCell.index);
                    });
                    const randomIndexes = getRandomArray(cellIndexList, POT_AUTO_DAUB_CNT, card);//随机几个格子
                    const cardPotDaubInfo = card.findEffect(CardEffect.PotDaubInfo);
                    for (let i = 0; i < randomIndexes.length; i++) {
                        potEffect[2].daubIndexList.push(randomIndexes[i]);
                        const cellsListIndex = cellIndexList.indexOf(randomIndexes[i]);
                        const noDaubAddPowerEffect = noDaubCells[cellsListIndex].findEffect(CellEffect.TreasurePotAddPower);
                        if (noDaubAddPowerEffect) {//改变层级
                            //addPowerEffect[1]+1，给即将涂抹的格子上的层加一
                            noDaubAddPowerEffect[2].isAutoDaub = true;
                            noDaubCells[cellsListIndex].updateEffectValue(CellEffect.TreasurePotAddPower, addPowerEffect[1] + 1, noDaubAddPowerEffect[2]);
                        }
                        if (cardPotDaubInfo) {
                            if (cardPotDaubInfo[2].daubList.length >= POT_AUTO_DAUB_CNT) {//为了先把一个层级的涂抹完。
                                cardPotDaubInfo[2].nextDaubList.push(noDaubCells[cellsListIndex]);
                            } else {
                                cardPotDaubInfo[2].daubList.push(noDaubCells[cellsListIndex]);
                            }
                        }
                    }
                }
            }
        }
    });
    autoDaub(progress, card);
};
//把所有自动涂抹的同一个等级,先涂抹完
const autoDaub = (progress: PlayerRoundProgress, card: Card) => {
    const cardPotDaubInfo = card.findEffect(CardEffect.PotDaubInfo);
    if (cardPotDaubInfo) {
        if (cardPotDaubInfo[1] < cardPotDaubInfo[2].daubList.length) {
            const daubIndex = cardPotDaubInfo[1];
            cardPotDaubInfo[1]++;//不先加会死循环
            cardPotDaubInfo[2].loopNum++;
            if (cardPotDaubInfo[2].loopNum > 100) {
                console.error("loopNum > 100");
            }
            progress.forceDaub(card, cardPotDaubInfo[2].daubList[daubIndex], false, false);
            if (cardPotDaubInfo[1] === cardPotDaubInfo[2].daubList.length) {
                if (cardPotDaubInfo[2].nextDaubList.length > 0) {//涂抹下一层的celle
                    cardPotDaubInfo[1] = 0;//index 从0开始
                    cardPotDaubInfo[2].daubList = cardPotDaubInfo[2].nextDaubList;
                    cardPotDaubInfo[2].nextDaubList = [];
                    autoDaub(progress, card);
                } else {
                    cardPotDaubInfo[1] = 0;//index 从0开始
                    cardPotDaubInfo[2].daubList = [];
                }
            }
        }
    }
};

const getNoAutoDaubCells = (card: Card): CardCell[] => {
    const noDaubCells = card.getNoDaubCells();
    const noNoAutoDaubCells = _.filter(noDaubCells, (cell) => {
        const noDaubAddPowerEffect = cell.findEffect(CellEffect.TreasurePotAddPower);
        if (noDaubAddPowerEffect && !noDaubAddPowerEffect[2].isAutoDaub) {
            return true;
        }
        return false;
    });
    return noNoAutoDaubCells;
};
const farsee: Farsee = card => {
    const naturalBingoResult = {
        naturalBingo: NaturalBingoFlag.Yes,
        daubOnceTarget: -1,
        daubTwiceTarget: [-1, -1] as [number, number]
    };

    if (card.canBingo) return naturalBingoResult;

    const onceTarget: number | null = null;
    const twiceTarget: [number, number] | null = null;

    return {
        naturalBingo: NaturalBingoFlag.No,
        daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
        daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
    };
};
export const treasurePot: IBingoRule = {
    initializeCardEffect,
    initializeCellEffect,
    bingoTester,
    beforeCellMeetEffect,
    afterCellDaub,
    farsee
};

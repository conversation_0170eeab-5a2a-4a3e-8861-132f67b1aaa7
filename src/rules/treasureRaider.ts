import {Card, CellEffect, NaturalBingoFlag, pickRandomItemsWithMutation, RandomSequence} from '..';
import {CardBingoTester} from '../Card';
import {
  getCounterTarget,
  handleDaubByPowerup,
  initializeBoxAndTicketRewardsAndOthers,
  initializeCollectionRewardsAkaShadowCard,
  initializeCounter,
  readCounter,
  updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';
import _ from "lodash";

const BOX_COUNT = 3;//箱子个数
const KEY_COUNT = 3;//钥匙个数

enum BoxState { None, Got, Open }

enum KeyState { None, Got, Open}


///// Rule treasure raider /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);
  progress.cards.forEach(card => {
    initializeCounter(card, 6);
  });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
  initializeBoxAndTicketRewardsAndOthers(host, progress);
  _.forEach(progress.cards, card => {
    const rs = new RandomSequence(card.seed);
    const cells = pickRandomItemsWithMutation(card.getNoDaubCells(), 6, rs);
    //随机一个宝石位置
    const gemIndex = rs.nextIntegerInRange(2);
    for (let i = 0; i < BOX_COUNT; i++) {
      cells[i].pushEffect(CellEffect.TreasureBox, BoxState.None);
      if (i === gemIndex) {
        cells[i].pushEffect(CellEffect.TreasureGem, 0);
      }
    }
    for (let i = 0; i < KEY_COUNT; i++) {
      cells[BOX_COUNT + i].pushEffect(CellEffect.TreasureKey, KeyState.None);
    }
  });
};


const bingoTester: CardBingoTester = card => {
  if (readCounter(card) >= getCounterTarget(card)) return true;
  return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};

const readBoxCount = (card: Card): number => {
  let boxCount = 0;
  _.forEach(card.cells, cell => {
    if (cell.hasEffect(CellEffect.TreasureKey, KeyState.Got) || cell.hasEffect(CellEffect.TreasureKey, KeyState.Open)) {
      boxCount++;
    }
  });
  return boxCount;
};

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const count = readCounter(card);
  if (cell.hasEffect(CellEffect.TreasureBox, BoxState.None)) {
    let state = BoxState.Got;
    let keyIdx = -1;
    for (const c of card.cells) {
      if (c.hasEffect(CellEffect.TreasureKey, KeyState.Got)) {
        c.updateEffectValue(CellEffect.TreasureKey, KeyState.Open, {index: cell.index});
        keyIdx = c.index
        state = BoxState.Open;
        if (cell.hasEffect(CellEffect.TreasureGem)) {
          cell.updateEffectValue(CellEffect.TreasureGem, 1);
        }
        break;
      }
    }
    cell.updateEffectValue(CellEffect.TreasureBox, state, {index: keyIdx, count: readBoxCount(card)});
    updateCounter(card, count + 1);
  } else if (cell.hasEffect(CellEffect.TreasureKey, KeyState.None)) {
    let state = KeyState.Got;
    const boxCells = card.cells.filter(c => c.hasEffect(CellEffect.TreasureBox, BoxState.Got));
    boxCells.sort((a, b) => {
      const effect1 = a.findEffect(CellEffect.TreasureBox);
      const effect2 = b.findEffect(CellEffect.TreasureBox);
      if (effect1 && effect2) {
        return effect1[2].count - effect2[2].count;
      }
      return 0;
    });
    let boxIdx = -1; 
    if (boxCells.length > 0) {
      state = KeyState.Open;
      boxCells[0].updateEffectValue(CellEffect.TreasureBox, BoxState.Open, {index: cell.index});
      boxIdx = boxCells[0].index
      if (boxCells[0].hasEffect(CellEffect.TreasureGem)) {
        boxCells[0].updateEffectValue(CellEffect.TreasureGem, 1);
      }
    }
    cell.updateEffectValue(CellEffect.TreasureKey, state, { index: boxIdx});
    updateCounter(card, count + 1);
  }
};

const farsee: Farsee = card => {
  const naturalBingoResult = {
    naturalBingo: NaturalBingoFlag.Yes,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const treasureRaider: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee
};

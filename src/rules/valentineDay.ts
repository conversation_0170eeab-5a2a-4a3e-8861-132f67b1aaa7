import {
  Card,
  CardCell,
  CellEffect,
  NaturalBingoFlag,
  PlayerRoundProgress,
  randomFloat,
  shuffleEffectArr,
  weightRandom
} from '..';
import {CardBingoTester} from '../Card';
import {
  getCounterTarget,
  handleDaubByPowerup,
  initializeBoxAndTicketRewardsAndOthers,
  initializeCardSeed,
  initializeCollectionRewardsAkaShadowCard,
  initializeCounter,
  readCounter,
  updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

const HEART_RATE = 0.3;
const MAGIC_RATE = [0.3, 0.3, 0.4];

///// Rule valentine day /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);
  progress.cards.forEach(card => {
    initializeCardSeed(card, card.seed);
    initializeCounter(card, 12);
  });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
  initializeBoxAndTicketRewardsAndOthers(host, progress);
  progress.cards.forEach(card => {
    //放置初始目标 3 个 2*2的块
    setTargetArea(card);
    //放置爱心
    card.cells.forEach(cell => {
      if (randomFloat(0, 1, card) < HEART_RATE) {
        //随机对应的技能
        const index = weightRandom(MAGIC_RATE, card);
        cell.pushEffect(CellEffect.ValentineHeart, 0, {type: index + 1});
      }
    });

  });
};

const setTargetArea = (card: Card) => {
  const TARGET_EFFECT = [CellEffect.ValentineChocolate, CellEffect.ValentineRing, CellEffect.ValentineRose];
  const cells: number[] = [];
  for (let i = 0; i < 4; i++) {
    for (let j = 0; j < 4; j++) {
      cells.push(i * CardCell.cellStepCount + j);
    }
  }
  shuffleEffectArr(cells, card);

  const picked: number[] = [];
  let used: number[] = [];
  let i = 0;
  while (picked.length < 3) {
    const cur = cells[i];
    if (!used.includes(cur)) {
      picked.push(cur);
      used = used.concat([cur]).concat(card.getAdjacentCellIndex(cur, 1));
    }
    i++;
  }
  TARGET_EFFECT.forEach((v, i) => {
    const vertex = card.cellAtIndex(picked[i]);
    vertex.pushEffect(TARGET_EFFECT[i], 0);
    (card.cellAtLocation(vertex.x, vertex.y + 1) as CardCell).pushEffect(TARGET_EFFECT[i], 0);
    (card.cellAtLocation(vertex.x + 1, vertex.y) as CardCell).pushEffect(TARGET_EFFECT[i], 0);
    (card.cellAtLocation(vertex.x + 1, vertex.y + 1) as CardCell).pushEffect(TARGET_EFFECT[i], 0);
  });
};


const bingoTester: CardBingoTester = card => {
  return readCounter(card) >= getCounterTarget(card);

};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};

const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const TARGET_EFFECT = [CellEffect.ValentineChocolate, CellEffect.ValentineRing, CellEffect.ValentineRose];
  let addCount = 0;
  const heartEffect = cell.findEffect(CellEffect.ValentineHeart);
  if (heartEffect) {
    cell.updateEffectValue(CellEffect.ValentineHeart, 1);
    triggerMagic(progress, card, cell, heartEffect[2].type);
  }
  cell.effects.forEach(([type]) => {
    if (TARGET_EFFECT.includes(type)) {
      cell.updateEffectValue(type, 1);
      addCount++;
    }
  });
  if (addCount > 0) {
    updateCounter(card, readCounter(card) + addCount);
  }
};
const triggerMagic = (progress: PlayerRoundProgress, card: Card, cell: CardCell, type: number) => {
  let cells: CardCell[] = [];
  //避免同时涂抹多个时 后续层级涂抹时前面的还未涂抹 导致筛选出问题
  const noDaubCells = card.cells.filter(cell => !cell.isDaubed && !cell.findEffectValue(CellEffect.MarkAsDaubByEffect));
  const count = noDaubCells.length;
  if (count > 0) {
    switch (type) {
        //随机消除两个格子
      case 1:
        cells = card.pickRandomCellsWithMutation(noDaubCells, Math.min(count, 2));
        break;
        //随机消除三个格子
      case 2:
        cells = card.pickRandomCellsWithMutation(noDaubCells, Math.min(count, 3));
        break;
        //随机消除上下左右四个格子
      case 3:
        cells = card.getEdgeAdjacentCells(cell);
        break;
    }
    if (cells.length > 0) {
      cells.forEach(c => c.pushEffect(CellEffect.MarkAsDaubByEffect, 1));
      for (const c of cells) {
        progress.forceDaub(card, c, false, false);
      }
    }
  }
};

const farsee: Farsee = card => {
  const naturalBingoResult = {
    naturalBingo: NaturalBingoFlag.Yes,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const valentineDay: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee
};

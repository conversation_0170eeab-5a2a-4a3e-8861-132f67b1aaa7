import _ from 'lodash';
import {
  Card,
  CardCell,
  CellEffect,
  NaturalBingoFlag,
  pickRandomItemsWithMutation,
  randomOneItemByWeights,
  RandomSequence
} from '..';
import {CardBingoTester} from '../Card';
import {
  getCounterTarget,
  handleDaubByPowerup,
  initializeBoxAndTicketRewardsAndOthers,
  initializeCollectionRewardsAkaShadowCard,
  initializeCounter,
  readCounter, updateCounter
} from './common';
import {CellDaubedHandler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

const WATERMELON_GOAL = 8;//收集西瓜的总个数
enum WatermelonState { Sprout, Flower, Melon}//🍉状态 苗 花 果
enum GoldWatermelonState { Small, Big, Boom}//金🍉状态


///// Rule 收集西瓜 /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
  initializeCollectionRewardsAkaShadowCard(preset, progress);

  _.forEach(progress.cards, card => {
    initializeCounter(card, WATERMELON_GOAL);
  });
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
  initializeBoxAndTicketRewardsAndOthers(host, progress);
};


const bingoTester: CardBingoTester = card => {
  if (readCounter(card) >= getCounterTarget(card)) return true;
  return false;
};

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => {
  handleDaubByPowerup(progress, card, cell, effect);
};


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
  const DaubWeight = [
    {type: CellEffect.Watermelon, weight: 70},
    {type: CellEffect.GoldWatermelon, weight: 15},
    {type: CellEffect.Scarecrow, weight: 15},
  ];
  const rs = new RandomSequence(card.seed + cell.index);
  if (!cell.hasEffect(CellEffect.GoldWatermelon)) {
    const {type} = randomOneItemByWeights(DaubWeight, rs);
    cell.pushEffect(type, 0);
  }
  effectAdjacentCell(card, cell);
  if (cell.hasEffect(CellEffect.Scarecrow)) {
    //若点开稻草人则随机涂抹一个格子 生成一个金西瓜
    const noDaubCells = card.getNoDaubCells();
    if (noDaubCells.length > 0) {
      const nextGold = pickRandomItemsWithMutation(noDaubCells, 1, rs)[0];
      nextGold.pushEffect(CellEffect.GoldWatermelon, GoldWatermelonState.Small);
      nextGold.pushEffect(CellEffect.MarkAsDaubByScarecrow, cell.index);
      progress.forceDaub(card, nextGold);
    }

  }
};

const effectAdjacentCell = (card: Card, cell: CardCell, type?: number) => {
  const range = type == CellEffect.Watermelon ? 2 : 1;
  const adjacentCells: CardCell[] = card.getAdjacentCells(cell, range);
  _.forEach(adjacentCells, c => {
    if (!c.isDaubed) return;
    let val;
    if ((val = c.findEffectValue(CellEffect.Watermelon)) != undefined) {
      val++;
      //变成大西瓜后改成西瓜苗 更新计数器
      if (val === WatermelonState.Melon) {
        val = WatermelonState.Sprout;
        updateCounter(card, readCounter(card) + 1);
      }
      c.updateEffectValue(CellEffect.Watermelon, val);
    } else if (!type && (val = c.findEffectValue(CellEffect.GoldWatermelon)) != undefined) {
      if (val < GoldWatermelonState.Boom) {
        val++;
        c.updateEffectValue(CellEffect.GoldWatermelon, val);
        if (val == GoldWatermelonState.Boom) {
          effectAdjacentCell(card, c, CellEffect.Watermelon);
        }
      }
    }
  });
};


const farsee: Farsee = card => {
  const naturalBingoResult = {
    naturalBingo: NaturalBingoFlag.Yes,
    daubOnceTarget: -1,
    daubTwiceTarget: [-1, -1] as [number, number]
  };

  if (card.canBingo) return naturalBingoResult;

  const onceTarget: number | null = null;
  const twiceTarget: [number, number] | null = null;

  return {
    naturalBingo: NaturalBingoFlag.No,
    daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
    daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
  };
};

export const watermelon: IBingoRule = {
  initializeCardEffect,
  initializeCellEffect,
  bingoTester,
  beforeCellMeetEffect,
  afterCellDaub,
  farsee,
};

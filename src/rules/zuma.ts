import _ from 'lodash';
import {Card, CardEffect, NaturalBingoFlag, randomInt} from '..';
import {CardBingoTester} from '../Card';
import {
    handleDaubByPowerup, initializeBoxAndTicketRewardsAndOthers, initializeCardSeed,
    initializeCollectionRewardsAkaShadowCard,
    initializeCounter,
    readCounter,
    updateCounter
} from './common';
import {CellDaubed<PERSON>andler, CellEffectHandler, Farsee, IBingoRule, RoundInitializer} from './IBingoRule';

const BINGO_TARGET = 1000;
const BASE_POINT = 20;
const EXTRA_ERASE_POINT_RATIO = 0.1;
const EXTRA_STRIKE_POINT_RATIO = 0.2;

///// Rule Zuma /////
const initializeCardEffect: RoundInitializer = (preset, progress) => {
    initializeCollectionRewardsAkaShadowCard(preset, progress);

    progress.cards.forEach(card => {
        initializeCardSeed(card, card.seed);
        initializeCounter(card, BINGO_TARGET);
        initializeBalls(card, 100);
    });
};

/**
 *   20 19 10 9 0
 *   21 18 11 8 1
 *   22 17 12 7 2
 *   23 16 13 6 3
 *   24 15 14 5 4
 * */

const INDEX_TO_BALLS = [20, 21, 22, 23, 24, 19, 18, 17, 16, 15, 10, 11, 12, 13, 14, 9, 8, 7, 6, 5, 0, 1, 2, 3, 4];

const print = (card: Card, balls: number[]) => {
    const order = [
        0, 5, 10, 15, 20,
        1, 6, 11, 16, 21,
        2, 7, 12, 17, 22,
        3, 8, 13, 18, 23,
        4, 9, 14, 19, 24,
    ];
    const ballStr = ['A', 'B', 'C', 'D', 'E'];
    let curStr = '';
    let cnt = 0;
    for (const i of order) {
        if (cnt % 5 === 0) {
            curStr = '';
        }
        const cell = card.cells[i];
        let cellNum = cell.value.toString();
        if (cellNum.length === 1) {
            cellNum = ' ' + cellNum;
        }

        let ballPos = -1;
        for (let j = 0; j < INDEX_TO_BALLS.length; j++) {
            if (INDEX_TO_BALLS[j] === i) {
                ballPos = j;
            }
        }
        const ball = ballStr[balls[ballPos]];
        if (cell.isDaubed) {
            cellNum = ball.repeat(4);
        } else {
            cellNum = ball + cellNum + ball;
        }
        cnt++;
        curStr += ' ' + cellNum;
        if (cnt % 5 === 0) {
            console.log(curStr);
        }
    }
    console.log('\n');
};

const initializeBalls = (card: Card, num: number) => {
    // 0,1,2,3,4 分别代表 红、黄、绿、蓝、紫
    const balls: number[] = []; // 按照card样式和补球顺序来的顺序,服务器使用
    for (let i = 0; i < num; i++) {
        balls.push(randomInt(0, 4, card));
    }

    card.pushEffect(CardEffect.MazuBalls, 0, {balls});
};

const initializeCellEffect: RoundInitializer = (host, progress) => {
    initializeBoxAndTicketRewardsAndOthers(host, progress);
};

const bingoTester: CardBingoTester = card => readCounter(card) >= BINGO_TARGET;

const beforeCellMeetEffect: CellEffectHandler = (progress, card, cell, effect) => handleDaubByPowerup(progress, card, cell, effect);


const afterCellDaub: CellDaubedHandler = (progress, card, cell) => {
    const ballsData = card.findCustomData<{ balls: number[] }>(CardEffect.MazuBalls);
    if (!ballsData) {
        throw new Error('Card has no Balls!.');
    }
    const {balls} = ballsData;

    let lastDaubPos = INDEX_TO_BALLS[cell.index];

    let eraseTimes = 0;

    const retData: { balls: number[], erase: [number, number], score: number }[] = [];

    let curRoundFinalScore = readCounter(card);

    // print(card, balls);

    // eslint-disable-next-line no-constant-condition
    while (true) {
        // 1. 检查当前是否为第一轮,或者消除后的位置的左边一个位置的球颜色等不等于消除位置右边的第一个球
        if (eraseTimes != 0 && !(lastDaubPos - 1 >= 0 && balls[lastDaubPos - 1] === balls[lastDaubPos])) {
            break;
        }

        const nowColor = balls[lastDaubPos];
        eraseTimes++;

        // 2. 找到与被消除的球相连的同颜色的球
        let curIdx = lastDaubPos - 1;
        let firstBallIdx = lastDaubPos;
        while (curIdx >= 0 && balls[curIdx] == nowColor) {
            firstBallIdx = curIdx;
            curIdx--;
        }
        curIdx = lastDaubPos + 1;
        let lastBallIdx = lastDaubPos;
        while (curIdx <= 25 && balls[curIdx] == nowColor) {
            lastBallIdx = curIdx;
            curIdx++;
        }

        const ballsToErase: number[] = [];
        for (let i = firstBallIdx; i <= lastBallIdx; i++) {
            ballsToErase.push(i);
        }

        lastDaubPos = firstBallIdx;

        eraseTimes++;

        // 3. 消除这几个球
        balls.splice(firstBallIdx, ballsToErase.length);
        // print(card, balls);

        const score = Math.ceil(BASE_POINT * ballsToErase.length * (1 + (ballsToErase.length - 1) * EXTRA_ERASE_POINT_RATIO) * (1 + (eraseTimes - 1) * EXTRA_STRIKE_POINT_RATIO));
        curRoundFinalScore = curRoundFinalScore + score;
        retData.push({
            balls: [...balls],
            erase: [firstBallIdx, ballsToErase.length],
            score: curRoundFinalScore,
        });
    }

    updateCounter(card, curRoundFinalScore);


    if (balls.filter(a => _.isNumber(a)).length < 26) {
        throw new Error('球数量不够');
    }

    card.updateEffectValue(CardEffect.MazuBalls, 0, {balls, retData});
};


const farsee: Farsee = card => {
    const naturalBingoResult = {
        naturalBingo: NaturalBingoFlag.Yes,
        daubOnceTarget: -1,
        daubTwiceTarget: [-1, -1] as [number, number]
    };

    if (card.canBingo) return naturalBingoResult;

    const onceTarget: number | null = null;
    const twiceTarget: [number, number] | null = null;

    return {
        naturalBingo: NaturalBingoFlag.No,
        daubOnceTarget: onceTarget === null ? naturalBingoResult.daubOnceTarget : onceTarget,
        daubTwiceTarget: twiceTarget === null ? naturalBingoResult.daubTwiceTarget : twiceTarget,
    };
};
export const zuma: IBingoRule = {
    initializeCardEffect,
    initializeCellEffect,
    bingoTester,
    beforeCellMeetEffect,
    afterCellDaub,
    farsee,
};

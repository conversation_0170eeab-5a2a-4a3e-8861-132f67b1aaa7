
const aws_region = process.env.AWS_REGION || 'localhost';
const isLambdaEnv = aws_region !== 'localhost';
const winston = require('winston');
const _ = require('lodash');

// lambda环境默认日志等级为info，除非模块有特殊设置
const env_lambdaLogLevel = process.env.lambdaLogLevel;
const lambdaLogLevel = isLambdaEnv ?
    // lambda环境默认等级info
    (_.includes(['debug', 'warn', 'error', 'info'], env_lambdaLogLevel) ? env_lambdaLogLevel : 'info') :
    // 测试环境默认等级debug
    env_lambdaLogLevel ? env_lambdaLogLevel : 'debug';

// @ts-ignore
const winstonInfoFormat = winston.format(info => {
    if (info.e && info.e instanceof Error) {
        // if (!c.isLambdaEnv) {
        //     console.error("error(no lambda env).\n", info);
        // }
        console.error(info);
        // stack的截取需要按实际日志进行控制
        if (info.e.stack) {
            info.e.stack = _.truncate(info.e.stack, {length: 1000});
        }
    }
    if (info.context) {
        info.awsRequestId = info.context.awsRequestId;
        delete info.context;
    }
    return info;
});

// 初始化logger，根据lambda和测试环境，初始化不同的logger
const winstonLogger = isLambdaEnv ? winston.createLogger({
    level: lambdaLogLevel,
    format: winston.format.combine(winstonInfoFormat(), winston.format.json()),
    transports: [
        new winston.transports.Console()
    ],
    exitOnError: true
}) : winston.createLogger({
    level: lambdaLogLevel,
    format: winston.format.combine(winstonInfoFormat(), winston.format.colorize(), winston.format.simple()),
    // format: winston.format.combine(winston.format.timestamp(), winstonInfoFormat(), winston.format.colorize(), winston.format.simple()),
    transports: [
        new winston.transports.Console()
    ],
    exitOnError: true
});

export {winstonLogger as logger};

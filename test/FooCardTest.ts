// @ts-nocheck
import {
    CellEffect,
    CommandFactory,
    IRoundHostPreset,
    IRoundProgressPreset,
    PlayerRoundProgress,
    PowerupId,
    RoundHost
} from '../src';
import cfg from '../test/cfg.json';
import _ from 'lodash';
import {hostOneRound} from '../simulation/hostOneRound';
import {logProgress} from '../simulation/utils';
import {findSameColors, tileMatching} from '../src/rules/tileMatching';

const powerup_quality_weight = {
    'powerUpsIdx': [0, 2, 4],
    'quality': [[{'quality': 1, 'weight': 30}, {
        'quality': 2,
        'weight': 50
    }, {'quality': 3, 'weight': 20}], [{
        'quality': 1,
        'weight': 10
    }, {'quality': 2, 'weight': 30}, {
        'quality': 3,
        'weight': 60
    }], [{'quality': 1, 'weight': 65}, {
        'quality': 2,
        'weight': 10
    }, {'quality': 3, 'weight': 25}]]
};
const regionQualityWeights = _.map(powerup_quality_weight.powerUpsIdx, (fromIndex, i, collection) => {
    return {
        fromIndex: fromIndex,
        toIndexExclusive: collection[i + 1] || Number.MAX_SAFE_INTEGER,
        weights: powerup_quality_weight.quality[i],
    };
});

const powerup_weight = [{'powerup': 1, 'weight': 50}, {
    'powerup': 2,
    'weight': 25
}, {'powerup': 3, 'weight': 25}, {'powerup': 4, 'weight': 50}, {
    'powerup': 5,
    'weight': 150
}, {'powerup': 6, 'weight': 50}, {'powerup': 7, 'weight': 50}];
const powerupWeights = _.map(powerup_weight, (cfg) => ({
    powerupId: cfg.powerup,
    weight: cfg.weight,
}));

const powerups_min_num = [{
    'weight': 50,
    'min_num': [{'powerup': 1, 'num': 1}, {
        'powerup': 2,
        'num': 0
    }, {'powerup': 3, 'num': 0}, {'powerup': 4, 'num': 1}, {
        'powerup': 5,
        'num': 0
    }, {'powerup': 6, 'num': 0}, {'powerup': 7, 'num': 1}]
}, {
    'weight': 50,
    'min_num': [{'powerup': 1, 'num': 1}, {
        'powerup': 2,
        'num': 0
    }, {'powerup': 3, 'num': 0}, {'powerup': 4, 'num': 1}, {
        'powerup': 5,
        'num': 0
    }, {'powerup': 6, 'num': 0}, {'powerup': 7, 'num': 1}]
}];
const minimumAmountRequirements = _.map(powerups_min_num, cfg => ({
    weight: cfg.weight,
    requirement: _.map(cfg.min_num, r => ({
        powerupId: r.powerup,
        amount: r.num,
    }))
}));

// _.values(__debug__.cfg.powerups).map(p => ({id: p.id, maxCountPerRound: p.round_max_used, effectCellCountPerCard: p.product_count, quality: p.quality}))
const powerupPresets = [{
    'id': 1,
    'maxCountPerRound': 100,
    'effectCellCountPerCard': 1,
    'quality': 1
}, {
    'id': 2,
    'maxCountPerRound': 100,
    'effectCellCountPerCard': 2,
    'quality': 1
}, {
    'id': 3,
    'maxCountPerRound': 100,
    'effectCellCountPerCard': 2,
    'quality': 1
}, {
    'id': 4,
    'maxCountPerRound': 100,
    'effectCellCountPerCard': 2,
    'quality': 2
}, {
    'id': 5,
    'maxCountPerRound': 1,
    'effectCellCountPerCard': 1,
    'quality': 2
}, {
    'id': 6,
    'maxCountPerRound': 1,
    'effectCellCountPerCard': 1,
    'quality': 3
}, {'id': 7, 'maxCountPerRound': 1, 'effectCellCountPerCard': 1, 'quality': 3}];

const powerup_click_weight = [[[{'powerup': 2, 'weight': 0.4}, {
    'powerup': 3,
    'weight': 0.4
}, {'powerup': 5, 'weight': 0.2}, {'powerup': 6, 'weight': 0.3}, {
    'powerup': 7,
    'weight': 0.9
}], [{'powerup': 2, 'weight': 0.4}, {
    'powerup': 3,
    'weight': 0.4
}, {'powerup': 5, 'weight': 0.2}, {'powerup': 6, 'weight': 0.3}, {
    'powerup': 7,
    'weight': 0.9
}], [{'powerup': 2, 'weight': 0.4}, {
    'powerup': 3,
    'weight': 0.4
}, {'powerup': 5, 'weight': 0.2}, {'powerup': 6, 'weight': 0.3}, {
    'powerup': 7,
    'weight': 0.9
}], [{'powerup': 2, 'weight': 0.4}, {
    'powerup': 3,
    'weight': 0.4
}, {'powerup': 5, 'weight': 0.2}, {'powerup': 6, 'weight': 0.3}, {
    'powerup': 7,
    'weight': 0.9
}]], [[{'powerup': 2, 'weight': 0.4}, {
    'powerup': 3,
    'weight': 0.4
}, {'powerup': 5, 'weight': 0.2}, {'powerup': 6, 'weight': 0.3}, {
    'powerup': 7,
    'weight': 0.9
}], [{'powerup': 2, 'weight': 0.4}, {
    'powerup': 3,
    'weight': 0.4
}, {'powerup': 5, 'weight': 0.2}, {'powerup': 6, 'weight': 0.3}, {
    'powerup': 7,
    'weight': 0.9
}], [{'powerup': 2, 'weight': 0.4}, {
    'powerup': 3,
    'weight': 0.4
}, {'powerup': 5, 'weight': 0.2}, {'powerup': 6, 'weight': 0.3}, {
    'powerup': 7,
    'weight': 0.9
}], [{'powerup': 2, 'weight': 0.4}, {
    'powerup': 3,
    'weight': 0.4
}, {'powerup': 5, 'weight': 0.2}, {'powerup': 6, 'weight': 0.3}, {
    'powerup': 7,
    'weight': 0.9
}]], [[{'powerup': 2, 'weight': 0.4}, {
    'powerup': 3,
    'weight': 0.4
}, {'powerup': 5, 'weight': 0.2}, {'powerup': 6, 'weight': 0.3}, {
    'powerup': 7,
    'weight': 0.9
}], [{'powerup': 2, 'weight': 0.4}, {
    'powerup': 3,
    'weight': 0.4
}, {'powerup': 5, 'weight': 0.2}, {'powerup': 6, 'weight': 0.3}, {
    'powerup': 7,
    'weight': 0.9
}], [{'powerup': 2, 'weight': 0.4}, {
    'powerup': 3,
    'weight': 0.4
}, {'powerup': 5, 'weight': 0.2}, {'powerup': 6, 'weight': 0.3}, {
    'powerup': 7,
    'weight': 0.9
}], [{'powerup': 2, 'weight': 0.4}, {
    'powerup': 3,
    'weight': 0.4
}, {'powerup': 5, 'weight': 0.2}, {'powerup': 6, 'weight': 0.3}, {
    'powerup': 7,
    'weight': 0.9
}]], [[{'powerup': 2, 'weight': 0.4}, {
    'powerup': 3,
    'weight': 0.4
}, {'powerup': 5, 'weight': 0.2}, {'powerup': 6, 'weight': 0.3}, {
    'powerup': 7,
    'weight': 0.9
}], [{'powerup': 2, 'weight': 0.4}, {
    'powerup': 3,
    'weight': 0.4
}, {'powerup': 5, 'weight': 0.2}, {'powerup': 6, 'weight': 0.3}, {
    'powerup': 7,
    'weight': 0.9
}], [{'powerup': 2, 'weight': 0.4}, {
    'powerup': 3,
    'weight': 0.4
}, {'powerup': 5, 'weight': 0.2}, {'powerup': 6, 'weight': 0.3}, {
    'powerup': 7,
    'weight': 0.9
}], [{'powerup': 2, 'weight': 0.4}, {
    'powerup': 3,
    'weight': 0.4
}, {'powerup': 5, 'weight': 0.2}, {'powerup': 6, 'weight': 0.3}, {
    'powerup': 7,
    'weight': 0.9
}]]];
const powerupEffectDaubableRates = _.map(powerup_click_weight, c => _.map(c, b => _.map(b, cfg => ({
    powerupId: cfg.powerup,
    rate: cfg.weight,
}))));

const powerup_extra_bingo = '0.04;0.08;0.11';
const powerupCausedExtraBingoRate: { [id: number]: number } = {};
_(powerup_extra_bingo)
    .split(';')
    .map((str, i, collection) => i === 0 ? _.toNumber(str) : _.toNumber(str) - _.toNumber(collection[i - 1]))
    .forEach((rate, index) => {
        if (index === 0) powerupCausedExtraBingoRate[PowerupId.DaubOnce] = rate;
        else if (index === 1) powerupCausedExtraBingoRate[PowerupId.DaubTwice] = rate;
        else powerupCausedExtraBingoRate[PowerupId.ForcedBingo] = rate;
    });


const hostPreset: IRoundHostPreset = {
    ruleId: 101,
    roundStartTime: 0,
    maxCallCount: 25,
    callInterval: 5e3,
    maxPlayerCount: 50,
    totalCardCountRange: {lower: 250, upper: 300},
    bingoCountRate: 0.15,
    callSeed: 200,
};

const progressPreset: IRoundProgressPreset = {
    joinRoundTime: 10e3,
    cardCountOptionIndex: 3,
    betOptionIndex: 0,
    cardPresets: [
        {
            seed: 19571,
            cellEffectInitializations: [{
                effect: [19, 1],
                count: 3,
                daubRate: 0.5
            }]
        },
        {seed: 15452, cellEffectInitializations: [{effect: [19, 2], count: 4}]},
        {seed: 10950},
        {seed: 10760},
    ],
    powerupBoxSeed: _.random(65535),
    powerupsCooldownTime: 15e3,
    daubCountToChargePowerups: 3,
    badBingoPenaltyTime: 10e3,
    cardCountOptions: [1, 2, 3, 4],
    betOptions: [10, 20, 30, 40],
    collectionRewardInitializationMap: cfg.shadow_card_modify,
    cellEffectInitializationMaps: [
        {effect: CellEffect.BoxReward, map: cfg.initial_box},
        {effect: CellEffect.TicketReward, map: cfg.initial_ticket}
    ],
    powerupPresets: powerupPresets,
    regionQualityWeights: regionQualityWeights,
    powerupWeights: powerupWeights,
    powerupMinimumAmountRequirements: minimumAmountRequirements,
    powerupEffectDaubableRateMap: powerupEffectDaubableRates,
    powerupCausedExtraBingoRate: powerupCausedExtraBingoRate as { [PowerupId.DaubOnce]: number, [PowerupId.DaubTwice]: number, [PowerupId.ForcedBingo]: number },
    powerupReplacements: [{originalId: 7, targetId: 4}],
    powerupCorrectionEndIndexByCardCountOptionIndex: [1, 2, 3, 3],
    extraBingoPowerupSwapIndexByCardCountOptionIndex: [2, 2, 3, 4],
    powerupBalances: {1: 1, 2: 0, 3: 3, 4: 4, 5: 5, 6: 6, 7: 0},
    powerupBalanceReplacements: {
        1: {beReplacedRate: 0.7, replaceWeight: 50},
        2: {beReplacedRate: 0.7, replaceWeight: 80},
        3: {beReplacedRate: 0.7, replaceWeight: 100},
        4: {beReplacedRate: 0.8, replaceWeight: 0},
        5: {beReplacedRate: 0.8, replaceWeight: 0},
        6: {beReplacedRate: 0.8, replaceWeight: 0},
        7: {beReplacedRate: 0.8, replaceWeight: 0},
    },
    doubleTicketPowerupNoBingoFactor: 0.1,
};

(async function () {

    const {logger} = require('../src/CoreUtils2');
    logger.init(require('./CoreLogger').logger);

    let ruleId = 221;

    //注意，确保种子每次跑出来的数据是一样的，否则就是BUG
    hostPreset.callSeed = 221000;

    hostPreset.ruleId = ruleId;
    hostPreset.maxCallCount = 70;
    hostPreset.maxPlayerCount = 1;

    // progressPreset.limitEffects = ['19_1', '20_1'];

    progressPreset.cardPresets = [
        {
            seed: 2100200657,
            cellEffectInitializations: [
                // {effect: [19, 1], count: 30, daubRate: 0.5},
                // {effect: [19, 1], count: 15, daubRate: 0.5},
                // {effect: [19, 1], count: 30},
                // {effect: [19, 1], count: 12},
                // {effect: [19, 1], count: 5},
                // {effect: [20, 1], count: 7},
            ]
        },
        // {
        //     seed: 15452, cellEffectInitializations: [
        //         {effect: [19, 1], count: 4},
        //         {effect: [20, 1], count: 7},
        //     ]
        // },
        // {
        //     seed: 10950,
        //     cellEffectInitializations: [{effect: [20, 1], count: 15}]
        // },
        // {seed: 10760},
    ];
    progressPreset.cardCountOptionIndex = _.findIndex(progressPreset.cardCountOptions, obj => {
        return obj === _.size(progressPreset.cardPresets);
    });

    // progressPreset.limitEffectsNum = 1;

    hostOneRound(hostPreset, [progressPreset], false);

})();




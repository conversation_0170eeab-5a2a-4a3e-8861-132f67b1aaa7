// @ts-nocheck

const _ = require('lodash');
const {logger, check2, error_TryBingo} = require('../src/CoreUtils2');

import _ from 'lodash';

describe('fooTest3Test', () => {
    // runs once before the first test in this block
    before(function () {
        this.timeout(0);
        let tmpLogger = require('./CoreLogger').logger;
        logger.init(tmpLogger);
    });
    it('check2Test', async function () {
        // require('../simulation/test');
        try {
            check2(false, error_TryBingo);
        } catch (e) {
            logger.error('check2Error', {e});
        }
    });
});

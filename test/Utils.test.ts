import * as Utils from '../src/Utils';
import _, { random } from 'lodash';
import assert from 'assert';
import { RandomSequence } from '../src';

describe('Utils', () => {
  it('pickRandomItemsWithMutation works properly', () => {
    const testingCount = 100000;
    const randomRange = 32;
    _(testingCount).range().forEach(() => {
      const items = _.range(_.random(randomRange));
      const itemLengthBefore = items.length;
      const pickCount = _.random(itemLengthBefore);

      const picked = Utils.pickRandomItemsWithMutation(items, pickCount);
      assert(picked.length === pickCount);
      assert(picked.indexOf(undefined as any) === -1);
      assert(items.length === itemLengthBefore - pickCount);
    });
  });

  it('can judge many times', () => {
    const testingCount = 1000;
    const randomRange = 32;

    _.range(testingCount).forEach(i => {
      const rs = new RandomSequence(_.random(65535));
      const n = _.random(randomRange);
      const result = Utils.judgeManyTimes(0.5, n, rs);

      assert(result.length === n);
    });
  });

  it('judgeManyTimes persistent result', () => {
    const seed = _.random(65535);
    const rs1 = new RandomSequence(seed);
    const rs2 = new RandomSequence(seed);
    const n1 = 255;
    const n2 = 512;
    const chance = _.random(true);

    const result1 = Utils.judgeManyTimes(chance, n1, rs1);
    const result2 = Utils.judgeManyTimes(chance, n2, rs2);

    _.forEach(result1, (r, i) => {
      assert(r === result2[i]);
    });
  });

  it('can resolve Smart Value', () => {
    let seed = 1976437082;
    const value = JSON.parse(`[{"effect_type": 19, "effect_value": 1, "count": {"expression": "[[{value: 0, weight: 20}, {value: 1, weight: 40}, {value: 2, weight: 40}], [{value: 1, weight: 60}, {value: 2, weight: 30}, {value: 3, weight: 10}], [{value: 1, weight: 60}, {value: 2, weight: 30}, {value: 3, weight: 10}], [{value: 1, weight: 60}, {value: 2, weight: 30}, {value: 3, weight: 10}]][bet_index]", "params": ["bet_index"]},"daub_rate":  {"expression": "[0.25, 0.3, 0.35, 0.4][bet_index]", "params": ["bet_index"]}}, {"effect_type": 19, "effect_value": 2, "count": {"expression": "[[{value: 0, weight: 95}, {value: 1, weight: 5}, {value: 2, weight: 0}], [{value: 0, weight: 75}, {value: 1, weight: 20}, {value: 2, weight: 5}], [{value: 0, weight: 61}, {value: 1, weight: 34}, {value: 2, weight: 5}], [{value: 0, weight: 53}, {value: 1, weight: 41}, {value: 2, weight: 6}]][bet_index]", "params": ["bet_index"]},"daub_rate":  {"expression": "[0.2, 0.2, 0.23, 0.26][bet_index]", "params": ["bet_index"]}}, {"effect_type": 19, "effect_value": 3, "count": {"expression": "[[{value: 0, weight: 95}, {value: 1, weight: 5}, {value: 2, weight: 0}], [{value: 0, weight: 95}, {value: 1, weight: 5}, {value: 2, weight: 0}], [{value: 0, weight: 90}, {value: 1, weight: 10}, {value: 2, weight: 0}], [{value: 0, weight: 85}, {value: 1, weight: 15}, {value: 2, weight: 0}]][bet_index]", "params": ["bet_index"]},"daub_rate":  {"expression": "[0.1, 0.1, 0.12, 0.15][bet_index]", "params": ["bet_index"]}}, {"effect_type": 19, "effect_value": 4, "count": {"expression": "[[{value: 0, weight: 100}, {value: 1, weight: 0}, {value: 2, weight: 0}], [{value: 0, weight: 100}, {value: 1, weight: 0}, {value: 2, weight: 0}], [{value: 0, weight: 97}, {value: 1, weight: 3}, {value: 2, weight: 0}], [{value: 0, weight: 94}, {value: 1, weight: 6}, {value: 2, weight: 0}]][bet_index]", "params": ["bet_index"]},"daub_rate":  {"expression": "[0.05, 0.05, 0.05, 0.05][bet_index]", "params": ["bet_index"]}}]`);
    const resolved1 = Utils.resolveSmartValue(value[0].daub_rate, () => 1, new RandomSequence(seed));
    assert(_.isNumber(resolved1));
    const resolved2 = Utils.resolveSmartValue(value[0].count, () => 1);
    assert(_.isNumber(resolved2));

    const stringValue = '[[{value: 2, weight: 60}, {value: 3, weight: 30}, {value: 4, weight: 10}], [{value: 0, weight: 60}, {value: 1, weight: ${intdd} + 3}, {value: 2, weight: 10}], [{value: 0, weight: 100}, {value: 1, weight: 0}, {value: 2, weight: 0}], [{value: 0, weight: 100}, {value: 1, weight: 0}, {value: 2, weight: 0}]][${card_index}]';
    const resolved3 = Utils.resolveSmartValue(stringValue, {
      bet_index: 0,
      card_index:0
    },new RandomSequence(seed));
    console.log(resolved3);
    assert(_.isNumber(resolved3));
  });
});

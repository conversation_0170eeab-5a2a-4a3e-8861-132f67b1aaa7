// @ts-nocheck
import fs from 'fs';
import path from 'path';
import PoolGenerator from '../generation/PoolGenerator';

Promise.resolve().then(async () => {
  const generator = new PoolGenerator({
    ruleId: 101,
    callSeed: 2333,
    cardSeedFrom: 10000,
    poolSize: 300,
    maxSeekCount: 1000000,
    bingoAtDistribution: [
      {before: 9, weight: 10},
      {before: 12, weight: 10},
      {before: 15, weight: 15},
      {before: 18, weight: 15},
      {before: 20, weight: 10},
      {before: 24, weight: 10},
      {before: 38, weight: 15},
      {before: 50, weight: 15}
    ],
    caughtRequirements: [{before: 25, count: 10}],
  });

  const succeed = generator.tryGenerate();
  const result = generator.getResult();
  fs.writeFileSync(path.resolve(__dirname, `rule_${101}_call_${2333}.json`), JSON.stringify(result));
});


import _ from 'lodash';
import assert from 'assert';
import {
  CellEffect, CommandFactory, IRoundHostPreset, IRoundProgressPreset, NaturalBingoFlag, PlayerRoundProgress, PowerupId, RoundHost
} from '../src';
import { hostOneRound } from '../simulation/hostOneRound';
import { getTestRoundProgressPreset } from '../simulation/utils';

const maxSeed = 2147483647;

const ruleIds = [101, 201, 202, 203, 204, 205];

const getHostPreset = (ruleId: number): IRoundHostPreset => ({
  ruleId: ruleId,
  roundStartTime: 0,
  maxCallCount: 25,
  callInterval: 5e3,
  maxPlayerCount: 50,
  totalCardCountRange: { lower: 250, upper: 300 },
  bingoCountRate: 0.15,
  callSeed: 200,
});

describe('Core', () => {
  it('can be initialized from preset.', () => {
    const hostPreset = getHostPreset(101);
    const progressPreset = getTestRoundProgressPreset();
    const host = new RoundHost(undefined, hostPreset);
    const progress = new PlayerRoundProgress(host, undefined, progressPreset);

    assert(progress);
  });

  it('can be restored from snapshot.', () => {
    _.forEach(ruleIds, ruleId => {
      const hostPreset = getHostPreset(ruleId);
      const progressPreset = getTestRoundProgressPreset();
      progressPreset.cardCountOptionIndex = 3;
      // TODO: pick from pools
      progressPreset.cardPresets = _.map([0, 1, 2, 3], () => ({ seed: _.random(maxSeed) }));
      progressPreset.powerupBoxSeed = _.random(maxSeed);

      const host = new RoundHost(undefined, hostPreset);
      const progress = new PlayerRoundProgress(host, undefined, progressPreset);

      const freshSnapshot = progress.getSnapshot();
      const restoredFromFresh = new PlayerRoundProgress(host, freshSnapshot);

      const freshEqual = _.isEqual(progress, restoredFromFresh);
      assert(freshEqual);

      // Play
      _.forEach(host.fullCallList, (callNumber, callIndex) => {
        const startTimeThisCall = (callIndex + 1) * hostPreset.callInterval;
        CommandFactory.receiveCall(startTimeThisCall).execute(progress);
        _.forEach(progress.cards, (card, cardIndex) => {
          if (card.isBingoed) return;
          const targetCell = card.cellDictionary[callNumber];
          const latestTimeThisCall = startTimeThisCall + hostPreset.callInterval;
          if (targetCell && !targetCell.isDaubed) {
            CommandFactory.tryDaub(latestTimeThisCall, cardIndex, targetCell.index).execute(progress);
            CommandFactory.tryUsePowerup(latestTimeThisCall).execute(progress);
          }
          if (card.canBingo) {
            CommandFactory.tryBingo(latestTimeThisCall, card.index).execute(progress);
          }
        });
      });

      const dirtySnapshot = progress.getSnapshot();
      const restoredFromDirty = new PlayerRoundProgress(host, dirtySnapshot);
      // debugger;

      const dirtyEqual = _.isEqual(progress, restoredFromDirty);
      assert(dirtyEqual);
    });
  });

  it('has farseer worked properly', () => {
    _.forEach(ruleIds, ruleId => {
      const hostPreset = getHostPreset(ruleId);
      const progressPreset = getTestRoundProgressPreset();
      progressPreset.cardCountOptionIndex = 3;
      // TODO: pick from pools
      progressPreset.cardPresets = _.map([0, 1, 2, 3], () => ({ seed: _.random(maxSeed) }));
      progressPreset.powerupBoxSeed = _.random(maxSeed);

      const host = new RoundHost(undefined, hostPreset);
      const progress = new PlayerRoundProgress(host, undefined, progressPreset);

      _.forEach(progress.cards, card => {
        assert(card.naturalBingo !== NaturalBingoFlag.Unknown, 'Should predict natural bingo.');
      });

      // Play without powerup usages
      _.forEach(host.fullCallList, (callNumber, callIndex) => {
        const startTimeThisCall = (callIndex + 1) * hostPreset.callInterval;
        CommandFactory.receiveCall(startTimeThisCall).execute(progress);
        _.forEach(progress.cards, (card, cardIndex) => {
          if (card.isBingoed) return;
          const targetCell = card.cellDictionary[callNumber];
          const latestTimeThisCall = startTimeThisCall + hostPreset.callInterval;
          if (targetCell && !targetCell.isDaubed) {
            CommandFactory.tryDaub(latestTimeThisCall, cardIndex, targetCell.index).execute(progress);
          }
          if (card.canBingo) {
            CommandFactory.tryBingo(latestTimeThisCall, card.index).execute(progress);
          }
        });
      });

      _.forEach(progress.cards, card => {
        if (card.naturalBingo === NaturalBingoFlag.Yes) assert(card.canBingo && card.isBingoed);
        else if (card.naturalBingo === NaturalBingoFlag.No) assert(!card.canBingo && !card.isBingoed);
      });

      // TODO: daub and test bingo

    });
  });
});
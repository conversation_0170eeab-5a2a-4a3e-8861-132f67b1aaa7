import _ from 'lodash';
import assert from 'assert';
import chalk from 'chalk';
import CardSeedGenerator from '../src/CardSeedGenerator';

describe('Card Generation', () => {

  const ruleIds = [101, 201, 202, 203, 204, 205];

  it('generates card seed records', () => {
    const generator = new CardSeedGenerator(0, ruleIds[0], 0);

    const generationCount = 1000;
    let naturalBingoCount = 0;
    let powerupBingoCount = 0;
    let noBingoCount = 0;
    _(generationCount).range().forEach((i) => {
      const record = generator.yieldOne();
      if (record.bingoAt !== -1) {
        naturalBingoCount += 1;
      } else if (record.requiredDaubCountToBingo !== -1) {
        powerupBingoCount += 1;
      } else {
        noBingoCount += 1;
      }

      console.log(chalk.inverse(` #${i} `));
      console.table(record);
    });

    console.log(chalk.inverse(' Natural Bingo ') + chalk.green(` ${naturalBingoCount} / ${generationCount}\n`));
    console.log(chalk.inverse(' Powerup Bingo ') + chalk.yellow(` ${powerupBingoCount} / ${generationCount}\n`));
    console.log(chalk.inverse(' no Bingo ') +  chalk.red(` ${noBingoCount} / ${generationCount}\n`));
  });
});

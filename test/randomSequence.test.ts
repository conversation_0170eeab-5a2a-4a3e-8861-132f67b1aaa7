import _ from 'lodash';
import assert from 'assert';
import { RandomSequence } from '../src/RandomSequence';

describe('RandomSequence', () => {
  const n = 1000000;
  const int32MaxValue = 2147483647;
  const int32MinValue = -2147483648;
  const int32Range = int32MaxValue - int32MinValue;

  const newDistribution = () => _.fill(Array(100), 0);
  const getIndex = (ratio: number) => _.toInteger(ratio * 100);
  const logDistribution = (distribution: number[]) => {
    console.log('=== distribution ===');
    console.table(distribution);
  };

  it('can initialized by a string seed', () => {
    const rs = new RandomSequence('foobar');

    assert(_.isInteger(rs.nextInt32()));
    assert(_.isNumber(rs.nextSingle()));
    assert(_.isNumber(rs.nextDouble()));
    assert(_.isInteger(rs.nextIntegerInRange(_.random(65535))));
  });

  it('can be restored by a state', () => {
    const rs = new RandomSequence('by_a_state_233');

    const times = _.random(16, 32);
    for (let i = 0; i < times; i += 1) {
      rs.nextInt32();
    }

    const state = rs.getState();

    const int32 = rs.nextInt32();
    const single = rs.nextSingle();
    const double = rs.nextDouble();
    const range = 65535;
    const inRange = rs.nextIntegerInRange(range);

    const newRs = new RandomSequence(state);

    assert(newRs.nextInt32() === int32);
    assert(newRs.nextSingle() === single);
    assert(newRs.nextDouble() === double);
    assert(newRs.nextIntegerInRange(range) === inRange);
  });

  it('generate random int32', () => {
    const seed = _.random(int32MaxValue);
    const rs = new RandomSequence(seed);
    const distribution = newDistribution();

    for (let i = 0; i < n; i += 1) {
      const num = rs.nextInt32();
      assert(num >= int32MinValue && num <= int32MaxValue);

      const di = getIndex((num - int32MinValue) / int32Range);
      distribution[di] += 1;
    }

    logDistribution(distribution);
  });

  it('generate random single', () => {
    const seed = _.random(int32MaxValue);
    const rs = new RandomSequence(seed);
    const distribution = newDistribution();

    for (let i = 0; i < n; i += 1) {
      const num = rs.nextSingle();
      assert(num >= 0 && num < 1);

      const di = getIndex(num);
      distribution[di] += 1;
    }

    logDistribution(distribution);
  });

  it('generate random double', () => {
    const seed = _.random(int32MaxValue);
    const rs = new RandomSequence(seed);
    const distribution = newDistribution();

    for (let i = 0; i < n; i += 1) {
      const num = rs.nextDouble();
      assert(num >= 0 && num < 1);

      const di = getIndex(num);
      distribution[di] += 1;
    }

    logDistribution(distribution);
  });

  it('generate integers in range', () => {
    const seed = _.random(int32MaxValue);
    const rs = new RandomSequence(seed);
    const distribution = newDistribution();

    for (let i = 0; i < n; i += 1) {
      const num1 = _.random(int32MinValue, int32MaxValue);
      const num2 = _.random(int32MinValue, int32MaxValue);
      const min = _.min([num1, num2]) as number;
      const max = _.max([num1, num2]) as number;

      const num = rs.nextIntegerInRange(max, min);

      assert(num >= min && num <= max);

      const ratio = (num - min) / (max - min + 1);
      const di = getIndex(ratio);
      distribution[di] += 1;
    }

    logDistribution(distribution);
  });

  it('generate floats in range', () => {
    const seed = _.random(int32MaxValue);
    const rs = new RandomSequence(seed);
    const distribution = newDistribution();

    for (let i = 0; i < n; i += 1) {
      const num1 = _.random(int32MinValue, int32MaxValue);
      const num2 = _.random(int32MinValue, int32MaxValue);
      const min = _.min([num1, num2]) as number;
      const max = _.max([num1, num2]) as number;

      const num = rs.nextFloatInRange(min, max);

      assert(num >= min && num <= max);

      const ratio = (num - min) / (max - min + 1);
      const di = getIndex(ratio);
      distribution[di] += 1;
    }

    logDistribution(distribution);
  });
});

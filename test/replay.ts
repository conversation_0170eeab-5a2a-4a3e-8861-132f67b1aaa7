import _ from 'lodash';
import { logProgress } from '../simulation/utils';
import { IRoundHostSnapshot, IRoundProgressSnapshot, PlayerRoundProgress, RoundHost } from '../src';
import assert from 'assert';
import PlayerCareer from '../simulation/PlayerCareer';
import { CommandType, getCommandHistoryFromSnapshot } from '../src/commands/IRoundCommand';

const roomId = 1;
// const callSeed = 68;

const hostSnapshotJson = `
{"roundStartTime":1641534615424,"maxPlayerCount":50,"bingoHappenedCount":0,"playerCount":0,"cardCount":0,"ruleId":101,"callInterval":5000,"remainingBingoCount":44,"fullCallList":[16,34,75,42,10,59,32,23,53,14,38,7,60,20,9,55,74,24,44,39,43,13]}
 
`;

const initialProgressSnapshotJson = `
{"betIndex":0,"joinTime":0,"penalty":10000,"receivedCallIndex":-1,"cards":[{"seed":694262303,"effects":[[1,0]],"cellValues":[12,9,14,13,6,26,20,22,23,28,34,31,33,38,36,50,54,49,59,55,63,72,75,74,71],"cellDaubed":[0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0],"cellEffects":[[],[[3,0]],[],[],[],[],[],[[3,0]],[],[],[],[],[[17,0]],[],[],[],[],[[19,1]],[],[],[],[],[],[],[]],"farseer":[2,-1,-1,-1],"canBingo":false,"isBingoed":false,"lastTry":0},{"seed":1971996772,"effects":[],"cellValues":[13,9,3,14,7,24,29,26,20,21,42,41,38,43,45,46,59,53,52,51,74,75,73,62,67],"cellDaubed":[0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0],"cellEffects":[[],[],[],[],[],[[3,0]],[[3,0]],[],[],[[19,1]],[],[[19,3]],[[17,0]],[],[],[],[],[],[[19,1]],[],[],[],[],[],[]],"farseer":[2,-1,-1,-1],"canBingo":false,"isBingoed":false,"lastTry":0},{"seed":1860104036,"effects":[],"cellValues":[2,9,13,12,5,16,22,21,24,27,38,34,33,42,40,48,55,53,47,60,68,74,65,69,73],"cellDaubed":[0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0],"cellEffects":[[[19,1]],[],[],[],[],[],[],[],[],[],[],[],[[17,0]],[],[],[],[[2,0]],[],[],[],[],[],[],[[19,1]],[]],"farseer":[1,14,15,18],"canBingo":false,"isBingoed":false,"lastTry":0},{"seed":1721331014,"effects":[],"cellValues":[4,2,11,14,15,30,26,19,23,22,42,33,36,40,38,53,59,55,52,51,67,75,73,68,66],"cellDaubed":[0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0],"cellEffects":[[],[],[],[],[],[[19,1]],[[3,0]],[],[],[],[[19,1]],[[3,0]],[[17,0]],[],[],[],[],[],[],[],[],[],[],[],[]],"farseer":[1,-1,11,13],"canBingo":false,"isBingoed":false,"lastTry":0}],"ranks":[],"commands":[],"powerupBox":{"seed":5749,"usages":[{"powerupId":7,"daubable":[[1],[1],[1],[1]]},{"powerupId":1},{"powerupId":4},{"powerupId":1},{"powerupId":3,"daubable":[[1,0],[1,1],[1,1],[0,1]]},{"powerupId":2,"daubable":[[0,1],[0,0],[0,0],[0,0]]},{"powerupId":5,"daubable":[[1],[0],[0],[0]]},{"powerupId":4},{"powerupId":3,"daubable":[[0,0],[0,0],[1,0],[0,1]]},{"powerupId":2,"daubable":[[0,0],[0,0],[0,0],[0,0]]},{"powerupId":4},{"powerupId":1},{"powerupId":3,"daubable":[[0,1],[0,1],[0,1],[0,1]]},{"powerupId":4},{"powerupId":3,"daubable":[[0,0],[0,0],[1,1],[1,1]]},{"powerupId":4},{"powerupId":2,"daubable":[[0,1],[1,0],[0,0],[0,0]]},{"powerupId":3,"daubable":[[0,0],[1,0],[0,1],[1,1]]},{"powerupId":2,"daubable":[[0,0],[0,0],[0,0],[0,0]]},{"powerupId":3,"daubable":[[0,1],[0,0],[1,0],[1,1]]},{"powerupId":4},{"powerupId":2,"daubable":[[1,0],[0,1],[1,1],[0,0]]},{"powerupId":1},{"powerupId":2,"daubable":[[0,0],[0,0],[0,0],[0,0]]},{"powerupId":3,"daubable":[[1,1],[0,0],[0,1],[0,0]]}],"index":0,"charge":0,"toCharge":3,"lastTime":-15000,"cd":15000,"fates":[4,1,1,1]},"pDaub":0}

`;

const progressSnapshotJson = `
{"betIndex":0,"joinTime":0,"penalty":10000,"receivedCallIndex":9,"cards":[{"seed":694262303,"effects":[[1,0]],"cellValues":[12,9,14,13,6,26,20,22,23,28,34,31,33,38,36,50,54,49,59,55,63,72,75,74,71],"cellDaubed":[0,0,0,0,0,0,0,0,1,0,1,0,1,0,0,0,0,0,1,1,0,0,1,0,0],"cellEffects":[[],[[3,0]],[],[],[],[],[],[[3,0]],[],[],[],[],[[17,0]],[],[],[],[],[[19,1]],[],[[6,0],[1,0]],[],[],[],[],[]],"farseer":[2,-1,-1,-1],"canBingo":true,"isBingoed":true,"lastTry":45552},{"seed":1971996772,"effects":[],"cellValues":[13,9,3,14,7,24,29,26,20,21,42,41,38,43,45,46,59,53,52,51,74,75,73,62,67],"cellDaubed":[0,0,0,0,0,0,0,0,0,0,1,0,1,0,0,0,1,0,0,0,0,1,0,0,0],"cellEffects":[[],[],[],[],[],[[3,0]],[[3,0]],[],[],[[19,1]],[],[[19,3]],[[17,0]],[],[],[],[[6,0]],[],[[19,1]],[],[],[],[],[],[]],"farseer":[2,-1,-1,-1],"canBingo":true,"isBingoed":true,"lastTry":30036},{"seed":1860104036,"effects":[],"cellValues":[2,9,13,12,5,16,22,21,24,27,38,34,33,42,40,48,55,53,47,60,68,74,65,69,73],"cellDaubed":[0,0,0,0,0,1,0,0,0,0,0,1,1,1,0,0,1,1,0,0,0,0,0,0,0],"cellEffects":[[[19,1]],[],[],[],[],[],[],[],[],[],[],[],[[17,0]],[],[],[],[[2,0],[1,0]],[],[],[],[],[],[],[[19,1]],[[6,0]]],"farseer":[1,14,15,18],"canBingo":false,"isBingoed":false,"lastTry":0},{"seed":1721331014,"effects":[],"cellValues":[4,2,11,14,15,30,26,19,23,22,42,33,36,40,38,53,59,55,52,51,67,75,73,68,66],"cellDaubed":[0,0,0,1,0,0,0,0,1,0,1,0,1,0,0,1,1,0,0,0,0,1,0,0,0],"cellEffects":[[],[],[],[[1,0]],[],[[19,1]],[[3,0]],[],[],[],[[19,1]],[[3,0]],[[17,0]],[],[],[],[],[],[],[],[[6,0]],[],[],[],[]],"farseer":[1,-1,11,13],"canBingo":false,"isBingoed":false,"lastTry":0}],"ranks":[1,1],"commands":[{"type":1,"time":18553,"payload":null},{"type":1,"time":20002,"payload":null},{"type":2,"time":21352,"payload":{"cardIndex":0,"cellIndex":10}},{"type":2,"time":21352,"payload":{"cardIndex":0,"cellIndex":22}},{"type":2,"time":21352,"payload":{"cardIndex":1,"cellIndex":10}},{"type":2,"time":21352,"payload":{"cardIndex":1,"cellIndex":21}},{"type":2,"time":21352,"payload":{"cardIndex":2,"cellIndex":5}},{"type":2,"time":21352,"payload":{"cardIndex":2,"cellIndex":11}},{"type":2,"time":21352,"payload":{"cardIndex":2,"cellIndex":13}},{"type":2,"time":21352,"payload":{"cardIndex":3,"cellIndex":10}},{"type":2,"time":21352,"payload":{"cardIndex":3,"cellIndex":21}},{"type":4,"time":21869,"payload":null},{"type":1,"time":25002,"payload":null},{"type":1,"time":30002,"payload":null},{"type":2,"time":30002,"payload":{"cardIndex":0,"cellIndex":18}},{"type":2,"time":30002,"payload":{"cardIndex":1,"cellIndex":16}},{"type":2,"time":30002,"payload":{"cardIndex":3,"cellIndex":16}},{"type":3,"time":30036,"payload":1},{"type":1,"time":35002,"payload":null},{"type":1,"time":40002,"payload":null},{"type":2,"time":40002,"payload":{"cardIndex":3,"cellIndex":8}},{"type":2,"time":40002,"payload":{"cardIndex":0,"cellIndex":8}},{"type":1,"time":45002,"payload":null},{"type":2,"time":45002,"payload":{"cardIndex":2,"cellIndex":17}},{"type":2,"time":45002,"payload":{"cardIndex":3,"cellIndex":15}},{"type":4,"time":45519,"payload":null},{"type":3,"time":45552,"payload":0},{"type":1,"time":50002,"payload":null}],"powerupBox":{"seed":5749,"usages":[{"powerupId":7,"daubable":[[1],[1],[1],[1]]},{"powerupId":1},{"powerupId":4},{"powerupId":1},{"powerupId":3,"daubable":[[1,0],[1,1],[1,1],[0,1]]},{"powerupId":2,"daubable":[[0,1],[0,0],[0,0],[0,0]]},{"powerupId":5,"daubable":[[1],[0],[0],[0]]},{"powerupId":4},{"powerupId":3,"daubable":[[0,0],[0,0],[1,0],[0,1]]},{"powerupId":2,"daubable":[[0,0],[0,0],[0,0],[0,0]]},{"powerupId":4},{"powerupId":1},{"powerupId":3,"daubable":[[0,1],[0,1],[0,1],[0,1]]},{"powerupId":4},{"powerupId":3,"daubable":[[0,0],[0,0],[1,1],[1,1]]},{"powerupId":4},{"powerupId":2,"daubable":[[0,1],[1,0],[0,0],[0,0]]},{"powerupId":3,"daubable":[[0,0],[1,0],[0,1],[1,1]]},{"powerupId":2,"daubable":[[0,0],[0,0],[0,0],[0,0]]},{"powerupId":3,"daubable":[[0,1],[0,0],[1,0],[1,1]]},{"powerupId":4},{"powerupId":2,"daubable":[[1,0],[0,1],[1,1],[0,0]]},{"powerupId":1},{"powerupId":2,"daubable":[[0,0],[0,0],[0,0],[0,0]]},{"powerupId":3,"daubable":[[1,1],[0,0],[0,1],[0,0]]}],"index":2,"charge":0,"toCharge":3,"lastTime":45519,"cd":15000,"fates":[4,1,1,1]},"pDaub":16}

`;


Promise.resolve().then(async () => {
  const career = new PlayerCareer({ betOption: 0, cardCount: 4, initialTicketCount: 100, maxRoundToPlay: 1, roomId: 1 });
  await career.init();

  const hostSnapshot = JSON.parse(hostSnapshotJson) as IRoundHostSnapshot;
  // const hostPreset = career.getHostPresetFromSnapshot(roomId, callSeed, hostSnapshot);
  const host = new RoundHost(hostSnapshot);

  const initialProgressSnapshot = JSON.parse(initialProgressSnapshotJson) as IRoundProgressSnapshot;
  const progressSnapshot = JSON.parse(progressSnapshotJson) as IRoundProgressSnapshot;

  const initialProgress = new PlayerRoundProgress(host, initialProgressSnapshot);
  // logProgress(initialProgress);

  // const progressPreset = career.getProgressPresetFromSnapshot(roomId, progressSnapshot);
  // const recoveredProgress = new PlayerRoundProgress(host, undefined, progressPreset);

  const progress = new PlayerRoundProgress(host, progressSnapshot);

  const commands = getCommandHistoryFromSnapshot(progressSnapshot.commands);
  logProgress(progress);

  debugger

  _.forEach(commands, command => {
    command.execute(initialProgress);

    const restoredSnapshot = initialProgress.getSnapshot();

    if (command.type !== CommandType.ReceiveCall) {
      logProgress(initialProgress);
      debugger
    }
  });

  logProgress(initialProgress);
});

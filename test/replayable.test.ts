// import assert from 'assert';

// import {
//   IRoundPreset, IRoundProgressPreset, PowerupId
// } from '../src';
// import cfg from './cfg.json';
// import _ from 'lodash';
// import { hostOneRound } from '../simulation/hostOneRound';

// const powerup_quality_weight = { "powerUpsIdx": [0, 2, 4], "quality": [[{ "quality": 1, "weight": 30 }, { "quality": 2, "weight": 50 }, { "quality": 3, "weight": 20 }], [{ "quality": 1, "weight": 10 }, { "quality": 2, "weight": 30 }, { "quality": 3, "weight": 60 }], [{ "quality": 1, "weight": 65 }, { "quality": 2, "weight": 10 }, { "quality": 3, "weight": 25 }]] };
// const regionQualityWeights = _.map(powerup_quality_weight.powerUpsIdx, (fromIndex, i, collection) => {
//   return {
//     fromIndex: fromIndex,
//     toIndexExclusive: collection[i + 1] || Number.MAX_SAFE_INTEGER,
//     weights: powerup_quality_weight.quality[i],
//   };
// });

// const powerup_weight = [{ "powerup": 1, "weight": 50 }, { "powerup": 2, "weight": 25 }, { "powerup": 3, "weight": 25 }, { "powerup": 4, "weight": 50 }, { "powerup": 5, "weight": 150 }, { "powerup": 6, "weight": 50 }, { "powerup": 7, "weight": 50 }];
// const powerupWeights = _.map(powerup_weight, (cfg) => ({
//   powerupId: cfg.powerup,
//   weight: cfg.weight,
// }));

// const powerups_min_num = [{ "powerup": 1, "num": 1 }, { "powerup": 2, "num": 0 }, { "powerup": 3, "num": 0 }, { "powerup": 4, "num": 1 }, { "powerup": 5, "num": 0 }, { "powerup": 6, "num": 0 }, { "powerup": 7, "num": 1 }];
// const minimumAmountRequirements = _.map(powerups_min_num, (cfg) => ({
//   powerupId: cfg.powerup,
//   amount: cfg.num,
// }));

// // _.values(__debug__.cfg.powerups).map(p => ({id: p.id, maxCountPerRound: p.round_max_used, effectCellCountPerCard: p.product_count, quality: p.quality}))
// const powerupPresets = [{ "id": 1, "maxCountPerRound": 100, "effectCellCountPerCard": 1, "quality": 1 }, { "id": 2, "maxCountPerRound": 100, "effectCellCountPerCard": 2, "quality": 1 }, { "id": 3, "maxCountPerRound": 100, "effectCellCountPerCard": 2, "quality": 1 }, { "id": 4, "maxCountPerRound": 100, "effectCellCountPerCard": 2, "quality": 2 }, { "id": 5, "maxCountPerRound": 1, "effectCellCountPerCard": 1, "quality": 2 }, { "id": 6, "maxCountPerRound": 1, "effectCellCountPerCard": 1, "quality": 3 }, { "id": 7, "maxCountPerRound": 1, "effectCellCountPerCard": 1, "quality": 3 }];

// const powerup_click_weight = [{ "powerup": 2, "weight": 0.4 }, { "powerup": 3, "weight": 0.4 }, { "powerup": 5, "weight": 0.65 }, { "powerup": 6, "weight": 0.65 }, { "powerup": 7, "weight": 0.15 }];
// const powerupEffectDaubableRates = _.map(powerup_click_weight, (cfg) => ({
//   powerupId: cfg.powerup,
//   rate: cfg.weight,
// }));

// const powerup_extra_bingo = '0.04;0.08;0.11';
// const powerupCausedExtraBingoRate: { [id: number]: number } = {};
// _(powerup_extra_bingo)
//   .split(';')
//   .map((str, i, collection) => i === 0 ? _.toNumber(str) : _.toNumber(str) - _.toNumber(collection[i - 1]))
//   .forEach((rate, index) => {
//     if (index === 0) powerupCausedExtraBingoRate[PowerupId.DaubOnce] = rate;
//     else if (index === 1) powerupCausedExtraBingoRate[PowerupId.DaubTwice] = rate;
//     else powerupCausedExtraBingoRate[PowerupId.ForcedBingo] = rate;
//   });


// const hostPreset: IRoundPreset = {
//   ruleId: 101,
//   roundStartTime: 0,
//   maxCallCount: 25,
//   callInterval: 5e3,
//   maxPlayerCount: 50,
//   totalCardCountRange: { lower: 250, upper: 300 },
//   bingoCountRate: 0.15,
//   powerupsCooldownTime: 15e3,
//   daubCountToChargePowerups: 3,
//   badBingoPenaltyTime: 10e3,
//   cardCountOptions: [1, 2, 3, 4],
//   betOptions: [10, 20, 30, 40],
//   callSeed: 54655735,
//   collectionRewardInitializationMap: cfg.shadow_card_modify,
//   boxRewardInitializationMap: cfg.initial_box,
//   ticketRewardInitializationMap: cfg.initial_ticket,
//   powerupPresets: powerupPresets,
//   regionQualityWeights: regionQualityWeights,
//   powerupWeights: powerupWeights,
//   powerupMinimumAmountRequirements: minimumAmountRequirements,
//   powerupEffectDaubableRates: powerupEffectDaubableRates,
//   powerupCausedExtraBingoRate: powerupCausedExtraBingoRate as { [PowerupId.DaubOnce]: number, [PowerupId.DaubTwice]: number, [PowerupId.ForcedBingo]: number },
//   powerupReplacements: [{ originalId: 7, targetId: 4 }]
// };

// // const host = new RoundHost(rules.common, hostPreset);

// const progressPreset: IRoundProgressPreset = {
//   cardCountOptionIndex: 3,
//   betOptionIndex: 0,
//   cardPresets: [
//     { seed: 1858935220, requiredDaubCountToBingo: 0 },
//     { seed: 1664426839, requiredDaubCountToBingo: 2, cellIndexesLeadToBingo: [[6, 8], [2, 22]] },
//     { seed: 2410134039, requiredDaubCountToBingo: 1, cellIndexesLeadToBingo: [[2], [18, 19], [4, 20]] },
//     { seed: 3177480064, requiredDaubCountToBingo: 0 },
//   ],
//   hostPreset,
//   powerupBoxPreset: {
//     powerupBoxSeed: _.random(65535)
//   }
// };

// // const presets = _.map(_.range(50), () => _.clone(progressPreset));

// const ruleIds = [101, 201, 202, 203, 204, 205];
// _.forEach(ruleIds, ruleId => {
//   hostPreset.ruleId = ruleId;
//   hostOneRound(hostPreset, [progressPreset], true);
// });

// // hostOneRound(hostPreset, [progressPreset], true);




// // describe('Replayable Testing', () => {

// //   it('can replay via snapshots', () => {
    
// //   });
// // });

